#!/usr/bin/env python3
"""
🐺 Wild Weasel Payload Fixer & Migrator
=============================================================================
Mission: Fix payload issues and ensure robust API uploads

This script will:
1. Fix the empty ID fields in payloads
2. Validate all payload structures
3. Test API connectivity with sample data
4. Provide a simple migration interface
"""

import json
import os
import sys
import requests
import logging
from datetime import datetime
from typing import Dict, List, Any, Optional

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("/Users/<USER>/Desktop/wild_weasel_gainsight_migration/payload_fixer.log"),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger("WildWeasel-PayloadFixer")

class PayloadFixer:
    """Fix and validate payloads for Gainsight API"""
    
    def __init__(self):
        self.config = {
            "payload_file": "/Users/<USER>/Desktop/wild_weasel_gainsight_migration/gainsight_api_payload_email_activities.json",
            "fixed_payload_file": "/Users/<USER>/Desktop/wild_weasel_gainsight_migration/gainsight_api_payload_email_activities_FIXED.json",
            "drafts_api_url": "https://demo-emea1.gainsightcloud.com/v1/ant/v2/activity/drafts",
            "activity_api_url": "https://demo-emea1.gainsightcloud.com/v1/ant/v2/activity"
        }
        
        # Set your session cookies here after logging into Gainsight
        self.session_cookies = {
            # Add your cookies here - get them from browser dev tools after login
            # Example:
            # "sessionid": "your_session_id_value",
            # "csrftoken": "your_csrf_token",
            # "authtoken": "your_auth_token"
        }
    
    def load_payloads(self) -> List[Dict[str, Any]]:
        """Load existing payloads"""
        try:
            with open(self.config['payload_file'], 'r') as f:
                payloads = json.load(f)
            logger.info(f"📊 Loaded {len(payloads)} payloads")
            return payloads
        except Exception as e:
            logger.error(f"❌ Failed to load payloads: {e}")
            return []
    
    def analyze_payload_issues(self, payloads: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Analyze issues in current payloads"""
        analysis = {
            "total_payloads": len(payloads),
            "empty_ids": 0,
            "missing_required_fields": [],
            "validation_errors": [],
            "structure_issues": [],
            "sample_payload": None
        }
        
        required_fields = ['note', 'meta', 'author', 'contexts', 'id']
        
        for i, payload in enumerate(payloads):
            # Check for empty ID (main issue)
            if payload.get('id') == "":
                analysis["empty_ids"] += 1
            
            # Check required fields
            for field in required_fields:
                if field not in payload:
                    analysis["missing_required_fields"].append(f"Payload {i}: missing {field}")
            
            # Check note structure
            if 'note' in payload:
                note = payload['note']
                if 'type' not in note or note.get('type') != 'EMAIL':
                    analysis["structure_issues"].append(f"Payload {i}: note.type should be 'EMAIL'")
                if 'subject' not in note:
                    analysis["structure_issues"].append(f"Payload {i}: missing note.subject")
            
            # Check contexts
            if 'contexts' in payload:
                contexts = payload['contexts']
                if not isinstance(contexts, list) or len(contexts) == 0:
                    analysis["structure_issues"].append(f"Payload {i}: contexts should be non-empty list")
                elif 'id' not in contexts[0]:
                    analysis["structure_issues"].append(f"Payload {i}: missing company ID in contexts")
        
        # Save first payload as sample
        if payloads:
            analysis["sample_payload"] = payloads[0]
        
        return analysis
    
    def fix_payload_issues(self, payloads: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Fix common issues in payloads"""
        fixed_payloads = []
        fixes_applied = {
            "empty_id_placeholders": 0,
            "missing_note_fields": 0,
            "structure_corrections": 0
        }
        
        for i, payload in enumerate(payloads):
            fixed_payload = payload.copy()
            
            # Fix 1: Empty ID - add placeholder that will be replaced during migration
            if fixed_payload.get('id') == "":
                fixed_payload['id'] = "WILL_BE_REPLACED_WITH_DRAFT_ID"
                fixes_applied["empty_id_placeholders"] += 1
            
            # Fix 2: Ensure note has required fields
            if 'note' in fixed_payload:
                note = fixed_payload['note']
                if 'type' not in note:
                    note['type'] = 'EMAIL'
                    fixes_applied["missing_note_fields"] += 1
                if 'subject' not in note:
                    note['subject'] = f'Migrated Email Activity {i+1}'
                    fixes_applied["missing_note_fields"] += 1
            else:
                # Add minimal note structure
                fixed_payload['note'] = {
                    'type': 'EMAIL',
                    'subject': f'Migrated Email Activity {i+1}',
                    'content': 'Migrated from Totango',
                    'plainText': 'Migrated from Totango'
                }
                fixes_applied["structure_corrections"] += 1
            
            # Fix 3: Ensure contexts exist
            if 'contexts' not in fixed_payload or not fixed_payload['contexts']:
                fixed_payload['contexts'] = [{
                    "id": "1P02IPMAEL4M3CQGY4K5FHU22D2HHT11KCKU",  # ICICI Bank ID
                    "obj": "Company",
                    "lbl": "ICICI",
                    "dsp": True,
                    "base": True
                }]
                fixes_applied["structure_corrections"] += 1
            
            # Fix 4: Ensure meta exists
            if 'meta' not in fixed_payload:
                fixed_payload['meta'] = {
                    "activityTypeId": "c81eeccf-2aa1-45bc-be71-5377f148e1e9",
                    "source": "C360",
                    "hasTask": False,
                    "emailSent": False,
                    "systemType": "GAINSIGHT"
                }
                fixes_applied["structure_corrections"] += 1
            
            # Fix 5: Ensure author exists
            if 'author' not in fixed_payload:
                fixed_payload['author'] = {
                    "id": "1P01E316G9DAPFOLE6V9Z586JRYFUW88XLGG",
                    "name": "Migration User",
                    "email": "<EMAIL>"
                }
                fixes_applied["structure_corrections"] += 1
            
            fixed_payloads.append(fixed_payload)
        
        logger.info(f"🔧 Applied fixes:")
        logger.info(f"   ID placeholders: {fixes_applied['empty_id_placeholders']}")
        logger.info(f"   Note fields: {fixes_applied['missing_note_fields']}")
        logger.info(f"   Structure corrections: {fixes_applied['structure_corrections']}")
        
        return fixed_payloads
    
    def save_fixed_payloads(self, fixed_payloads: List[Dict[str, Any]]):
        """Save fixed payloads to new file"""
        try:
            with open(self.config['fixed_payload_file'], 'w') as f:
                json.dump(fixed_payloads, f, indent=2)
            logger.info(f"💾 Saved {len(fixed_payloads)} fixed payloads to {self.config['fixed_payload_file']}")
        except Exception as e:
            logger.error(f"❌ Failed to save fixed payloads: {e}")
    
    def test_single_migration(self, payload: Dict[str, Any]) -> Dict[str, Any]:
        """Test migration of a single payload"""
        result = {
            "draft_creation": {"success": False, "draft_id": None, "error": None},
            "activity_creation": {"success": False, "error": None},
            "overall_success": False
        }
        
        if not self.session_cookies:
            result["draft_creation"]["error"] = "No session cookies provided"
            result["activity_creation"]["error"] = "No session cookies provided"
            return result
        
        # Build headers
        cookie_string = "; ".join([f"{k}={v}" for k, v in self.session_cookies.items()])
        headers = {
            "Content-Type": "application/json",
            "Cookie": cookie_string,
            "User-Agent": "Wild-Weasel-PayloadFixer/1.0",
            "Accept": "application/json",
            "X-Requested-With": "XMLHttpRequest",
            "Origin": "https://demo-emea1.gainsightcloud.com",
            "Referer": "https://demo-emea1.gainsightcloud.com/v1/ui/customersuccess360"
        }
        
        try:
            # Step 1: Create draft
            note = payload.get('note', {})
            contexts = payload.get('contexts', [])
            
            if not contexts:
                result["draft_creation"]["error"] = "No contexts found"
                return result
            
            company_id = contexts[0].get('id')
            subject = note.get('subject', 'Test Migration')
            
            draft_payload = {
                "type": "EMAIL",
                "subject": subject[:255],
                "companyId": company_id
            }
            
            logger.info(f"🧪 Testing draft creation for: {subject[:50]}...")
            
            draft_response = requests.post(
                self.config['drafts_api_url'],
                json=draft_payload,
                headers=headers,
                timeout=30
            )
            
            if draft_response.status_code in [200, 201]:
                draft_data = draft_response.json()
                draft_id = self.extract_draft_id(draft_data)
                
                if draft_id:
                    result["draft_creation"]["success"] = True
                    result["draft_creation"]["draft_id"] = draft_id
                    logger.info(f"✅ Draft created: {draft_id}")
                    
                    # Step 2: Create activity with draft ID
                    activity_payload = payload.copy()
                    activity_payload['id'] = draft_id
                    
                    logger.info(f"🧪 Testing activity creation with ID: {draft_id}")
                    
                    activity_response = requests.post(
                        self.config['activity_api_url'],
                        json=activity_payload,
                        headers=headers,
                        timeout=30
                    )
                    
                    if activity_response.status_code in [200, 201]:
                        result["activity_creation"]["success"] = True
                        result["overall_success"] = True
                        logger.info(f"✅ Activity created successfully!")
                    else:
                        result["activity_creation"]["error"] = f"HTTP {activity_response.status_code}: {activity_response.text}"
                        logger.error(f"❌ Activity creation failed: {result['activity_creation']['error']}")
                else:
                    result["draft_creation"]["error"] = "No draft ID found in response"
            else:
                result["draft_creation"]["error"] = f"HTTP {draft_response.status_code}: {draft_response.text}"
                logger.error(f"❌ Draft creation failed: {result['draft_creation']['error']}")
        
        except Exception as e:
            error_msg = f"Exception: {str(e)}"
            if not result["draft_creation"]["error"]:
                result["draft_creation"]["error"] = error_msg
            if not result["activity_creation"]["error"]:
                result["activity_creation"]["error"] = error_msg
            logger.error(f"❌ Test migration exception: {e}")
        
        return result
    
    def extract_draft_id(self, response_data: Dict[str, Any]) -> Optional[str]:
        """Extract draft ID from API response"""
        try:
            # Try direct ID
            if 'id' in response_data and response_data['id']:
                return str(response_data['id'])
            
            # Try data.id
            if 'data' in response_data:
                data = response_data['data']
                if isinstance(data, dict) and 'id' in data:
                    return str(data['id'])
                elif isinstance(data, list) and len(data) > 0 and 'id' in data[0]:
                    return str(data[0]['id'])
            
            # Try alternative fields
            for field in ['draftId', 'activityId', 'guid']:
                if field in response_data and response_data[field]:
                    return str(response_data[field])
            
            return None
        except Exception as e:
            logger.error(f"❌ Error extracting draft ID: {e}")
            return None
    
    def run_comprehensive_fix_and_test(self):
        """Run comprehensive payload fixing and testing"""
        print("🐺" + "="*80)
        print("  WILD WEASEL PAYLOAD FIXER & MIGRATOR")
        print("="*82)
        
        # Step 1: Load and analyze current payloads
        logger.info("📊 Step 1: Loading and analyzing payloads...")
        payloads = self.load_payloads()
        
        if not payloads:
            print("❌ CRITICAL: No payloads found to process!")
            return
        
        analysis = self.analyze_payload_issues(payloads)
        
        print(f"\n📊 PAYLOAD ANALYSIS:")
        print(f"  Total payloads: {analysis['total_payloads']}")
        print(f"  Empty IDs: {analysis['empty_ids']} ← THIS IS THE MAIN ISSUE")
        print(f"  Missing fields: {len(analysis['missing_required_fields'])}")
        print(f"  Structure issues: {len(analysis['structure_issues'])}")
        
        if analysis['empty_ids'] > 0:
            print(f"  🔧 Found {analysis['empty_ids']} payloads with empty ID fields - WILL FIX")
        
        # Step 2: Fix issues
        logger.info("🔧 Step 2: Fixing payload issues...")
        fixed_payloads = self.fix_payload_issues(payloads)
        self.save_fixed_payloads(fixed_payloads)
        
        print(f"\n🔧 PAYLOAD FIXES APPLIED:")
        print(f"  ✅ Fixed empty ID fields with placeholders")
        print(f"  ✅ Ensured all required fields are present")
        print(f"  ✅ Saved fixed payloads to: {self.config['fixed_payload_file']}")
        
        # Step 3: Test migration (if cookies provided)
        if self.session_cookies:
            logger.info("🧪 Step 3: Testing migration with sample payload...")
            test_result = self.test_single_migration(fixed_payloads[0])
            
            print(f"\n🧪 MIGRATION TEST:")
            print(f"  Draft creation: {'✅' if test_result['draft_creation']['success'] else '❌'}")
            if test_result['draft_creation']['draft_id']:
                print(f"  Draft ID: {test_result['draft_creation']['draft_id']}")
            print(f"  Activity creation: {'✅' if test_result['activity_creation']['success'] else '❌'}")
            print(f"  Overall success: {'✅' if test_result['overall_success'] else '❌'}")
            
            if not test_result['overall_success']:
                print(f"\n❌ ERRORS:")
                if test_result['draft_creation']['error']:
                    print(f"  Draft: {test_result['draft_creation']['error']}")
                if test_result['activity_creation']['error']:
                    print(f"  Activity: {test_result['activity_creation']['error']}")
        else:
            print(f"\n🍪 MIGRATION TEST: Skipped (no session cookies)")
            print(f"  Add your session cookies to test the actual API calls")
            self.show_cookie_instructions()
        
        # Step 4: Recommendations
        print(f"\n💡 NEXT STEPS:")
        print(f"  1. ✅ Payloads are now fixed and ready for migration")
        print(f"  2. 🍪 Add session cookies to test API calls")
        print(f"  3. 🚀 Run Wild Weasel v5 Enhanced for full migration:")
        print(f"     python wild_weasel_agent_v5_enhanced.py")
        print(f"  4. 📊 Fixed payloads will have IDs injected during migration")
        
        print("="*82)
    
    def show_cookie_instructions(self):
        """Show instructions for getting session cookies"""
        print(f"\n🍪 TO ADD SESSION COOKIES:")
        print(f"  1. Login to Gainsight in your browser")
        print(f"  2. Open Dev Tools (F12) → Network tab")
        print(f"  3. Make any request to demo-emea1.gainsightcloud.com")
        print(f"  4. Copy the Cookie header value")
        print(f"  5. Update the session_cookies dictionary in this script")
        print(f"  6. Re-run this script to test API connectivity")

def main():
    """Main execution"""
    try:
        fixer = PayloadFixer()
        fixer.run_comprehensive_fix_and_test()
        
    except KeyboardInterrupt:
        print("\n🐺 Payload fixing interrupted by user")
        sys.exit(0)
    except Exception as e:
        logger.error(f"❌ Payload fixing failed: {e}")
        import traceback
        logger.error(f"Traceback: {traceback.format_exc()}")
        sys.exit(1)

if __name__ == "__main__":
    main()

#!/usr/bin/env python3
"""
🐺 Wild Weasel SMART UI Test
Test the SMART UI workflow implementation
"""

import json
import os

def test_smart_ui_implementation():
    """Test the SMART UI workflow in the updated agent"""
    
    print("🐺" + "="*80)
    print("  WILD WEASEL SMART UI WORKFLOW TEST")
    print("="*82)
    
    agent_file = "/Users/<USER>/Desktop/wild_weasel_gainsight_migration/wild_weasel_agent_v4_final.py"
    
    # Test 1: Check if SMART UI method exists
    print("📝 Test 1: Checking SMART UI method implementation...")
    try:
        with open(agent_file, 'r') as f:
            agent_code = f.read()
        
        # Check for key SMART UI components
        smart_ui_components = [
            'create_activity_via_ui_smart',
            'handle_response',
            'drafts_response_data',
            'unique_id',
            "'/v1/ant/v2/activity/drafts'",
            'page.on(\'response\'',
            'nz-select[placeholder=\"Select Activity Type\"]',
            'Log Activity'
        ]
        
        missing_components = []
        for component in smart_ui_components:
            if component not in agent_code:
                missing_components.append(component)
        
        if missing_components:
            print(f"  ❌ Missing SMART UI components: {missing_components}")
            return False
        
        print("  ✅ All SMART UI components found")
        
    except Exception as e:
        print(f"  ❌ Error reading agent file: {e}")
        return False
    
    # Test 2: Check the workflow implementation
    print("\n🔄 Test 2: Verifying SMART UI workflow...")
    
    workflow_steps = [
        ('Network Monitoring', 'page.on(\'response\''),
        ('Create Activity Button', 'Create Activity'),
        ('Email Dropdown', 'nz-select'),
        ('Email Selection', 'Email'),
        ('Drafts API Capture', 'drafts_response_data'),
        ('Form Filling', 'fill_activity_form_enhanced'),
        ('Log Activity', 'Log Activity')
    ]
    
    all_steps_found = True
    for step_name, step_code in workflow_steps:
        if step_code in agent_code:
            print(f"  ✅ {step_name}: Found")
        else:
            print(f"  ❌ {step_name}: Missing")
            all_steps_found = False
    
    if not all_steps_found:
        return False
    
    # Test 3: Check enhanced form filling
    print("\n📝 Test 3: Checking enhanced form filling...")
    
    form_features = [
        ('Subject Filling', 'input[placeholder*=\"Subject\"'),
        ('Content Filling', 'textarea[placeholder*=\"Notes\"'),
        ('JavaScript Fallback', 'dispatchEvent'),
        ('Touchpoint Reason', 'Ant__Touchpoint_Reason__c'),
        ('Activity Date', 'activityDate')
    ]
    
    form_complete = True
    for feature_name, feature_code in form_features:
        if feature_code in agent_code:
            print(f"  ✅ {feature_name}: Implemented")
        else:
            print(f"  ⚠️ {feature_name}: Not found (may be optional)")
    
    # Test 4: Check integration with migration workflow
    print("\n🔄 Test 4: Checking integration with migration workflow...")
    
    integration_checks = [
        ('Method Called', 'create_activity_via_ui_smart'),
        ('SMART_UI Method Used', 'method_used = \"SMART_UI\"'),
        ('UI Stats Updated', 'ui_stats'),
        ('Response Handler Cleanup', 'remove_listener')
    ]
    
    integration_complete = True
    for check_name, check_code in integration_checks:
        if check_code in agent_code:
            print(f"  ✅ {check_name}: Correct")
        else:
            print(f"  ❌ {check_name}: Missing")
            integration_complete = False
    
    if not integration_complete:
        return False
    
    # Test 5: Summary
    print("\n📋 Test 5: SMART UI Workflow Summary")
    print("  🎯 WORKFLOW STEPS:")
    print("    1. ✅ Set up network response monitoring")
    print("    2. ✅ Click 'Create Activity' button")
    print("    3. ✅ Open activity type dropdown")
    print("    4. ✅ Select 'Email' (triggers drafts API)")
    print("    5. ✅ Capture unique ID from drafts response")
    print("    6. ✅ Fill activity form with enhanced selectors")
    print("    7. ✅ Click 'Log Activity' to submit")
    print("    8. ✅ Clean up response listeners")
    
    print("\n💡 KEY FEATURES:")
    print("  🔍 Network monitoring captures drafts API automatically")
    print("  🎯 Enhanced selectors for Gainsight UI elements")
    print("  ⚡ JavaScript fallbacks for robust automation")
    print("  🧹 Proper cleanup of event listeners")
    
    print("="*82)
    print("🐺 SMART UI WORKFLOW TEST: ✅ PASSED")
    print("="*82)
    
    return True

if __name__ == "__main__":
    try:
        success = test_smart_ui_implementation()
        if success:
            print("\n🎉 SMART UI workflow is correctly implemented!")
            print("\n🚀 Ready to execute:")
            print("   python3 wild_weasel_agent_v4_final.py")
            print("\n💡 The agent will now:")
            print("   1. Try API calls first (priority)")
            print("   2. Fall back to SMART UI workflow if API fails")
            print("   3. Automatically capture drafts API calls during UI workflow")
        else:
            print("\n❌ SMART UI workflow implementation has issues.")
    except Exception as e:
        print(f"\n❌ Test failed with error: {e}")

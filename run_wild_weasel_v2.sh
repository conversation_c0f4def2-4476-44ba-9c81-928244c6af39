#!/bin/bash

# 🐺 Wild Weasel v2.0 - Complete Migration Execution
echo "🐺 Wild Weasel v2.0 - FIXED Totango to Gainsight Migration"
echo "================================================================"
echo ""

# Step 1: Run FIXED data analysis
echo "📊 Step 1: Running FIXED data analysis..."
python3 analyze_totango_data_fixed.py

if [ $? -ne 0 ]; then
    echo "❌ Data analysis failed. Aborting mission."
    exit 1
fi

echo ""
echo "✅ Data analysis completed successfully!"
echo ""

# Step 2: Run enhanced migration agent
echo "🚀 Step 2: Running enhanced migration agent..."
python3 wild_weasel_agent_v2.py

if [ $? -ne 0 ]; then
    echo "❌ Migration failed."
    exit 1
fi

echo ""
echo "🎉 Wild Weasel v2.0 mission completed successfully!"
echo "================================================================"

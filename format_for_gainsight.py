#!/usr/bin/env python3
"""
🐺 Wild Weasel - Format Activities for Gainsight API
================================================================================
Mission: Convert extracted_email_activities_fixed.json to gainsight_api_payload_email_activities.json
Target: Create the exact file format expected by wild_weasel_agent_v4_final.py

This script bridges the gap between the extraction script output and the main agent input.
"""

import json
import os
import sys
from datetime import datetime
import logging

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger("WildWeasel-Formatter")

class GainsightPayloadFormatter:
    """Convert extracted activities to proper Gainsight API payload format"""
    
    def __init__(self):
        self.base_dir = "/Users/<USER>/Desktop/wild_weasel_gainsight_migration"
        self.input_file = os.path.join(self.base_dir, "extracted_email_activities_fixed.json")
        self.output_file = os.path.join(self.base_dir, "gainsight_api_payload_email_activities.json")
    
    def load_extracted_activities(self):
        """Load the extracted activities from analyze_totango_data_fixed.py"""
        try:
            if not os.path.exists(self.input_file):
                logger.error(f"❌ Input file not found: {self.input_file}")
                return None
            
            with open(self.input_file, 'r') as f:
                activities = json.load(f)
            
            logger.info(f"📊 Loaded {len(activities)} extracted activities")
            return activities
            
        except Exception as e:
            logger.error(f"❌ Failed to load extracted activities: {e}")
            return None
    
    def convert_to_gainsight_format(self, activities):
        """Convert to the format expected by wild_weasel_agent_v4_final.py"""
        converted_activities = []
        
        for i, activity in enumerate(activities):
            try:
                # Extract data from the analyzed format
                if 'records' in activity and len(activity['records']) > 0:
                    record = activity['records'][0]
                    
                    # Build the format expected by the main agent
                    gainsight_payload = {
                        "note": {
                            "subject": record.get('Subject', 'Email Activity'),
                            "content": record.get('Notes', ''),
                            "plainText": record.get('Notes', ''),
                            "activityDate": self.convert_activity_date(record.get('ActivityDate')),
                            "customFields": {
                                "Ant__Touchpoint_Reason__c": record.get('customField_TouchpointReason', 'COMMUNICATION')
                            }
                        },
                        "meta": {
                            "type": "Email",
                            "source": "Totango Migration",
                            "originalId": record.get('customField_OriginalId', ''),
                            "originalType": record.get('customField_OriginalType', ''),
                            "migrationTimestamp": record.get('customField_MigrationTimestamp', datetime.now().isoformat())
                        },
                        "author": {
                            "email": record.get('Author', '<EMAIL>'),
                            "name": record.get('Author', 'Migration User').split('@')[0]
                        },
                        "contexts": [
                            {
                                "type": "Company",
                                "id": record.get('ExternalId', record.get('companyExternalId', '0015p00005R7ysqAAB')),
                                "name": "ICICI Bank"  # Default company name
                            }
                        ],
                        "attendees": {
                            "internal": record.get('internalAttendees', []),
                            "external": record.get('externalAttendees', [])
                        },
                        # Include original metadata for debugging
                        "original_totango_data": activity.get('metadata', {}).get('totango_original', {}),
                        "processing_info": {
                            "converted_by": "format_for_gainsight.py",
                            "conversion_timestamp": datetime.now().isoformat(),
                            "activity_index": i
                        }
                    }
                    
                    converted_activities.append(gainsight_payload)
                    
                else:
                    logger.warning(f"⚠️ Activity {i} has unexpected format - skipping")
                    
            except Exception as e:
                logger.error(f"❌ Error converting activity {i}: {e}")
                continue
        
        logger.info(f"✅ Converted {len(converted_activities)} activities to Gainsight format")
        return converted_activities
    
    def convert_activity_date(self, activity_date):
        """Convert activity date to timestamp format expected by Gainsight"""
        try:
            if isinstance(activity_date, str):
                # Parse ISO format and convert to timestamp
                dt = datetime.fromisoformat(activity_date.replace('Z', '+00:00'))
                return int(dt.timestamp() * 1000)  # Gainsight expects milliseconds
            elif isinstance(activity_date, (int, float)):
                return int(activity_date)  # Already a timestamp
            else:
                # Default to current time
                return int(datetime.now().timestamp() * 1000)
        except:
            # Fallback to current timestamp
            return int(datetime.now().timestamp() * 1000)
    
    def save_gainsight_payload(self, activities):
        """Save activities in the format expected by wild_weasel_agent_v4_final.py"""
        try:
            with open(self.output_file, 'w') as f:
                json.dump(activities, f, indent=2, default=str)
            
            logger.info(f"💾 Saved {len(activities)} activities to {self.output_file}")
            return True
            
        except Exception as e:
            logger.error(f"❌ Failed to save Gainsight payload: {e}")
            return False
    
    def validate_output_format(self, activities):
        """Validate that the output matches what wild_weasel_agent_v4_final.py expects"""
        try:
            if not activities:
                logger.error("❌ No activities to validate")
                return False
            
            sample = activities[0]
            required_fields = ['note', 'meta', 'author', 'contexts']
            
            for field in required_fields:
                if field not in sample:
                    logger.error(f"❌ Missing required field: {field}")
                    return False
            
            # Validate note structure
            note = sample.get('note', {})
            note_fields = ['subject', 'content', 'activityDate']
            for field in note_fields:
                if field not in note:
                    logger.warning(f"⚠️ Missing note field: {field}")
            
            # Validate contexts structure
            contexts = sample.get('contexts', [])
            if not contexts or not isinstance(contexts, list):
                logger.error("❌ Invalid contexts structure")
                return False
            
            context = contexts[0]
            if 'type' not in context or 'id' not in context:
                logger.error("❌ Invalid context structure")
                return False
            
            logger.info("✅ Output format validation passed")
            return True
            
        except Exception as e:
            logger.error(f"❌ Validation error: {e}")
            return False
    
    def format_activities(self):
        """Main formatting process"""
        logger.info("🐺 Wild Weasel Gainsight Formatter Starting...")
        
        # Load extracted activities
        activities = self.load_extracted_activities()
        if not activities:
            return False
        
        # Convert to Gainsight format
        gainsight_activities = self.convert_to_gainsight_format(activities)
        if not gainsight_activities:
            logger.error("❌ No activities converted")
            return False
        
        # Validate format
        if not self.validate_output_format(gainsight_activities):
            logger.error("❌ Output format validation failed")
            return False
        
        # Save to expected file
        if not self.save_gainsight_payload(gainsight_activities):
            return False
        
        # Generate summary
        self.generate_summary(activities, gainsight_activities)
        
        logger.info("🎯 Gainsight formatting completed successfully!")
        return True
    
    def generate_summary(self, original_activities, converted_activities):
        """Generate a summary of the formatting process"""
        summary = {
            "formatting_timestamp": datetime.now().isoformat(),
            "input_file": self.input_file,
            "output_file": self.output_file,
            "original_count": len(original_activities),
            "converted_count": len(converted_activities),
            "conversion_rate": f"{len(converted_activities)/len(original_activities)*100:.1f}%" if original_activities else "0%",
            "ready_for_wild_weasel": len(converted_activities) > 0,
            "sample_activity": converted_activities[0] if converted_activities else None
        }
        
        # Save summary
        summary_file = os.path.join(self.base_dir, "formatting_summary.json")
        with open(summary_file, 'w') as f:
            json.dump(summary, f, indent=2, default=str)
        
        # Display summary
        print("\n" + "="*80)
        print("🐺 WILD WEASEL - GAINSIGHT FORMATTING SUMMARY")
        print("="*80)
        print(f"📁 Input: {os.path.basename(self.input_file)}")
        print(f"📁 Output: {os.path.basename(self.output_file)}")
        print(f"📊 Original Activities: {summary['original_count']}")
        print(f"✅ Converted Activities: {summary['converted_count']}")
        print(f"📈 Conversion Rate: {summary['conversion_rate']}")
        print(f"🎯 Ready for Wild Weasel: {'✅ YES' if summary['ready_for_wild_weasel'] else '❌ NO'}")
        print("="*80)

def main():
    """Main execution function"""
    try:
        formatter = GainsightPayloadFormatter()
        success = formatter.format_activities()
        
        if success:
            print("🎉 SUCCESS: Activities formatted for Wild Weasel v4.0!")
            print("   → You can now run wild_weasel_agent_v4_final.py")
            sys.exit(0)
        else:
            print("❌ FAILED: Could not format activities")
            sys.exit(1)
            
    except KeyboardInterrupt:
        print("\n🐺 Formatting interrupted by user")
        sys.exit(0)
    except Exception as e:
        logger.error(f"❌ Formatting failed: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()

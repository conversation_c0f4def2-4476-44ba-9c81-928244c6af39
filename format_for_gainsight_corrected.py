#!/usr/bin/env python3
"""
🐺 Wild Weasel - CORRECTED Gainsight Format Converter
================================================================================
Mission: Convert extracted_email_activities_fixed.json to ACTUAL Gainsight API format
Target: Match the exact structure used by Gainsight C360 Timeline API

This corrected version creates the proper Gainsight API payload structure.
"""

import json
import os
import sys
from datetime import datetime
import logging

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger("WildWeasel-CorrectedFormatter")

class GainsightCorrectFormatter:
    """Convert to the ACTUAL Gainsight API format structure"""
    
    def __init__(self):
        self.base_dir = "/Users/<USER>/Desktop/wild_weasel_gainsight_migration"
        self.input_file = os.path.join(self.base_dir, "extracted_email_activities_fixed.json")
        self.output_file = os.path.join(self.base_dir, "gainsight_api_payload_email_activities.json")
        
        # Default Gainsight IDs (these would need to be actual IDs from your instance)
        self.default_user_id = "1P01E316G9DAPFOLE6SOOUG71XRMN5F3PLER"  # Default user ID
        self.default_company_id = "1P02IPMAEL4M3CQGY4K5FHU22D2HHT11KCKU"  # ICICI company ID
        self.email_activity_type_id = "c81eeccf-2aa1-45bc-be71-5377f148e1e9"  # Email activity type
        self.touchpoint_reason_id = "1I00J1INQCQ6GQ3T2MWULITYE9ASDFPQ2KD3"  # Communication touchpoint
        self.flow_type_id = "1I00EBMOY66ROGCBY63I51PAXG5OJRPK37AQ"  # Default flow type
    
    def load_extracted_activities(self):
        """Load the extracted activities"""
        try:
            if not os.path.exists(self.input_file):
                logger.error(f"❌ Input file not found: {self.input_file}")
                return None
            
            with open(self.input_file, 'r') as f:
                activities = json.load(f)
            
            logger.info(f"📊 Loaded {len(activities)} extracted activities")
            return activities
            
        except Exception as e:
            logger.error(f"❌ Failed to load extracted activities: {e}")
            return None
    
    def create_user_object(self, email, name=None):
        """Create a Gainsight user object"""
        if not name:
            name = email.split('@')[0].replace('.', ' ').title()
        
        return {
            "id": self.default_user_id,  # In real scenario, this would be looked up
            "obj": "User",
            "name": name,
            "email": email,
            "eid": None,
            "eobj": "User", 
            "epp": None,
            "esys": "SALESFORCE",
            "sys": "GAINSIGHT",
            "pp": ""
        }
    
    def create_attendee_object(self, email, name=None):
        """Create attendee object for customFields"""
        if not name:
            name = email.split('@')[0].replace('.', ' ').title()
        
        return {
            "id": self.default_user_id,
            "obj": "User",
            "name": name,
            "email": email,
            "eid": None,
            "eobj": "User",
            "epp": None,
            "esys": "SALESFORCE",
            "sys": "GAINSIGHT",
            "pp": ""
        }
    
    def convert_to_gainsight_format(self, activities):
        """Convert to actual Gainsight API format"""
        converted_activities = []
        
        for i, activity in enumerate(activities):
            try:
                # Extract data from the analyzed format
                if 'records' in activity and len(activity['records']) > 0:
                    record = activity['records'][0]
                    
                    # Convert attendees
                    internal_attendees = []
                    external_attendees = []
                    
                    for email in record.get('internalAttendees', []):
                        internal_attendees.append(self.create_attendee_object(email))
                    
                    for email in record.get('externalAttendees', []):
                        external_attendees.append(self.create_attendee_object(email))
                    
                    # Build the actual Gainsight structure
                    gainsight_activity = {
                        "lastModifiedByUser": self.create_user_object(
                            record.get('Author', '<EMAIL>'),
                            "Migration User"
                        ),
                        "note": {
                            "customFields": {
                                "internalAttendees": internal_attendees,
                                "externalAttendees": external_attendees,
                                "ant__Status1552512571338": None,
                                "Ant__Touchpoint_Reason__c": self.touchpoint_reason_id,
                                "Ant__Flow_Type__c": self.flow_type_id
                            },
                            "type": "EMAIL",
                            "subject": record.get('Subject', 'Email Activity')[:255],
                            "activityDate": self.convert_activity_date(record.get('ActivityDate')),
                            "content": self.escape_html(record.get('Notes', 'Email activity migrated from Totango')),
                            "plainText": record.get('Notes', 'Email activity migrated from Totango'),
                            "trackers": None
                        },
                        "mentions": [],
                        "relatedRecords": {},
                        "meta": {
                            "activityTypeId": self.email_activity_type_id,
                            "ctaId": None,
                            "source": "C360",
                            "hasTask": False,
                            "emailSent": False,
                            "systemType": "GAINSIGHT",
                            "notesTemplateId": None
                        },
                        "author": self.create_user_object(
                            record.get('Author', '<EMAIL>'),
                            "Migration User"
                        ),
                        "syncedToSFDC": False,
                        "id": None,  # Will be generated by Gainsight
                        "tasks": [],
                        "attachments": [],
                        "contexts": [
                            {
                                "id": self.default_company_id,
                                "obj": "Company",
                                "eobj": "Account",
                                "eid": record.get('ExternalId', record.get('companyExternalId')),
                                "esys": "SALESFORCE",
                                "lbl": "ICICI Bank",
                                "dsp": True,
                                "base": True
                            }
                        ]
                    }
                    
                    converted_activities.append(gainsight_activity)
                    
                else:
                    logger.warning(f"⚠️ Activity {i} has unexpected format - skipping")
                    
            except Exception as e:
                logger.error(f"❌ Error converting activity {i}: {e}")
                continue
        
        logger.info(f"✅ Converted {len(converted_activities)} activities to ACTUAL Gainsight format")
        return converted_activities
    
    def convert_activity_date(self, activity_date):
        """Convert activity date to timestamp format expected by Gainsight"""
        try:
            if isinstance(activity_date, str):
                # Parse ISO format and convert to timestamp
                dt = datetime.fromisoformat(activity_date.replace('Z', '+00:00'))
                return int(dt.timestamp() * 1000)  # Gainsight expects milliseconds
            elif isinstance(activity_date, (int, float)):
                return int(activity_date)  # Already a timestamp
            else:
                # Default to current time
                return int(datetime.now().timestamp() * 1000)
        except:
            # Fallback to current timestamp
            return int(datetime.now().timestamp() * 1000)
    
    def escape_html(self, text):
        """Escape HTML content for Gainsight"""
        if not text:
            return ""
        
        # Basic HTML escaping
        text = text.replace('&', '&amp;')
        text = text.replace('<', '&lt;')
        text = text.replace('>', '&gt;')
        text = text.replace('"', '&quot;')
        text = text.replace("'", '&#x27;')
        
        return text
    
    def save_gainsight_payload(self, activities):
        """Save activities in the correct Gainsight format"""
        try:
            with open(self.output_file, 'w') as f:
                json.dump(activities, f, indent=2, default=str)
            
            logger.info(f"💾 Saved {len(activities)} activities to {self.output_file}")
            return True
            
        except Exception as e:
            logger.error(f"❌ Failed to save Gainsight payload: {e}")
            return False
    
    def validate_output_format(self, activities):
        """Validate that the output matches actual Gainsight API structure"""
        try:
            if not activities:
                logger.error("❌ No activities to validate")
                return False
            
            sample = activities[0]
            required_top_level = ['lastModifiedByUser', 'note', 'mentions', 'relatedRecords', 
                                'meta', 'author', 'syncedToSFDC', 'tasks', 'attachments', 'contexts']
            
            for field in required_top_level:
                if field not in sample:
                    logger.error(f"❌ Missing required top-level field: {field}")
                    return False
            
            # Validate note structure
            note = sample.get('note', {})
            note_fields = ['customFields', 'type', 'subject', 'activityDate', 'content', 'plainText']
            for field in note_fields:
                if field not in note:
                    logger.warning(f"⚠️ Missing note field: {field}")
            
            # Validate customFields structure
            custom_fields = note.get('customFields', {})
            required_custom = ['internalAttendees', 'externalAttendees', 'Ant__Touchpoint_Reason__c']
            for field in required_custom:
                if field not in custom_fields:
                    logger.warning(f"⚠️ Missing custom field: {field}")
            
            # Validate contexts structure
            contexts = sample.get('contexts', [])
            if not contexts or not isinstance(contexts, list):
                logger.error("❌ Invalid contexts structure")
                return False
            
            context = contexts[0]
            context_fields = ['id', 'obj', 'eobj', 'eid', 'esys', 'lbl', 'dsp', 'base']
            for field in context_fields:
                if field not in context:
                    logger.warning(f"⚠️ Missing context field: {field}")
            
            logger.info("✅ Output format validation passed - matches Gainsight API structure")
            return True
            
        except Exception as e:
            logger.error(f"❌ Validation error: {e}")
            return False
    
    def format_activities(self):
        """Main formatting process"""
        logger.info("🐺 Wild Weasel CORRECTED Gainsight Formatter Starting...")
        
        # Load extracted activities
        activities = self.load_extracted_activities()
        if not activities:
            return False
        
        # Convert to actual Gainsight format
        gainsight_activities = self.convert_to_gainsight_format(activities)
        if not gainsight_activities:
            logger.error("❌ No activities converted")
            return False
        
        # Validate format
        if not self.validate_output_format(gainsight_activities):
            logger.error("❌ Output format validation failed")
            return False
        
        # Save to expected file
        if not self.save_gainsight_payload(gainsight_activities):
            return False
        
        # Generate summary
        self.generate_summary(activities, gainsight_activities)
        
        logger.info("🎯 CORRECTED Gainsight formatting completed successfully!")
        return True
    
    def generate_summary(self, original_activities, converted_activities):
        """Generate a summary of the formatting process"""
        summary = {
            "formatting_timestamp": datetime.now().isoformat(),
            "input_file": self.input_file,
            "output_file": self.output_file,
            "original_count": len(original_activities),
            "converted_count": len(converted_activities),
            "conversion_rate": f"{len(converted_activities)/len(original_activities)*100:.1f}%" if original_activities else "0%",
            "format_version": "ACTUAL Gainsight API Structure",
            "ready_for_wild_weasel": len(converted_activities) > 0,
            "sample_activity": converted_activities[0] if converted_activities else None
        }
        
        # Save summary
        summary_file = os.path.join(self.base_dir, "corrected_formatting_summary.json")
        with open(summary_file, 'w') as f:
            json.dump(summary, f, indent=2, default=str)
        
        # Display summary
        print("\n" + "="*80)
        print("🐺 WILD WEASEL - CORRECTED GAINSIGHT FORMATTING SUMMARY")
        print("="*80)
        print(f"📁 Input: {os.path.basename(self.input_file)}")
        print(f"📁 Output: {os.path.basename(self.output_file)}")
        print(f"📊 Original Activities: {summary['original_count']}")
        print(f"✅ Converted Activities: {summary['converted_count']}")
        print(f"📈 Conversion Rate: {summary['conversion_rate']}")
        print(f"🏗️ Format Version: {summary['format_version']}")
        print(f"🎯 Ready for Wild Weasel: {'✅ YES' if summary['ready_for_wild_weasel'] else '❌ NO'}")
        print("="*80)
        print("🔧 KEY CORRECTIONS APPLIED:")
        print("  ✅ Proper Gainsight API structure with all required fields")
        print("  ✅ Correct attendees format in customFields")
        print("  ✅ Proper author and lastModifiedByUser objects")
        print("  ✅ Correct contexts structure with Gainsight IDs")
        print("  ✅ All required top-level fields (mentions, tasks, attachments, etc.)")
        print("="*80)

def main():
    """Main execution function"""
    try:
        formatter = GainsightCorrectFormatter()
        success = formatter.format_activities()
        
        if success:
            print("🎉 SUCCESS: Activities formatted with ACTUAL Gainsight API structure!")
            print("   → File now matches the real Gainsight format")
            print("   → You can now run wild_weasel_agent_v4_final.py")
            sys.exit(0)
        else:
            print("❌ FAILED: Could not format activities")
            sys.exit(1)
            
    except KeyboardInterrupt:
        print("\n🐺 Formatting interrupted by user")
        sys.exit(0)
    except Exception as e:
        logger.error(f"❌ Formatting failed: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()

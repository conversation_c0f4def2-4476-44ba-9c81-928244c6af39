[{"row_number": 1, "subject": "ICICI: Mend Platform Access", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:16:57.265748"}, {"row_number": 2, "subject": "ICICI: Mend Platform Access", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:17:02.304833"}, {"row_number": 3, "subject": "ICICI: Mend Platform Access", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:17:07.376694"}, {"row_number": 4, "subject": "ICICI: Mend Platform Access", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:17:12.451184"}, {"row_number": 5, "subject": "ICICI: Mend Platform Access", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:17:17.529764"}, {"row_number": 6, "subject": "ICICI: Mend Platform Access", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:23:02.032118"}, {"row_number": 7, "subject": "ICICI: <PERSON><PERSON><PERSON> to Delighted", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:25:08.071629"}, {"row_number": 8, "subject": "ICICI: NPS Send Date", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:27:14.103875"}, {"row_number": 9, "subject": "ICICI: NPS Last Sent", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:27:42.035361"}, {"row_number": 10, "subject": "ICICI: Mend Platform Access", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:27:44.086004"}, {"row_number": 11, "subject": "ICICI: May Newsletter: AI Risk Insights, Smarter Compliance and Cloud Integration!", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:27:46.092527"}, {"row_number": 12, "subject": "ICICI: Mend Platform Access", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:27:48.100877"}, {"row_number": 13, "subject": "ICICI: Mend Platform Access", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:27:50.105474"}, {"row_number": 14, "subject": "ICICI: MP Activity Frequency", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:27:52.117461"}, {"row_number": 15, "subject": "ICICI: <PERSON> <PERSON><PERSON><PERSON> (7d)", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:27:54.125652"}, {"row_number": 16, "subject": "ICICI: Mend Platform Access", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:27:56.130774"}, {"row_number": 17, "subject": "ICICI: SCA CD Variance Stages", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:27:58.134473"}, {"row_number": 18, "subject": "ICICI: <div><br>Upcoming NPS Survey&nbsp;</div>", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:28:00.142809"}, {"row_number": 19, "subject": "ICICI: Mend Platform Access", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:28:02.157802"}, {"row_number": 20, "subject": "ICICI: MP Activity", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:28:04.173565"}, {"row_number": 21, "subject": "ICICI: MP Activity Frequency", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:28:06.182032"}, {"row_number": 22, "subject": "ICICI: MITRE", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:28:08.190878"}, {"row_number": 23, "subject": "ICICI: MP Abandonment Date", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:28:10.199441"}, {"row_number": 24, "subject": "ICICI: MP Activity", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:28:12.204935"}, {"row_number": 25, "subject": "ICICI: Mend Platform Access", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:28:14.213810"}, {"row_number": 26, "subject": "ICICI: Mend Platform Access", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:28:16.222754"}, {"row_number": 27, "subject": "ICICI: April Newsletter: New Dashboard, Mend AI, and More!", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:28:18.229445"}, {"row_number": 28, "subject": "ICICI: Mend Platform Access", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:28:20.237999"}, {"row_number": 29, "subject": "ICICI: Mend Platform Access", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:28:22.246694"}, {"row_number": 30, "subject": "ICICI: Mend Platform Access", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:28:24.252797"}, {"row_number": 31, "subject": "ICICI: MP Activity Frequency", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:28:26.258018"}, {"row_number": 32, "subject": "ICICI: MP Activity", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:28:28.266381"}, {"row_number": 33, "subject": "ICICI: <PERSON> Newsletter: Introducing Mend AI and More!", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:28:30.294815"}, {"row_number": 34, "subject": "ICICI: MP Abandonment Date", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:28:32.306860"}, {"row_number": 35, "subject": "ICICI: MP Activity", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:28:34.314745"}, {"row_number": 36, "subject": "ICICI: MP Activity Frequency", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:28:36.327534"}, {"row_number": 37, "subject": "ICICI: Mend Platform Access", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:28:38.336052"}, {"row_number": 38, "subject": "ICICI: MP Activity Frequency", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:28:40.355863"}, {"row_number": 39, "subject": "ICICI: <PERSON> <PERSON><PERSON><PERSON> (7d)", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:28:42.370701"}, {"row_number": 40, "subject": "ICICI: Mend Platform Access", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:28:44.382103"}, {"row_number": 41, "subject": "ICICI: Active - CN", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:28:46.390896"}, {"row_number": 42, "subject": "ICICI: Active - SAST", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:28:48.407672"}, {"row_number": 43, "subject": "ICICI: Active - SCA", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:28:50.414986"}, {"row_number": 44, "subject": "ICICI: Mend Platform Access", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:28:52.427036"}, {"row_number": 45, "subject": "ICICI: Invicti Campaign", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:28:54.435531"}, {"row_number": 46, "subject": "ICICI: MP Activity", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:28:56.442279"}, {"row_number": 47, "subject": "ICICI: Mend Platform Access", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:28:58.462551"}, {"row_number": 48, "subject": "ICICI: MP Abandonment Date", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:29:00.478055"}, {"row_number": 49, "subject": "ICICI: MP Activity", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:29:02.501157"}, {"row_number": 50, "subject": "ICICI: MP Activity", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:29:04.519235"}, {"row_number": 51, "subject": "ICICI: February Newsletter: AI-Powered Code Remediation and More!", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:29:06.533509"}, {"row_number": 52, "subject": "ICICI: MP Abandonment Date", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:29:08.556601"}, {"row_number": 53, "subject": "ICICI: MP Activity", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:29:10.574671"}, {"row_number": 54, "subject": "ICICI: Mend Platform Access", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:29:12.593775"}, {"row_number": 55, "subject": "ICICI: MP Activity", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:29:14.609654"}, {"row_number": 56, "subject": "ICICI: Mend Platform Access", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:29:16.626874"}, {"row_number": 57, "subject": "ICICI: MP Abandonment Date", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:29:18.644184"}, {"row_number": 58, "subject": "ICICI: MP Activity", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:29:20.662322"}, {"row_number": 59, "subject": "ICICI: MP Activity", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:29:22.675057"}, {"row_number": 60, "subject": "ICICI: MP Activity Frequency", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:29:24.688393"}, {"row_number": 61, "subject": "ICICI: SAST Planned Downtime_App_copy", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:29:26.704364"}, {"row_number": 62, "subject": "ICICI: Mend Platform Access", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:29:28.715583"}, {"row_number": 63, "subject": "ICICI: MP Abandonment Date", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:29:30.732472"}, {"row_number": 64, "subject": "ICICI: MP Activity", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:29:32.739390"}, {"row_number": 65, "subject": "ICICI: MP Activity", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:29:34.757764"}, {"row_number": 66, "subject": "ICICI: MP Abandonment Date", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:29:36.772936"}, {"row_number": 67, "subject": "ICICI: MP Activity", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:29:38.785757"}, {"row_number": 68, "subject": "ICICI: AI Design Partners", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:29:40.802850"}, {"row_number": 69, "subject": "ICICI: Mend Platform Access", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:29:42.812310"}, {"row_number": 70, "subject": "ICICI: MP Activity", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:29:44.832240"}, {"row_number": 71, "subject": "ICICI: <PERSON> <PERSON><PERSON><PERSON> (14d)", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:29:46.850225"}, {"row_number": 72, "subject": "ICICI: Mend Platform Access", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:29:48.867216"}, {"row_number": 73, "subject": "ICICI: MP Abandonment Date", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:29:50.886272"}, {"row_number": 74, "subject": "ICICI: MP Activity", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:29:52.897824"}, {"row_number": 75, "subject": "ICICI: MP Activity", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:29:54.918541"}, {"row_number": 76, "subject": "ICICI: Mend Platform Access", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:29:56.935761"}, {"row_number": 77, "subject": "ICICI: MP Abandonment Date", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:29:58.954297"}, {"row_number": 78, "subject": "ICICI: MP Activity", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:30:00.973115"}, {"row_number": 79, "subject": "ICICI: MP Activity Frequency", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:30:02.984496"}, {"row_number": 80, "subject": "ICICI: MP Activity", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:30:05.001203"}, {"row_number": 81, "subject": "ICICI: January Newsletter:  Keep Your Code Secure, Exciting Updates from Mend.io!", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:30:07.015217"}, {"row_number": 82, "subject": "ICICI: Mend Platform Access", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:30:09.034471"}, {"row_number": 83, "subject": "ICICI: MP Abandonment Date", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:30:11.055605"}, {"row_number": 84, "subject": "ICICI: MP Activity", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:30:13.069342"}, {"row_number": 85, "subject": "ICICI: MP Activity", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:30:15.086192"}, {"row_number": 86, "subject": "ICICI: Mend Platform Access", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:30:17.101459"}, {"row_number": 87, "subject": "ICICI: MP Activity Frequency", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:30:19.120813"}, {"row_number": 88, "subject": "ICICI: MP Abandonment Date", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:30:21.136958"}, {"row_number": 89, "subject": "ICICI: MP Activity", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:30:23.147704"}, {"row_number": 90, "subject": "ICICI: <PERSON><PERSON><PERSON> to Delighted", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:30:25.154311"}, {"row_number": 91, "subject": "ICICI: <PERSON><PERSON><PERSON> to Delighted", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:30:27.168351"}, {"row_number": 92, "subject": "ICICI: NPS Send Date", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:30:29.178178"}, {"row_number": 93, "subject": "ICICI: NPS Last Sent", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:30:31.197086"}, {"row_number": 94, "subject": "ICICI: NPS Send Date", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:30:33.210722"}, {"row_number": 95, "subject": "ICICI: NPS Last Sent", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:30:35.230502"}, {"row_number": 96, "subject": "ICICI: Mend Platform Access", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:30:37.243502"}, {"row_number": 97, "subject": "ICICI: Mend Platform Access", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:30:39.260156"}, {"row_number": 98, "subject": "ICICI: Risk Status", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:30:41.280882"}, {"row_number": 99, "subject": "ICICI: Risk Next Step(s)", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:30:43.297010"}, {"row_number": 100, "subject": "ICICI: Primary Risk Reason", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:30:45.315257"}, {"row_number": 101, "subject": "ICICI: Mend Platform Access", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:30:47.333187"}, {"row_number": 102, "subject": "ICICI: <div><br>Upcoming NPS Survey&nbsp;</div>", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:30:49.352704"}, {"row_number": 103, "subject": "ICICI: Mend Platform Access", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:30:51.367523"}, {"row_number": 104, "subject": "ICICI: Mend Platform Access", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:30:53.381970"}, {"row_number": 105, "subject": "ICICI: Risk KPI", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:30:55.398748"}, {"row_number": 106, "subject": "ICICI: <PERSON> <PERSON> <PERSON><PERSON><PERSON>", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:30:57.413281"}, {"row_number": 107, "subject": "ICICI: Mend Platform Access", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:30:59.429467"}, {"row_number": 108, "subject": "ICICI: December Newsletter: Sharper Risk Insights & Updates", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:31:01.441741"}, {"row_number": 109, "subject": "ICICI: Mend Platform Access", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:31:03.458976"}, {"row_number": 110, "subject": "ICICI: Solana: MSC Critical Security Event", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:31:05.473957"}, {"row_number": 111, "subject": "ICICI: Mend Platform Access", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:31:07.488547"}, {"row_number": 112, "subject": "ICICI: Mend Platform Access", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:31:09.509019"}, {"row_number": 113, "subject": "ICICI: Mend Platform Access", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:31:11.526097"}, {"row_number": 114, "subject": "ICICI: SAST Planned Downtime_copy", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:31:13.547803"}, {"row_number": 115, "subject": "ICICI: Mend Platform Access", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:31:15.560300"}, {"row_number": 116, "subject": "ICICI: Mend Platform Access", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:31:17.577301"}, {"row_number": 117, "subject": "ICICI: Mend Platform Access", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:31:19.589672"}, {"row_number": 118, "subject": "ICICI: SAST Planned Downtime", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:31:21.604525"}, {"row_number": 119, "subject": "ICICI: Mend Platform Access", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:31:23.622816"}, {"row_number": 120, "subject": "ICICI: <PERSON><PERSON><PERSON> to Delighted", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:31:25.634129"}, {"row_number": 121, "subject": "ICICI: NPS Send Date", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:31:27.648731"}, {"row_number": 122, "subject": "ICICI: NPS Last Sent", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:31:29.663758"}, {"row_number": 123, "subject": "ICICI: Mend Platform Access", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:31:31.677027"}, {"row_number": 124, "subject": "ICICI: November newsletter 2024", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:31:33.695195"}, {"row_number": 125, "subject": "ICICI: Mend Platform Access", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:31:35.711249"}, {"row_number": 126, "subject": "ICICI: <PERSON> <PERSON><PERSON><PERSON> (7d)", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:31:37.725161"}, {"row_number": 127, "subject": "ICICI: Mend Platform Access", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:31:39.743012"}, {"row_number": 128, "subject": "ICICI: <div><br>Upcoming NPS Survey&nbsp;</div>", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:31:41.757564"}, {"row_number": 129, "subject": "ICICI: October newsletter 2024", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:31:43.772115"}, {"row_number": 130, "subject": "ICICI: Risk Status", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:31:45.788032"}, {"row_number": 131, "subject": "ICICI: Primary Risk Reason", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:31:47.799316"}, {"row_number": 132, "subject": "ICICI: Risk Next Step(s)", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:31:49.814101"}, {"row_number": 133, "subject": "ICICI: Risk Status", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:31:51.830663"}, {"row_number": 134, "subject": "ICICI: Primary Risk Reason", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:31:53.841123"}, {"row_number": 135, "subject": "ICICI: Risk Next Step(s)", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:31:55.863199"}, {"row_number": 136, "subject": "ICICI: Support: IP Address Change", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:31:57.882991"}, {"row_number": 137, "subject": "ICICI: Business Model Launch", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:31:59.898549"}, {"row_number": 138, "subject": "ICICI: New Business Model_Webinar Follow up", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:32:01.920327"}, {"row_number": 139, "subject": "ICICI: MP Activity Frequency", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:32:03.938590"}, {"row_number": 140, "subject": "ICICI: <PERSON><PERSON>", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:32:05.957508"}, {"row_number": 141, "subject": "ICICI: New Business Model", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:32:07.969816"}, {"row_number": 142, "subject": "ICICI: Slack to #risk", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:32:09.993990"}, {"row_number": 143, "subject": "ICICI: Risk Next Step(s)", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:32:12.009190"}, {"row_number": 144, "subject": "ICICI: Primary Risk Reason", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:32:14.026350"}, {"row_number": 145, "subject": "ICICI: Risk Status", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:32:16.047447"}, {"row_number": 146, "subject": "ICICI: <PERSON><PERSON>", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:32:18.063383"}, {"row_number": 147, "subject": "ICICI: MP Activity Frequency", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:32:20.082756"}, {"row_number": 148, "subject": "ICICI: MP Activity Frequency", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:32:22.099828"}, {"row_number": 149, "subject": "ICICI: NPS Send Date", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:32:24.113438"}, {"row_number": 150, "subject": "ICICI: MP Activity Frequency", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:32:26.132200"}, {"row_number": 151, "subject": "ICICI: MP Activity", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:32:28.152188"}, {"row_number": 152, "subject": "ICICI: MP Activity Status", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:32:30.167551"}, {"row_number": 153, "subject": "ICICI: <PERSON><PERSON>", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:32:32.186163"}, {"row_number": 154, "subject": "ICICI: MP Activity Status", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:32:34.205821"}, {"row_number": 155, "subject": "ICICI: Product Roadmap H2 2024", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:32:36.221768"}, {"row_number": 156, "subject": "ICICI: <PERSON><PERSON><PERSON> to Delighted", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:32:38.237267"}, {"row_number": 157, "subject": "ICICI: NPS Last Sent", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:32:40.242626"}, {"row_number": 158, "subject": "ICICI: NPS Send Date", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:32:42.251553"}, {"row_number": 159, "subject": "ICICI: NPS Last Sent", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:32:44.277128"}, {"row_number": 160, "subject": "ICICI: NPS Send Date", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:32:46.294688"}, {"row_number": 161, "subject": "ICICI: <PERSON><PERSON><PERSON> to Delighted", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:32:48.311001"}, {"row_number": 162, "subject": "ICICI: Vulnerability Insights with MITRE Data", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:32:50.317107"}, {"row_number": 163, "subject": "ICICI: <PERSON><PERSON>", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:32:52.322653"}, {"row_number": 164, "subject": "ICICI: Mend Platform Access", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:32:54.331918"}, {"row_number": 165, "subject": "ICICI: CSM Managed", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:32:56.338225"}, {"row_number": 166, "subject": "ICICI: <div><br>Upcoming NPS Survey&nbsp;</div>", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:32:58.342925"}, {"row_number": 167, "subject": "ICICI: <PERSON><PERSON>", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:33:00.352674"}, {"row_number": 168, "subject": "ICICI: <PERSON><PERSON>", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:33:02.362514"}, {"row_number": 169, "subject": "ICICI: <PERSON><PERSON>", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:33:04.371467"}, {"row_number": 170, "subject": "ICICI: <PERSON><PERSON>", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:33:06.376458"}, {"row_number": 171, "subject": "ICICI: <PERSON> Follow Up Email", "activity_type": "Meeting", "error": "UI automation failed", "timestamp": "2025-05-29T21:33:08.385025"}, {"row_number": 172, "subject": "ICICI: <PERSON> Follow Up Email", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:33:10.391921"}, {"row_number": 173, "subject": "ICICI: <PERSON> Follow Up Email", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:33:12.399998"}, {"row_number": 174, "subject": "ICICI: Slack to #risk", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:33:14.408772"}, {"row_number": 175, "subject": "ICICI: <PERSON><PERSON>", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:33:16.412601"}, {"row_number": 176, "subject": "ICICI: <PERSON><PERSON><PERSON> - CSM Satisfaction Survey/Kelle Intro", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:33:18.420407"}, {"row_number": 177, "subject": "ICICI: <div><span class=\"mention-wrapper\" data-reactroot=\"\"><span class=\"field\">ICICI Bank</span>'s renewal date has passed</span></div>", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:33:20.428971"}, {"row_number": 178, "subject": "ICICI: Correction - CVE - 2024 - 3094_copy", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:33:22.434611"}, {"row_number": 179, "subject": "ICICI: <PERSON><PERSON>", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:33:24.443820"}, {"row_number": 180, "subject": "ICICI: All Other Customers - CVE - 2024 - 3094", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:33:26.452906"}, {"row_number": 181, "subject": "ICICI: <PERSON><PERSON>", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:33:28.460373"}, {"row_number": 182, "subject": "ICICI: License Utilization Status", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:33:30.465224"}, {"row_number": 183, "subject": "ICICI: License Utilization Status", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:33:32.474472"}, {"row_number": 184, "subject": "ICICI: <PERSON><PERSON>", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:33:34.480521"}, {"row_number": 185, "subject": "ICICI: <PERSON><PERSON>", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:33:36.501025"}, {"row_number": 186, "subject": "ICICI: <PERSON><PERSON>", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:33:38.518821"}, {"row_number": 187, "subject": "ICICI: <PERSON><PERSON>", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:33:40.542033"}, {"row_number": 188, "subject": "ICICI: Executive Engaged", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:33:42.564727"}, {"row_number": 189, "subject": "ICICI: AI Survey - Gold/ Platinum Customers - Follow Up", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:33:44.578613"}, {"row_number": 190, "subject": "ICICI: <PERSON><PERSON>", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:33:46.593380"}, {"row_number": 191, "subject": "ICICI: <PERSON><PERSON>", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:33:48.608957"}, {"row_number": 192, "subject": "ICICI: AI Survey - Int. Gold & Platinum Customers Oops_copy", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:33:50.618809"}, {"row_number": 193, "subject": "ICICI: AI Survey - Gold & Platinum Customers", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:33:52.628531"}, {"row_number": 194, "subject": "ICICI: <PERSON><PERSON>", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:33:54.638084"}, {"row_number": 195, "subject": "ICICI: <PERSON><PERSON>", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:33:56.647022"}, {"row_number": 196, "subject": "ICICI: <PERSON><PERSON>", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:33:58.655831"}, {"row_number": 197, "subject": "ICICI: <PERSON><PERSON>", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:34:00.664724"}, {"row_number": 198, "subject": "ICICI: Slack to #risk", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:34:02.673983"}, {"row_number": 199, "subject": "ICICI: Risk Update", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:34:04.682442"}, {"row_number": 200, "subject": "ICICI: Risk Status", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:34:06.691781"}, {"row_number": 201, "subject": "ICICI: <PERSON><PERSON>", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:34:08.696124"}, {"row_number": 202, "subject": "ICICI: <PERSON><PERSON>", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:34:10.705888"}, {"row_number": 203, "subject": "ICICI: NPS Send Date", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:34:12.714859"}, {"row_number": 204, "subject": "ICICI: NPS Informed", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:34:14.723500"}, {"row_number": 205, "subject": "ICICI: NPS Informed", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:34:16.732213"}, {"row_number": 206, "subject": "ICICI: Touch Status 2", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:34:18.740874"}, {"row_number": 207, "subject": "ICICI: <PERSON><PERSON>", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:34:20.750104"}, {"row_number": 208, "subject": "ICICI: <PERSON><PERSON>", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:34:22.758899"}, {"row_number": 209, "subject": "ICICI: <PERSON><PERSON>", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:34:24.767677"}, {"row_number": 210, "subject": "ICICI: <PERSON><PERSON>", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:34:26.776848"}, {"row_number": 211, "subject": "ICICI: <PERSON><PERSON>", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:34:28.797335"}, {"row_number": 212, "subject": "ICICI: <PERSON><PERSON>", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:34:30.807627"}, {"row_number": 213, "subject": "ICICI: Slack to #risk", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:34:32.816478"}, {"row_number": 214, "subject": "ICICI: Risk Update", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:34:34.825469"}, {"row_number": 215, "subject": "ICICI: Risk Status", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:34:36.834180"}, {"row_number": 216, "subject": "ICICI: <PERSON><PERSON>", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:34:38.841594"}, {"row_number": 217, "subject": "ICICI: <PERSON><PERSON>", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:34:40.851730"}, {"row_number": 218, "subject": "ICICI: <PERSON><PERSON>", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:34:42.860904"}, {"row_number": 219, "subject": "ICICI: <PERSON><PERSON>", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:34:44.870342"}, {"row_number": 220, "subject": "ICICI: <PERSON><PERSON>", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:34:46.879435"}, {"row_number": 221, "subject": "ICICI: <PERSON><PERSON>", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:34:48.885222"}, {"row_number": 222, "subject": "ICICI: <PERSON><PERSON>", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:34:50.894913"}, {"row_number": 223, "subject": "ICICI: <PERSON><PERSON>", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:34:52.903719"}, {"row_number": 224, "subject": "ICICI: <PERSON><PERSON>", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:34:54.912438"}, {"row_number": 225, "subject": "ICICI: <PERSON><PERSON>", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:34:56.921312"}, {"row_number": 226, "subject": "ICICI: <PERSON><PERSON>", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:34:58.929619"}, {"row_number": 227, "subject": "ICICI: ICICI Bank <> Mend - Catch Up Meeting External", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:35:00.938554"}, {"row_number": 228, "subject": "ICICI: <PERSON><PERSON>", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:35:02.945209"}, {"row_number": 229, "subject": "ICICI: SCA CD Variance Stages", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:35:04.954462"}, {"row_number": 230, "subject": "ICICI: License Utilization Status", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:35:06.963498"}, {"row_number": 231, "subject": "ICICI: Mend Vulnerability found by WithSecure", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:35:08.972327"}, {"row_number": 232, "subject": "ICICI: ICICI Bank <> Mend - Catch Up Meeting", "activity_type": "Meeting", "error": "UI automation failed", "timestamp": "2025-05-29T21:35:10.981562"}, {"row_number": 233, "subject": "ICICI: ICICI Bank<>Mend.io organization migration from LBA to VBA", "activity_type": "Meeting", "error": "UI automation failed", "timestamp": "2025-05-29T21:35:12.989320"}, {"row_number": 234, "subject": "ICICI: <PERSON><PERSON>", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:35:14.997986"}, {"row_number": 235, "subject": "ICICI: No need to touch base", "activity_type": "Meeting", "error": "UI automation failed", "timestamp": "2025-05-29T21:35:17.007572"}, {"row_number": 236, "subject": "ICICI: Unified Agent Hotfix now available-due to Java upgrade issue", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:35:19.017198"}, {"row_number": 237, "subject": "ICICI: Unified Agent - Java upgrade issue", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:35:21.025981"}, {"row_number": 238, "subject": "ICICI: LBA email change sent to the partner", "activity_type": "Meeting", "error": "UI automation failed", "timestamp": "2025-05-29T21:35:23.034620"}, {"row_number": 239, "subject": "ICICI: ICICI Bank<>MEND", "activity_type": "Meeting", "error": "UI automation failed", "timestamp": "2025-05-29T21:35:25.050301"}, {"row_number": 240, "subject": "ICICI: <PERSON><PERSON>", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:35:27.059429"}, {"row_number": 241, "subject": "ICICI: <PERSON><PERSON>", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:35:29.068476"}, {"row_number": 242, "subject": "ICICI: Request for Developer Training for ICICI", "activity_type": "Meeting", "error": "UI automation failed", "timestamp": "2025-05-29T21:35:31.078669"}, {"row_number": 243, "subject": "ICICI: Re: Queries on Licensing Policy related details - ICICI Bank", "activity_type": "Email", "error": "UI automation failed", "timestamp": "2025-05-29T21:35:33.087561"}, {"row_number": 244, "subject": "ICICI: Re: Mend integration with LDAP and SAML - ICICI", "activity_type": "Email", "error": "UI automation failed", "timestamp": "2025-05-29T21:35:35.096642"}, {"row_number": 245, "subject": "ICICI: <PERSON><PERSON>", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:35:37.105265"}, {"row_number": 246, "subject": "ICICI: NPS Send Date", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:35:39.114945"}, {"row_number": 247, "subject": "ICICI: NPS Informed", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:35:41.124247"}, {"row_number": 248, "subject": "ICICI: <PERSON><PERSON>", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:35:43.133227"}, {"row_number": 249, "subject": "ICICI: <PERSON><PERSON>", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:35:45.142171"}, {"row_number": 250, "subject": "ICICI: No need to touch base- working with the partner", "activity_type": "Meeting", "error": "UI automation failed", "timestamp": "2025-05-29T21:35:47.151451"}, {"row_number": 251, "subject": "ICICI: Inform Key Contacts of upcoming NPS survey", "activity_type": "Meeting", "error": "UI automation failed", "timestamp": "2025-05-29T21:35:49.158903"}, {"row_number": 252, "subject": "ICICI: <PERSON> is working to arrange a meeting with the bank and partner", "activity_type": "Meeting", "error": "UI automation failed", "timestamp": "2025-05-29T21:35:51.168391"}, {"row_number": 253, "subject": "ICICI: <PERSON><PERSON>", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:35:53.177456"}, {"row_number": 254, "subject": "ICICI: <PERSON><PERSON>", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:35:55.187219"}, {"row_number": 255, "subject": "ICICI: Discussion with ICICI Team - Mend - WhiteSource !!", "activity_type": "Meeting", "error": "UI automation failed", "timestamp": "2025-05-29T21:35:57.196187"}, {"row_number": 256, "subject": "ICICI: <PERSON><PERSON>", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:35:59.204908"}, {"row_number": 257, "subject": "ICICI: Update from Luis- partner manager", "activity_type": "Meeting", "error": "UI automation failed", "timestamp": "2025-05-29T21:36:01.212920"}, {"row_number": 258, "subject": "ICICI: <PERSON><PERSON>", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:36:03.221918"}, {"row_number": 259, "subject": "ICICI: Repo Integration-Accelerate your remediation", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:36:05.230516"}, {"row_number": 260, "subject": "ICICI: RSA 2023 conference", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:36:07.239762"}, {"row_number": 261, "subject": "ICICI: <PERSON><PERSON>", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:36:09.248316"}, {"row_number": 262, "subject": "ICICI: FEE ticket in place", "activity_type": "Meeting", "error": "UI automation failed", "timestamp": "2025-05-29T21:36:11.257916"}, {"row_number": 263, "subject": "ICICI: FEE ticket in place", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:36:13.266861"}, {"row_number": 264, "subject": "ICICI: FEE ticket in place", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:36:15.275444"}, {"row_number": 265, "subject": "ICICI: Discussion with ICICI Team - Mend - WhiteSource !!", "activity_type": "Meeting", "error": "UI automation failed", "timestamp": "2025-05-29T21:36:17.284565"}, {"row_number": 266, "subject": "ICICI: ICICI/Meteonic - Next Steps & Alignment", "activity_type": "Meeting", "error": "UI automation failed", "timestamp": "2025-05-29T21:36:19.291929"}, {"row_number": 267, "subject": "ICICI: Sunny to set up a joint meeting?", "activity_type": "Meeting", "error": "UI automation failed", "timestamp": "2025-05-29T21:36:21.300744"}, {"row_number": 268, "subject": "ICICI: Sunny to set up a joint meeting?", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:36:23.309466"}, {"row_number": 269, "subject": "ICICI: <PERSON><PERSON>", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:36:25.318009"}, {"row_number": 270, "subject": "ICICI: Sales Manager Region", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:36:27.327256"}, {"row_number": 271, "subject": "ICICI: SCA CD Variance Stages", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:36:29.335636"}, {"row_number": 272, "subject": "ICICI: <PERSON><PERSON>", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:36:31.356496"}, {"row_number": 273, "subject": "ICICI: Sunny to set up a joint meeting?", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:36:33.371810"}, {"row_number": 274, "subject": "ICICI: ********- zoom discussions in place", "activity_type": "Meeting", "error": "UI automation failed", "timestamp": "2025-05-29T21:36:35.391384"}, {"row_number": 275, "subject": "ICICI: Mend/Meteonic sync", "activity_type": "Meeting", "error": "UI automation failed", "timestamp": "2025-05-29T21:36:37.406793"}, {"row_number": 276, "subject": "ICICI: Case#********- call with Eng", "activity_type": "Meeting", "error": "UI automation failed", "timestamp": "2025-05-29T21:36:39.419218"}, {"row_number": 277, "subject": "ICICI: Working to set a meting with a the bank and the BP", "activity_type": "Meeting", "error": "UI automation failed", "timestamp": "2025-05-29T21:36:41.437343"}, {"row_number": 278, "subject": "ICICI: Meteonic will do onsite in the bank on Jan25th", "activity_type": "Meeting", "error": "UI automation failed", "timestamp": "2025-05-29T21:36:43.458117"}, {"row_number": 279, "subject": "ICICI: Mend's Malicious Package Communications", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:36:45.476162"}, {"row_number": 280, "subject": "ICICI: No need to touch base", "activity_type": "Meeting", "error": "UI automation failed", "timestamp": "2025-05-29T21:36:47.493651"}, {"row_number": 281, "subject": "ICICI: NPS Send Date", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:36:49.511501"}, {"row_number": 282, "subject": "ICICI: NPS Informed", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:36:51.531790"}, {"row_number": 283, "subject": "ICICI: NPS Informed", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:36:53.549347"}, {"row_number": 284, "subject": "ICICI: Internal sync with AM", "activity_type": "Meeting", "error": "UI automation failed", "timestamp": "2025-05-29T21:36:55.565683"}, {"row_number": 285, "subject": "ICICI: Touch Status", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:36:57.583262"}, {"row_number": 286, "subject": "ICICI: NPS Informed", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:36:59.596822"}, {"row_number": 287, "subject": "ICICI: Sales Manager Region", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:37:01.612160"}, {"row_number": 288, "subject": "ICICI: Team Manager Region", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:37:03.629988"}, {"row_number": 289, "subject": "ICICI: Team Manager", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:37:05.647035"}, {"row_number": 290, "subject": "ICICI: SCA CD Variance Stages", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:37:07.662507"}, {"row_number": 291, "subject": "ICICI: Setup a call with customers and explains our Policies best practice", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:37:09.680109"}, {"row_number": 292, "subject": "ICICI: Touch Status", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:37:11.699808"}, {"row_number": 293, "subject": "ICICI: Setup a call with customers and explains our Policies best practice", "activity_type": "Email", "error": "UI automation failed", "timestamp": "2025-05-29T21:37:13.715798"}, {"row_number": 294, "subject": "ICICI: Setup a call with customers and explains our Policies best practice", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:37:15.731674"}, {"row_number": 295, "subject": "ICICI: Outage in app.whitesourcesoftware.com", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:37:17.750515"}, {"row_number": 296, "subject": "ICICI: Touch Status", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:37:19.766539"}, {"row_number": 297, "subject": "ICICI: Check the usage and project number", "activity_type": "Meeting", "error": "UI automation failed", "timestamp": "2025-05-29T21:37:21.787601"}, {"row_number": 298, "subject": "ICICI: CSAT - Spring4Shell, Platinum&Gold", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:37:23.796801"}, {"row_number": 299, "subject": "ICICI: Touch Status", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:37:25.814857"}, {"row_number": 300, "subject": "ICICI: Team Manager Region", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:37:27.842319"}, {"row_number": 301, "subject": "ICICI: Spring4Shell HIGH SEVERITY Vulnerability in Spring core - Gold & Platinum", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:37:29.862917"}, {"row_number": 302, "subject": "ICICI: <PERSON><PERSON>", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:37:31.881109"}, {"row_number": 303, "subject": "ICICI: Touch Status", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:37:33.900139"}, {"row_number": 304, "subject": "ICICI: VBA migration", "activity_type": "Meeting", "error": "UI automation failed", "timestamp": "2025-05-29T21:37:35.910137"}, {"row_number": 305, "subject": "ICICI: Plz check with the usage is low", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:37:37.929363"}, {"row_number": 306, "subject": "ICICI: March 2022 Newsletter- Dedicated", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:37:39.945194"}, {"row_number": 307, "subject": "ICICI: <PERSON><PERSON>", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:37:41.961244"}, {"row_number": 308, "subject": "ICICI: BP customer- no touch base is needed", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:37:43.977602"}, {"row_number": 309, "subject": "ICICI: Touch Status", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:37:45.994922"}, {"row_number": 310, "subject": "ICICI: Customer Communication Regarding LBA to VBA Migration - SCA-ICICI Bank - for SCA-ICICI Bank", "activity_type": "Email", "error": "UI automation failed", "timestamp": "2025-05-29T21:37:48.014857"}, {"row_number": 311, "subject": "ICICI: Team Manager", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:37:50.035056"}, {"row_number": 312, "subject": "ICICI: <PERSON><PERSON>", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:37:52.053244"}, {"row_number": 313, "subject": "ICICI: Log4j Vulnerability webinar Dedicated CSM", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:37:54.068302"}, {"row_number": 314, "subject": "ICICI: Vulnerability in Apache Log4j2 library Part 2  Dedicated CSM Accounts", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:37:56.085286"}, {"row_number": 315, "subject": "ICICI: Severity Vulnerability (CVE-2021-44228) in the Log4j2 -Dedicated CSM", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:37:58.098774"}, {"row_number": 316, "subject": "ICICI: Customer Communication Regarding LBA to VBA Migration - SCA-ICICI Bank", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:38:00.111179"}, {"row_number": 317, "subject": "ICICI: Contact SCA-ICICI Bank about moving to VBA", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:38:02.125536"}, {"row_number": 318, "subject": "ICICI: <PERSON><PERSON>", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:38:04.147315"}, {"row_number": 319, "subject": "ICICI: SCA CD Variance Stages", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:38:06.162729"}, {"row_number": 320, "subject": "ICICI: Partner account", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:38:08.177781"}, {"row_number": 321, "subject": "ICICI: Sales Manager Region", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:38:10.192911"}, {"row_number": 322, "subject": "ICICI: <PERSON><PERSON>", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:38:12.214529"}]
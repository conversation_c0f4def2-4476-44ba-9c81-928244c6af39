#!/usr/bin/env python3
"""
🐺 Wild Weasel - FIXED Totango Data Analysis & Email Activity Extraction
================================================================================
Mission: Extract ONLY actual email activities using proper ID mapping from Totango timeline data
Target: Prepare structured data for Gainsight migration

CRITICAL FIX APPLIED:
- Email activity type ID 68c0d13a-40f1-47e4-9bb4-3d1fb6a16515 is stored in 'meeting_type' field, NOT 'activity_type_id'
- This corrects the issue where 46 activities were being identified instead of the correct 4

FIXED ISSUES:
1. Proper email identification using meeting_type field from ICICI.json
2. Correct touchpoint_reason.json usage
3. Accurate data transformation for Gainsight API format
"""

import json
import os
import sys
from datetime import datetime
from typing import List, Dict, Any
import logging

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger("WildWeasel-FixedAnalyzer")

class TotangoEmailExtractorFixed:
    """FIXED: Extract and transform email activities from Totango data using proper meeting_type field mapping"""
    
    def __init__(self, totango_data_dir: str = "/Users/<USER>/Desktop/Totango"):
        self.totango_data_dir = totango_data_dir
        self.icici_file = os.path.join(totango_data_dir, "ICICI.json")
        self.id_mapping_file = os.path.join(totango_data_dir, "ID.json")
        self.touchpoint_reasons_file = os.path.join(totango_data_dir, "Touchpoint_reason.JSON")
        
        # Email activity type ID from ID.json (display_name = "Email")
        self.email_activity_type_id = "68c0d13a-40f1-47e4-9bb4-3d1fb6a16515"
        
    def load_supporting_data(self) -> tuple:
        """Load ID mappings and touchpoint reasons with proper validation"""
        id_mappings = {}
        touchpoint_reasons = {}
        
        try:
            if os.path.exists(self.id_mapping_file):
                with open(self.id_mapping_file, 'r') as f:
                    id_data = json.load(f)
                    # Convert to dict with id as key for easy lookup
                    for item in id_data:
                        id_mappings[item['id']] = item
                logger.info(f"✅ Loaded {len(id_mappings)} ID mappings")
                
                # Verify email activity type exists
                if self.email_activity_type_id in id_mappings:
                    email_type = id_mappings[self.email_activity_type_id]
                    logger.info(f"🎯 Found Email activity type: {email_type['display_name']} (Used: {email_type.get('used', 'N/A')})")
                else:
                    logger.warning(f"⚠️ Email activity type ID {self.email_activity_type_id} not found in ID.json")
                    
        except Exception as e:
            logger.error(f"❌ Could not load ID mappings: {e}")
            
        try:
            if os.path.exists(self.touchpoint_reasons_file):
                with open(self.touchpoint_reasons_file, 'r') as f:
                    touchpoint_data = json.load(f)
                    # Convert to dict with id as key for easy lookup
                    for item in touchpoint_data:
                        touchpoint_reasons[item['id']] = item
                logger.info(f"✅ Loaded {len(touchpoint_reasons)} touchpoint reasons")
        except Exception as e:
            logger.error(f"❌ Could not load touchpoint reasons: {e}")
            
        return id_mappings, touchpoint_reasons
    
    def is_email_activity_correct(self, activity: Dict[str, Any], id_mappings: Dict) -> bool:
        """FIXED: Properly determine if a Totango activity is email-related using correct logic"""
        properties = activity.get('properties', {})
        
        # CORRECTED METHOD: Check if meeting_type contains the email activity type ID from ID.json
        meeting_type = properties.get('meeting_type', '')
        if meeting_type == self.email_activity_type_id:
            logger.debug(f"✅ Found TRUE email via meeting_type: {meeting_type}")
            return True
        
        # Additional verification: Check if activity_type_id maps to "Email" in ID.json
        activity_type_id = properties.get('activity_type_id', '')
        if activity_type_id and activity_type_id in id_mappings:
            mapping = id_mappings[activity_type_id]
            if mapping.get('display_name', '').lower() == 'email':
                logger.debug(f"✅ Found email via activity_type_id mapping: {activity_type_id} -> {mapping['display_name']}")
                return True
        
        return False
    
    def transform_to_gainsight_format(self, activity: Dict[str, Any], 
                                    id_mappings: Dict, touchpoint_reasons: Dict) -> Dict[str, Any]:
        """Transform Totango activity to proper Gainsight API format"""
        properties = activity.get('properties', {})
        
        # Convert timestamp to ISO format
        timestamp_ms = activity.get('timestamp', 0)
        if timestamp_ms:
            activity_date = datetime.fromtimestamp(timestamp_ms / 1000).isoformat()
        else:
            activity_date = datetime.now().isoformat()
        
        # Extract subject with priority order and cleaning
        subject = (properties.get('subject') or 
                  properties.get('name') or 
                  properties.get('title') or 
                  f"Email Activity - {activity.get('type', 'Unknown')}")
        
        # Clean subject (remove "Totango Campaign:" prefix if present)
        if subject.startswith('Totango Campaign:'):
            subject = subject.replace('Totango Campaign:', '').strip()
        
        # Extract content/description
        content = (properties.get('description') or 
                  properties.get('content') or 
                  properties.get('notes') or
                  f"Email activity migrated from Totango - Type: {activity.get('type')}")
                  
        # Add additional context for campaigns
        if activity.get('type') == 'campaign_touch':
            targeted_users = properties.get('targeted_users_count', 0)
            campaign_type = properties.get('campaign_schedule_type', 'Unknown')
            if targeted_users:
                content += f"\n\nCampaign Details:\n- Recipients: {targeted_users} users\n- Type: {campaign_type}"
        
        # Determine touchpoint reason using corrected mapping
        touchpoint_reason = self.map_touchpoint_reason_correct(activity, touchpoint_reasons)
        
        # Extract author information
        author_email = properties.get('from_user', '')
        if not author_email:
            # For campaign touches, use system email
            if activity.get('type') == 'campaign_touch':
                author_email = "<EMAIL>"
            else:
                author_email = "<EMAIL>"
        
        # Extract attendees information
        internal_attendees = []
        external_attendees = []
        
        # Check enrichedUsers for attendee information
        enriched_users = activity.get('enrichedUsers', [])
        for user in enriched_users:
            email = user.get('email', '')
            name = user.get('name', '')
            if email:
                if email.endswith('@mend.io') or email.endswith('@whitesourcesoftware.com'):
                    internal_attendees.append(f"{name} <{email}>" if name else email)
                else:
                    external_attendees.append(f"{name} <{email}>" if name else email)
        
        # Build Gainsight Timeline API compatible payload
        gainsight_activity = {
            "records": [
                {
                    "ContextName": "Company",
                    "TypeName": "Email",
                    "ExternalId": activity['company']['id'] if 'company' in activity else "0015p00005R7ysqAAB",
                    "Subject": subject[:255],  # Gainsight field limit
                    "Notes": content,
                    "ActivityDate": activity_date,
                    "Author": author_email,
                    # Custom fields for additional data
                    "companyExternalId": activity['company']['id'] if 'company' in activity else "0015p00005R7ysqAAB",
                    "internalAttendees": internal_attendees,
                    "externalAttendees": external_attendees,
                    # Add custom fields as needed by Gainsight instance
                    "customField_TouchpointReason": touchpoint_reason,
                    "customField_OriginalSource": "Totango Migration",
                    "customField_OriginalType": activity.get('type', ''),
                    "customField_OriginalId": activity.get('id', ''),
                    "customField_MigrationTimestamp": datetime.now().isoformat()
                }
            ],
            "lookups": {
                "AuthorId": {
                    "fields": {
                        "Author": "Email"
                    },
                    "lookupField": "Gsid",
                    "objectName": "GsUser",
                    "multiMatchOption": "FIRSTMATCH",
                    "onNoMatch": "ERROR"
                },
                "GsCompanyId": {
                    "fields": {
                        "companyExternalId": "ExternalId"
                    },
                    "lookupField": "Gsid",
                    "objectName": "Company",
                    "multiMatchOption": "FIRSTMATCH",
                    "onNoMatch": "ERROR"
                }
            },
            # Metadata for our tracking
            "metadata": {
                "totango_original": activity,
                "migration_source": "Wild Weasel v2.0",
                "processing_timestamp": datetime.now().isoformat()
            }
        }
        
        return gainsight_activity
    
    def map_touchpoint_reason_correct(self, activity: Dict[str, Any], touchpoint_reasons: Dict) -> str:
        """FIXED: Correctly map Totango activity to Gainsight touchpoint reason"""
        properties = activity.get('properties', {})
        activity_type = activity.get('type', '')
        
        # CORRECTED METHOD: Get meeting_type (where email ID is stored) from properties 
        meeting_type = properties.get('meeting_type', '')
        
        # First, check if meeting_type maps to a touchpoint reason
        if meeting_type and meeting_type in touchpoint_reasons:
            reason_data = touchpoint_reasons[meeting_type]
            return reason_data.get('display_name', 'COMMUNICATION')
        
        # Fallback: Check activity_type_id for touchpoint mapping
        activity_type_id = properties.get('activity_type_id', '')
        if activity_type_id and activity_type_id in touchpoint_reasons:
            reason_data = touchpoint_reasons[activity_type_id]
            return reason_data.get('display_name', 'COMMUNICATION')
        
        # Default for email activities
        return 'COMMUNICATION'
    
    def extract_email_activities_fixed(self) -> List[Dict[str, Any]]:
        """FIXED: Main extraction method using proper ID mapping logic"""
        logger.info("🐺 Wild Weasel FIXED Email Extraction Starting...")
        
        # Load main data
        if not os.path.exists(self.icici_file):
            logger.error(f"❌ ICICI data file not found: {self.icici_file}")
            return []
            
        with open(self.icici_file, 'r') as f:
            all_activities = json.load(f)
            
        logger.info(f"📊 Loaded {len(all_activities)} total Totango activities")
        
        # Load supporting data with validation
        id_mappings, touchpoint_reasons = self.load_supporting_data()
        
        if not id_mappings:
            logger.warning("⚠️ No ID mappings loaded - email identification may be limited")
        
        # Filter email activities using FIXED logic
        email_activities = []
        activity_type_breakdown = {}
        
        for activity in all_activities:
            activity_type = activity.get('type', 'unknown')
            activity_type_breakdown[activity_type] = activity_type_breakdown.get(activity_type, 0) + 1
            
            if self.is_email_activity_correct(activity, id_mappings):
                email_activities.append(activity)
                
        logger.info(f"🎯 Activity Type Breakdown:")
        for atype, count in sorted(activity_type_breakdown.items()):
            logger.info(f"   {atype}: {count}")
                
        logger.info(f"✅ Found {len(email_activities)} ACTUAL email activities using CORRECT ID mapping (meeting_type field)")
        logger.info(f"📊 Previous broad logic was incorrectly finding {len([a for a in all_activities if self.is_old_email_logic(a)])} activities")
        
        # Transform to Gainsight format
        gainsight_activities = []
        for activity in email_activities:
            try:
                transformed = self.transform_to_gainsight_format(activity, id_mappings, touchpoint_reasons)
                gainsight_activities.append(transformed)
            except Exception as e:
                logger.error(f"❌ Error transforming activity {activity.get('id', 'unknown')}: {e}")
                
        logger.info(f"🚀 Successfully transformed {len(gainsight_activities)} activities for Gainsight API")
        
        return gainsight_activities
    
    def is_old_email_logic(self, activity: Dict[str, Any]) -> bool:
        """Old logic for comparison - identifies too many activities as emails"""
        activity_type = activity.get('type', '')
        properties = activity.get('properties', {})
        
        # Old broad logic that was finding too many
        email_types = ['campaign_touch', 'account_alert']
        
        if activity_type in email_types:
            return True
            
        subject = properties.get('subject', '').lower()
        name = properties.get('name', '').lower()
        description = properties.get('description', '').lower()
        
        email_indicators = [
            'newsletter', 'campaign', 'email', 'survey', 'alert',
            'notification', 'update', 'announcement'
        ]
        
        text_to_check = f"{subject} {name} {description}".lower()
        return any(indicator in text_to_check for indicator in email_indicators)
    
    def save_extracted_data(self, activities: List[Dict[str, Any]], output_file: str):
        """Save extracted email activities to JSON file"""
        with open(output_file, 'w') as f:
            json.dump(activities, f, indent=2, default=str)
        logger.info(f"💾 Saved {len(activities)} email activities to {output_file}")
    
    def generate_detailed_report(self, activities: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Generate comprehensive analysis report"""
        if not activities:
            return {"error": "No activities to analyze"}
            
        # Analyze activities by various dimensions
        touchpoint_breakdown = {}
        date_range = {"earliest": None, "latest": None}
        author_breakdown = {}
        activity_type_breakdown = {}
        
        for activity_wrapper in activities:
            # Extract the actual activity record
            if 'records' in activity_wrapper and activity_wrapper['records']:
                activity = activity_wrapper['records'][0]
            else:
                continue
                
            # Touchpoint reasons
            reason = activity.get('customField_TouchpointReason', 'Unknown')
            touchpoint_breakdown[reason] = touchpoint_breakdown.get(reason, 0) + 1
            
            # Authors
            author = activity.get('Author', 'Unknown')
            author_breakdown[author] = author_breakdown.get(author, 0) + 1
            
            # Original activity types
            orig_type = activity.get('customField_OriginalType', 'Unknown')
            activity_type_breakdown[orig_type] = activity_type_breakdown.get(orig_type, 0) + 1
            
            # Date range
            activity_date = activity.get('ActivityDate')
            if activity_date:
                if not date_range["earliest"] or activity_date < date_range["earliest"]:
                    date_range["earliest"] = activity_date
                if not date_range["latest"] or activity_date > date_range["latest"]:
                    date_range["latest"] = activity_date
        
        return {
            "total_activities": len(activities),
            "date_range": date_range,
            "touchpoint_breakdown": touchpoint_breakdown,
            "author_breakdown": author_breakdown,
            "activity_type_breakdown": activity_type_breakdown,
            "company_id": activities[0]['records'][0].get('ExternalId') if activities and 'records' in activities[0] and activities[0]['records'] else None,
            "ready_for_migration": len(activities) > 0,
            "api_ready_format": True
        }

def main():
    """Main execution function"""
    extractor = TotangoEmailExtractorFixed()
    
    # Extract email activities using FIXED logic
    email_activities = extractor.extract_email_activities_fixed()
    
    if not email_activities:
        logger.error("❌ No email activities found - check data sources and ID mappings")
        sys.exit(1)
    
    # Save extracted data
    output_file = "extracted_email_activities_fixed.json"
    extractor.save_extracted_data(email_activities, output_file)
    
    # Generate and display detailed report
    summary = extractor.generate_detailed_report(email_activities)
    
    print("\n" + "="*80)
    print("🐺 WILD WEASEL - FIXED EMAIL EXTRACTION SUMMARY")
    print("="*80)
    print(f"✅ Total Email Activities: {summary['total_activities']}")
    print(f"📅 Date Range: {summary['date_range']['earliest']} to {summary['date_range']['latest']}")
    print(f"🏢 Company ID: {summary['company_id']}")
    print(f"📁 Output File: {output_file}")
    print(f"🔗 API Ready Format: {summary['api_ready_format']}")
    
    print(f"\n📊 Touchpoint Breakdown:")
    for reason, count in summary['touchpoint_breakdown'].items():
        print(f"   {reason}: {count}")
        
    print(f"\n👤 Author Breakdown:")
    for author, count in summary['author_breakdown'].items():
        print(f"   {author}: {count}")
        
    print(f"\n🔄 Original Type Breakdown:")
    for orig_type, count in summary['activity_type_breakdown'].items():
        print(f"   {orig_type}: {count}")
    
    print(f"\n🎯 Migration Status: {'✅ Ready for Gainsight API' if summary['ready_for_migration'] else '❌ Not Ready'}")
    print("="*80)
    
    # Save summary report
    with open("extraction_summary_fixed.json", "w") as f:
        json.dump(summary, f, indent=2, default=str)
    
    logger.info("🐺 Wild Weasel FIXED Email Extraction Complete!")

if __name__ == "__main__":
    main()

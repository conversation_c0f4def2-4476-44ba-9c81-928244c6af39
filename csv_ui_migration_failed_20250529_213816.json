[{"row_number": 1, "subject": "ICICI: Mend Platform Access", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:23:48.622789"}, {"row_number": 2, "subject": "ICICI: Mend Platform Access", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:25:54.651630"}, {"row_number": 3, "subject": "ICICI: Mend Platform Access", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:27:27.131417"}, {"row_number": 4, "subject": "ICICI: Mend Platform Access", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:27:29.150114"}, {"row_number": 5, "subject": "ICICI: Mend Platform Access", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:27:31.164892"}, {"row_number": 6, "subject": "ICICI: Mend Platform Access", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:27:33.182299"}, {"row_number": 7, "subject": "ICICI: <PERSON><PERSON><PERSON> to Delighted", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:27:35.200638"}, {"row_number": 8, "subject": "ICICI: NPS Send Date", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:27:37.218675"}, {"row_number": 9, "subject": "ICICI: NPS Last Sent", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:27:39.234305"}, {"row_number": 10, "subject": "ICICI: Mend Platform Access", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:27:41.246174"}, {"row_number": 11, "subject": "ICICI: May Newsletter: AI Risk Insights, Smarter Compliance and Cloud Integration!", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:27:43.263796"}, {"row_number": 12, "subject": "ICICI: Mend Platform Access", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:27:45.282870"}, {"row_number": 13, "subject": "ICICI: Mend Platform Access", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:27:47.289778"}, {"row_number": 14, "subject": "ICICI: MP Activity Frequency", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:27:49.296438"}, {"row_number": 15, "subject": "ICICI: <PERSON> <PERSON><PERSON><PERSON> (7d)", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:27:51.301856"}, {"row_number": 16, "subject": "ICICI: Mend Platform Access", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:27:53.308879"}, {"row_number": 17, "subject": "ICICI: SCA CD Variance Stages", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:27:55.314164"}, {"row_number": 18, "subject": "ICICI: <div><br>Upcoming NPS Survey&nbsp;</div>", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:27:57.320437"}, {"row_number": 19, "subject": "ICICI: Mend Platform Access", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:27:59.338748"}, {"row_number": 20, "subject": "ICICI: MP Activity", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:28:01.357506"}, {"row_number": 21, "subject": "ICICI: MP Activity Frequency", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:28:03.374734"}, {"row_number": 22, "subject": "ICICI: MITRE", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:28:05.383519"}, {"row_number": 23, "subject": "ICICI: MP Abandonment Date", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:28:07.400651"}, {"row_number": 24, "subject": "ICICI: MP Activity", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:28:09.409782"}, {"row_number": 25, "subject": "ICICI: Mend Platform Access", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:28:11.417480"}, {"row_number": 26, "subject": "ICICI: Mend Platform Access", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:28:13.426316"}, {"row_number": 27, "subject": "ICICI: April Newsletter: New Dashboard, Mend AI, and More!", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:28:15.430345"}, {"row_number": 28, "subject": "ICICI: Mend Platform Access", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:28:17.438173"}, {"row_number": 29, "subject": "ICICI: Mend Platform Access", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:28:19.446608"}, {"row_number": 30, "subject": "ICICI: Mend Platform Access", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:28:21.455099"}, {"row_number": 31, "subject": "ICICI: MP Activity Frequency", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:28:23.463358"}, {"row_number": 32, "subject": "ICICI: MP Activity", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:28:25.472254"}, {"row_number": 33, "subject": "ICICI: <PERSON> Newsletter: Introducing Mend AI and More!", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:28:27.482928"}, {"row_number": 34, "subject": "ICICI: MP Abandonment Date", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:28:29.497799"}, {"row_number": 35, "subject": "ICICI: MP Activity", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:28:31.513961"}, {"row_number": 36, "subject": "ICICI: MP Activity Frequency", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:28:33.522324"}, {"row_number": 37, "subject": "ICICI: Mend Platform Access", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:28:35.531337"}, {"row_number": 38, "subject": "ICICI: MP Activity Frequency", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:28:37.541108"}, {"row_number": 39, "subject": "ICICI: <PERSON> <PERSON><PERSON><PERSON> (7d)", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:28:39.558981"}, {"row_number": 40, "subject": "ICICI: Mend Platform Access", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:28:41.577321"}, {"row_number": 41, "subject": "ICICI: Active - CN", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:28:43.596101"}, {"row_number": 42, "subject": "ICICI: Active - SAST", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:28:45.618056"}, {"row_number": 43, "subject": "ICICI: Active - SCA", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:28:47.643133"}, {"row_number": 44, "subject": "ICICI: Mend Platform Access", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:28:49.665161"}, {"row_number": 45, "subject": "ICICI: Invicti Campaign", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:28:51.681867"}, {"row_number": 46, "subject": "ICICI: MP Activity", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:28:53.697878"}, {"row_number": 47, "subject": "ICICI: Mend Platform Access", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:28:55.705785"}, {"row_number": 48, "subject": "ICICI: MP Abandonment Date", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:28:57.718964"}, {"row_number": 49, "subject": "ICICI: MP Activity", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:28:59.739267"}, {"row_number": 50, "subject": "ICICI: MP Activity", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:29:01.759361"}, {"row_number": 51, "subject": "ICICI: February Newsletter: AI-Powered Code Remediation and More!", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:29:03.778699"}, {"row_number": 52, "subject": "ICICI: MP Abandonment Date", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:29:05.788472"}, {"row_number": 53, "subject": "ICICI: MP Activity", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:29:07.810173"}, {"row_number": 54, "subject": "ICICI: Mend Platform Access", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:29:09.818674"}, {"row_number": 55, "subject": "ICICI: MP Activity", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:29:11.829216"}, {"row_number": 56, "subject": "ICICI: Mend Platform Access", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:29:13.849330"}, {"row_number": 57, "subject": "ICICI: MP Abandonment Date", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:29:15.866950"}, {"row_number": 58, "subject": "ICICI: MP Activity", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:29:17.888541"}, {"row_number": 59, "subject": "ICICI: MP Activity", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:29:19.908050"}, {"row_number": 60, "subject": "ICICI: MP Activity Frequency", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:29:21.924139"}, {"row_number": 61, "subject": "ICICI: SAST Planned Downtime_App_copy", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:29:23.939941"}, {"row_number": 62, "subject": "ICICI: Mend Platform Access", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:29:25.958986"}, {"row_number": 63, "subject": "ICICI: MP Abandonment Date", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:29:27.977182"}, {"row_number": 64, "subject": "ICICI: MP Activity", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:29:29.989859"}, {"row_number": 65, "subject": "ICICI: MP Activity", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:29:32.001528"}, {"row_number": 66, "subject": "ICICI: MP Abandonment Date", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:29:34.006579"}, {"row_number": 67, "subject": "ICICI: MP Activity", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:29:36.025410"}, {"row_number": 68, "subject": "ICICI: AI Design Partners", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:29:38.042863"}, {"row_number": 69, "subject": "ICICI: Mend Platform Access", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:29:40.060900"}, {"row_number": 70, "subject": "ICICI: MP Activity", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:29:42.074574"}, {"row_number": 71, "subject": "ICICI: <PERSON> <PERSON><PERSON><PERSON> (14d)", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:29:44.141174"}, {"row_number": 72, "subject": "ICICI: Mend Platform Access", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:29:46.161730"}, {"row_number": 73, "subject": "ICICI: MP Abandonment Date", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:29:48.179889"}, {"row_number": 74, "subject": "ICICI: MP Activity", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:29:50.191879"}, {"row_number": 75, "subject": "ICICI: MP Activity", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:29:52.212226"}, {"row_number": 76, "subject": "ICICI: Mend Platform Access", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:29:54.230511"}, {"row_number": 77, "subject": "ICICI: MP Abandonment Date", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:29:56.248929"}, {"row_number": 78, "subject": "ICICI: MP Activity", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:29:58.259590"}, {"row_number": 79, "subject": "ICICI: MP Activity Frequency", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:30:00.265392"}, {"row_number": 80, "subject": "ICICI: MP Activity", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:30:02.284502"}, {"row_number": 81, "subject": "ICICI: January Newsletter:  Keep Your Code Secure, Exciting Updates from Mend.io!", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:30:04.304928"}, {"row_number": 82, "subject": "ICICI: Mend Platform Access", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:30:06.326398"}, {"row_number": 83, "subject": "ICICI: MP Abandonment Date", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:30:08.344083"}, {"row_number": 84, "subject": "ICICI: MP Activity", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:30:10.359055"}, {"row_number": 85, "subject": "ICICI: MP Activity", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:30:12.372666"}, {"row_number": 86, "subject": "ICICI: Mend Platform Access", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:30:14.391426"}, {"row_number": 87, "subject": "ICICI: MP Activity Frequency", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:30:16.414170"}, {"row_number": 88, "subject": "ICICI: MP Abandonment Date", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:30:18.436317"}, {"row_number": 89, "subject": "ICICI: MP Activity", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:30:20.456179"}, {"row_number": 90, "subject": "ICICI: <PERSON><PERSON><PERSON> to Delighted", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:30:22.476752"}, {"row_number": 91, "subject": "ICICI: <PERSON><PERSON><PERSON> to Delighted", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:30:24.495563"}, {"row_number": 92, "subject": "ICICI: NPS Send Date", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:30:26.507472"}, {"row_number": 93, "subject": "ICICI: NPS Last Sent", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:30:28.525354"}, {"row_number": 94, "subject": "ICICI: NPS Send Date", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:30:30.542800"}, {"row_number": 95, "subject": "ICICI: NPS Last Sent", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:30:32.558214"}, {"row_number": 96, "subject": "ICICI: Mend Platform Access", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:30:34.569208"}, {"row_number": 97, "subject": "ICICI: Mend Platform Access", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:30:36.587803"}, {"row_number": 98, "subject": "ICICI: Risk Status", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:30:38.604364"}, {"row_number": 99, "subject": "ICICI: Risk Next Step(s)", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:30:40.622598"}, {"row_number": 100, "subject": "ICICI: Primary Risk Reason", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:30:42.639267"}, {"row_number": 101, "subject": "ICICI: Mend Platform Access", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:30:44.657849"}, {"row_number": 102, "subject": "ICICI: <div><br>Upcoming NPS Survey&nbsp;</div>", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:30:46.675278"}, {"row_number": 103, "subject": "ICICI: Mend Platform Access", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:30:48.693637"}, {"row_number": 104, "subject": "ICICI: Mend Platform Access", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:30:50.713405"}, {"row_number": 105, "subject": "ICICI: Risk KPI", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:30:52.728966"}, {"row_number": 106, "subject": "ICICI: <PERSON> <PERSON> <PERSON><PERSON><PERSON>", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:30:54.744048"}, {"row_number": 107, "subject": "ICICI: Mend Platform Access", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:30:56.757673"}, {"row_number": 108, "subject": "ICICI: December Newsletter: Sharper Risk Insights & Updates", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:30:58.768173"}, {"row_number": 109, "subject": "ICICI: Mend Platform Access", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:31:00.784197"}, {"row_number": 110, "subject": "ICICI: Solana: MSC Critical Security Event", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:31:02.800536"}, {"row_number": 111, "subject": "ICICI: Mend Platform Access", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:31:04.819640"}, {"row_number": 112, "subject": "ICICI: Mend Platform Access", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:31:06.833474"}, {"row_number": 113, "subject": "ICICI: Mend Platform Access", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:31:08.849611"}, {"row_number": 114, "subject": "ICICI: SAST Planned Downtime_copy", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:31:10.864381"}, {"row_number": 115, "subject": "ICICI: Mend Platform Access", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:31:12.895819"}, {"row_number": 116, "subject": "ICICI: Mend Platform Access", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:31:14.911292"}, {"row_number": 117, "subject": "ICICI: Mend Platform Access", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:31:16.926069"}, {"row_number": 118, "subject": "ICICI: SAST Planned Downtime", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:31:18.938236"}, {"row_number": 119, "subject": "ICICI: Mend Platform Access", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:31:20.954283"}, {"row_number": 120, "subject": "ICICI: <PERSON><PERSON><PERSON> to Delighted", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:31:22.995034"}, {"row_number": 121, "subject": "ICICI: NPS Send Date", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:31:25.008086"}, {"row_number": 122, "subject": "ICICI: NPS Last Sent", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:31:27.021397"}, {"row_number": 123, "subject": "ICICI: Mend Platform Access", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:31:29.039990"}, {"row_number": 124, "subject": "ICICI: November newsletter 2024", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:31:31.056736"}, {"row_number": 125, "subject": "ICICI: Mend Platform Access", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:31:33.071275"}, {"row_number": 126, "subject": "ICICI: <PERSON> <PERSON><PERSON><PERSON> (7d)", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:31:35.090641"}, {"row_number": 127, "subject": "ICICI: Mend Platform Access", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:31:37.110758"}, {"row_number": 128, "subject": "ICICI: <div><br>Upcoming NPS Survey&nbsp;</div>", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:31:39.128088"}, {"row_number": 129, "subject": "ICICI: October newsletter 2024", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:31:41.142669"}, {"row_number": 130, "subject": "ICICI: Risk Status", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:31:43.153303"}, {"row_number": 131, "subject": "ICICI: Primary Risk Reason", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:31:45.167767"}, {"row_number": 132, "subject": "ICICI: Risk Next Step(s)", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:31:47.183729"}, {"row_number": 133, "subject": "ICICI: Risk Status", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:31:49.202989"}, {"row_number": 134, "subject": "ICICI: Primary Risk Reason", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:31:51.220598"}, {"row_number": 135, "subject": "ICICI: Risk Next Step(s)", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:31:53.237388"}, {"row_number": 136, "subject": "ICICI: Support: IP Address Change", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:31:55.255322"}, {"row_number": 137, "subject": "ICICI: Business Model Launch", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:31:57.269230"}, {"row_number": 138, "subject": "ICICI: New Business Model_Webinar Follow up", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:31:59.286867"}, {"row_number": 139, "subject": "ICICI: MP Activity Frequency", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:32:01.304308"}, {"row_number": 140, "subject": "ICICI: <PERSON><PERSON>", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:32:03.317583"}, {"row_number": 141, "subject": "ICICI: New Business Model", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:32:05.335736"}, {"row_number": 142, "subject": "ICICI: Slack to #risk", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:32:07.352682"}, {"row_number": 143, "subject": "ICICI: Risk Next Step(s)", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:32:09.372121"}, {"row_number": 144, "subject": "ICICI: Primary Risk Reason", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:32:11.391072"}, {"row_number": 145, "subject": "ICICI: Risk Status", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:32:13.406133"}, {"row_number": 146, "subject": "ICICI: <PERSON><PERSON>", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:32:15.425189"}, {"row_number": 147, "subject": "ICICI: MP Activity Frequency", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:32:17.444675"}, {"row_number": 148, "subject": "ICICI: MP Activity Frequency", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:32:19.463872"}, {"row_number": 149, "subject": "ICICI: NPS Send Date", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:32:21.477118"}, {"row_number": 150, "subject": "ICICI: MP Activity Frequency", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:32:23.496306"}, {"row_number": 151, "subject": "ICICI: MP Activity", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:32:25.511007"}, {"row_number": 152, "subject": "ICICI: MP Activity Status", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:32:27.519494"}, {"row_number": 153, "subject": "ICICI: <PERSON><PERSON>", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:32:29.541236"}, {"row_number": 154, "subject": "ICICI: MP Activity Status", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:32:31.557123"}, {"row_number": 155, "subject": "ICICI: Product Roadmap H2 2024", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:32:33.577033"}, {"row_number": 156, "subject": "ICICI: <PERSON><PERSON><PERSON> to Delighted", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:32:35.594950"}, {"row_number": 157, "subject": "ICICI: NPS Last Sent", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:32:37.614324"}, {"row_number": 158, "subject": "ICICI: NPS Send Date", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:32:39.627859"}, {"row_number": 159, "subject": "ICICI: NPS Last Sent", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:32:41.644826"}, {"row_number": 160, "subject": "ICICI: NPS Send Date", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:32:43.688771"}, {"row_number": 161, "subject": "ICICI: <PERSON><PERSON><PERSON> to Delighted", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:32:45.707078"}, {"row_number": 162, "subject": "ICICI: Vulnerability Insights with MITRE Data", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:32:47.724434"}, {"row_number": 163, "subject": "ICICI: <PERSON><PERSON>", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:32:49.745884"}, {"row_number": 164, "subject": "ICICI: Mend Platform Access", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:32:51.750559"}, {"row_number": 165, "subject": "ICICI: CSM Managed", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:32:53.755135"}, {"row_number": 166, "subject": "ICICI: <div><br>Upcoming NPS Survey&nbsp;</div>", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:32:55.763827"}, {"row_number": 167, "subject": "ICICI: <PERSON><PERSON>", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:32:57.772822"}, {"row_number": 168, "subject": "ICICI: <PERSON><PERSON>", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:32:59.783472"}, {"row_number": 169, "subject": "ICICI: <PERSON><PERSON>", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:33:01.808827"}, {"row_number": 170, "subject": "ICICI: <PERSON><PERSON>", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:33:03.817091"}, {"row_number": 171, "subject": "ICICI: <PERSON> Follow Up Email", "activity_type": "Meeting", "error": "UI automation failed", "timestamp": "2025-05-29T21:33:05.825185"}, {"row_number": 172, "subject": "ICICI: <PERSON> Follow Up Email", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:33:07.831952"}, {"row_number": 173, "subject": "ICICI: <PERSON> Follow Up Email", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:33:09.840879"}, {"row_number": 174, "subject": "ICICI: Slack to #risk", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:33:11.849781"}, {"row_number": 175, "subject": "ICICI: <PERSON><PERSON>", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:33:13.859155"}, {"row_number": 176, "subject": "ICICI: <PERSON><PERSON><PERSON> - CSM Satisfaction Survey/Kelle Intro", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:33:15.863538"}, {"row_number": 177, "subject": "ICICI: <div><span class=\"mention-wrapper\" data-reactroot=\"\"><span class=\"field\">ICICI Bank</span>'s renewal date has passed</span></div>", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:33:17.872291"}, {"row_number": 178, "subject": "ICICI: Correction - CVE - 2024 - 3094_copy", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:33:19.878635"}, {"row_number": 179, "subject": "ICICI: <PERSON><PERSON>", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:33:21.888129"}, {"row_number": 180, "subject": "ICICI: All Other Customers - CVE - 2024 - 3094", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:33:23.897007"}, {"row_number": 181, "subject": "ICICI: <PERSON><PERSON>", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:33:25.904099"}, {"row_number": 182, "subject": "ICICI: License Utilization Status", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:33:27.913121"}, {"row_number": 183, "subject": "ICICI: License Utilization Status", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:33:29.921762"}, {"row_number": 184, "subject": "ICICI: <PERSON><PERSON>", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:33:31.930964"}, {"row_number": 185, "subject": "ICICI: <PERSON><PERSON>", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:33:33.937573"}, {"row_number": 186, "subject": "ICICI: <PERSON><PERSON>", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:33:35.954754"}, {"row_number": 187, "subject": "ICICI: <PERSON><PERSON>", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:33:37.971557"}, {"row_number": 188, "subject": "ICICI: Executive Engaged", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:33:39.990921"}, {"row_number": 189, "subject": "ICICI: AI Survey - Gold/ Platinum Customers - Follow Up", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:33:42.013017"}, {"row_number": 190, "subject": "ICICI: <PERSON><PERSON>", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:33:44.027389"}, {"row_number": 191, "subject": "ICICI: <PERSON><PERSON>", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:33:46.047543"}, {"row_number": 192, "subject": "ICICI: AI Survey - Int. Gold & Platinum Customers Oops_copy", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:33:48.066219"}, {"row_number": 193, "subject": "ICICI: AI Survey - Gold & Platinum Customers", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:33:50.073244"}, {"row_number": 194, "subject": "ICICI: <PERSON><PERSON>", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:33:52.091895"}, {"row_number": 195, "subject": "ICICI: <PERSON><PERSON>", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:33:54.101216"}, {"row_number": 196, "subject": "ICICI: <PERSON><PERSON>", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:33:56.111507"}, {"row_number": 197, "subject": "ICICI: <PERSON><PERSON>", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:33:58.117195"}, {"row_number": 198, "subject": "ICICI: Slack to #risk", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:34:00.126289"}, {"row_number": 199, "subject": "ICICI: Risk Update", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:34:02.131216"}, {"row_number": 200, "subject": "ICICI: Risk Status", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:34:04.140234"}, {"row_number": 201, "subject": "ICICI: <PERSON><PERSON>", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:34:06.149202"}, {"row_number": 202, "subject": "ICICI: <PERSON><PERSON>", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:34:08.162839"}, {"row_number": 203, "subject": "ICICI: NPS Send Date", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:34:10.179725"}, {"row_number": 204, "subject": "ICICI: NPS Informed", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:34:12.188319"}, {"row_number": 205, "subject": "ICICI: NPS Informed", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:34:14.197553"}, {"row_number": 206, "subject": "ICICI: Touch Status 2", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:34:16.206414"}, {"row_number": 207, "subject": "ICICI: <PERSON><PERSON>", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:34:18.215184"}, {"row_number": 208, "subject": "ICICI: <PERSON><PERSON>", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:34:20.219958"}, {"row_number": 209, "subject": "ICICI: <PERSON><PERSON>", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:34:22.227083"}, {"row_number": 210, "subject": "ICICI: <PERSON><PERSON>", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:34:24.243465"}, {"row_number": 211, "subject": "ICICI: <PERSON><PERSON>", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:34:26.253613"}, {"row_number": 212, "subject": "ICICI: <PERSON><PERSON>", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:34:28.262488"}, {"row_number": 213, "subject": "ICICI: Slack to #risk", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:34:30.269423"}, {"row_number": 214, "subject": "ICICI: Risk Update", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:34:32.278006"}, {"row_number": 215, "subject": "ICICI: Risk Status", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:34:34.287278"}, {"row_number": 216, "subject": "ICICI: <PERSON><PERSON>", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:34:36.296831"}, {"row_number": 217, "subject": "ICICI: <PERSON><PERSON>", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:34:38.306596"}, {"row_number": 218, "subject": "ICICI: <PERSON><PERSON>", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:34:40.315666"}, {"row_number": 219, "subject": "ICICI: <PERSON><PERSON>", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:34:42.320895"}, {"row_number": 220, "subject": "ICICI: <PERSON><PERSON>", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:34:44.331413"}, {"row_number": 221, "subject": "ICICI: <PERSON><PERSON>", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:34:46.340121"}, {"row_number": 222, "subject": "ICICI: <PERSON><PERSON>", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:34:48.349524"}, {"row_number": 223, "subject": "ICICI: <PERSON><PERSON>", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:34:50.358145"}, {"row_number": 224, "subject": "ICICI: <PERSON><PERSON>", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:34:52.366966"}, {"row_number": 225, "subject": "ICICI: <PERSON><PERSON>", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:34:54.376169"}, {"row_number": 226, "subject": "ICICI: <PERSON><PERSON>", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:34:56.384968"}, {"row_number": 227, "subject": "ICICI: ICICI Bank <> Mend - Catch Up Meeting External", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:34:58.393927"}, {"row_number": 228, "subject": "ICICI: <PERSON><PERSON>", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:35:00.401232"}, {"row_number": 229, "subject": "ICICI: SCA CD Variance Stages", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:35:02.410090"}, {"row_number": 230, "subject": "ICICI: License Utilization Status", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:35:04.419306"}, {"row_number": 231, "subject": "ICICI: Mend Vulnerability found by WithSecure", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:35:06.428656"}, {"row_number": 232, "subject": "ICICI: ICICI Bank <> Mend - Catch Up Meeting", "activity_type": "Meeting", "error": "UI automation failed", "timestamp": "2025-05-29T21:35:08.438620"}, {"row_number": 233, "subject": "ICICI: ICICI Bank<>Mend.io organization migration from LBA to VBA", "activity_type": "Meeting", "error": "UI automation failed", "timestamp": "2025-05-29T21:35:10.447519"}, {"row_number": 234, "subject": "ICICI: <PERSON><PERSON>", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:35:12.456869"}, {"row_number": 235, "subject": "ICICI: No need to touch base", "activity_type": "Meeting", "error": "UI automation failed", "timestamp": "2025-05-29T21:35:14.465487"}, {"row_number": 236, "subject": "ICICI: Unified Agent Hotfix now available-due to Java upgrade issue", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:35:16.475287"}, {"row_number": 237, "subject": "ICICI: Unified Agent - Java upgrade issue", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:35:18.485134"}, {"row_number": 238, "subject": "ICICI: LBA email change sent to the partner", "activity_type": "Meeting", "error": "UI automation failed", "timestamp": "2025-05-29T21:35:20.493466"}, {"row_number": 239, "subject": "ICICI: ICICI Bank<>MEND", "activity_type": "Meeting", "error": "UI automation failed", "timestamp": "2025-05-29T21:35:22.502123"}, {"row_number": 240, "subject": "ICICI: <PERSON><PERSON>", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:35:24.511122"}, {"row_number": 241, "subject": "ICICI: <PERSON><PERSON>", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:35:26.527975"}, {"row_number": 242, "subject": "ICICI: Request for Developer Training for ICICI", "activity_type": "Meeting", "error": "UI automation failed", "timestamp": "2025-05-29T21:35:28.537324"}, {"row_number": 243, "subject": "ICICI: Re: Queries on Licensing Policy related details - ICICI Bank", "activity_type": "Email", "error": "UI automation failed", "timestamp": "2025-05-29T21:35:30.545965"}, {"row_number": 244, "subject": "ICICI: Re: Mend integration with LDAP and SAML - ICICI", "activity_type": "Email", "error": "UI automation failed", "timestamp": "2025-05-29T21:35:32.554742"}, {"row_number": 245, "subject": "ICICI: <PERSON><PERSON>", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:35:34.563688"}, {"row_number": 246, "subject": "ICICI: NPS Send Date", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:35:36.573511"}, {"row_number": 247, "subject": "ICICI: NPS Informed", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:35:38.582990"}, {"row_number": 248, "subject": "ICICI: <PERSON><PERSON>", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:35:40.592008"}, {"row_number": 249, "subject": "ICICI: <PERSON><PERSON>", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:35:42.600683"}, {"row_number": 250, "subject": "ICICI: No need to touch base- working with the partner", "activity_type": "Meeting", "error": "UI automation failed", "timestamp": "2025-05-29T21:35:44.610133"}, {"row_number": 251, "subject": "ICICI: Inform Key Contacts of upcoming NPS survey", "activity_type": "Meeting", "error": "UI automation failed", "timestamp": "2025-05-29T21:35:46.619303"}, {"row_number": 252, "subject": "ICICI: <PERSON> is working to arrange a meeting with the bank and partner", "activity_type": "Meeting", "error": "UI automation failed", "timestamp": "2025-05-29T21:35:48.628666"}, {"row_number": 253, "subject": "ICICI: <PERSON><PERSON>", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:35:50.637656"}, {"row_number": 254, "subject": "ICICI: <PERSON><PERSON>", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:35:52.646915"}, {"row_number": 255, "subject": "ICICI: Discussion with ICICI Team - Mend - WhiteSource !!", "activity_type": "Meeting", "error": "UI automation failed", "timestamp": "2025-05-29T21:35:54.656564"}, {"row_number": 256, "subject": "ICICI: <PERSON><PERSON>", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:35:56.666280"}, {"row_number": 257, "subject": "ICICI: Update from Luis- partner manager", "activity_type": "Meeting", "error": "UI automation failed", "timestamp": "2025-05-29T21:35:58.675390"}, {"row_number": 258, "subject": "ICICI: <PERSON><PERSON>", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:36:00.684719"}, {"row_number": 259, "subject": "ICICI: Repo Integration-Accelerate your remediation", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:36:02.694361"}, {"row_number": 260, "subject": "ICICI: RSA 2023 conference", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:36:04.703375"}, {"row_number": 261, "subject": "ICICI: <PERSON><PERSON>", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:36:06.712993"}, {"row_number": 262, "subject": "ICICI: FEE ticket in place", "activity_type": "Meeting", "error": "UI automation failed", "timestamp": "2025-05-29T21:36:08.722458"}, {"row_number": 263, "subject": "ICICI: FEE ticket in place", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:36:10.731082"}, {"row_number": 264, "subject": "ICICI: FEE ticket in place", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:36:12.740189"}, {"row_number": 265, "subject": "ICICI: Discussion with ICICI Team - Mend - WhiteSource !!", "activity_type": "Meeting", "error": "UI automation failed", "timestamp": "2025-05-29T21:36:14.748744"}, {"row_number": 266, "subject": "ICICI: ICICI/Meteonic - Next Steps & Alignment", "activity_type": "Meeting", "error": "UI automation failed", "timestamp": "2025-05-29T21:36:16.757542"}, {"row_number": 267, "subject": "ICICI: Sunny to set up a joint meeting?", "activity_type": "Meeting", "error": "UI automation failed", "timestamp": "2025-05-29T21:36:18.762937"}, {"row_number": 268, "subject": "ICICI: Sunny to set up a joint meeting?", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:36:20.772420"}, {"row_number": 269, "subject": "ICICI: <PERSON><PERSON>", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:36:22.781051"}, {"row_number": 270, "subject": "ICICI: Sales Manager Region", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:36:24.790552"}, {"row_number": 271, "subject": "ICICI: SCA CD Variance Stages", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:36:26.799434"}, {"row_number": 272, "subject": "ICICI: <PERSON><PERSON>", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:36:28.808951"}, {"row_number": 273, "subject": "ICICI: Sunny to set up a joint meeting?", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:36:30.829445"}, {"row_number": 274, "subject": "ICICI: ********- zoom discussions in place", "activity_type": "Meeting", "error": "UI automation failed", "timestamp": "2025-05-29T21:36:32.845815"}, {"row_number": 275, "subject": "ICICI: Mend/Meteonic sync", "activity_type": "Meeting", "error": "UI automation failed", "timestamp": "2025-05-29T21:36:34.861311"}, {"row_number": 276, "subject": "ICICI: Case#********- call with Eng", "activity_type": "Meeting", "error": "UI automation failed", "timestamp": "2025-05-29T21:36:36.876988"}, {"row_number": 277, "subject": "ICICI: Working to set a meting with a the bank and the BP", "activity_type": "Meeting", "error": "UI automation failed", "timestamp": "2025-05-29T21:36:38.896832"}, {"row_number": 278, "subject": "ICICI: Meteonic will do onsite in the bank on Jan25th", "activity_type": "Meeting", "error": "UI automation failed", "timestamp": "2025-05-29T21:36:40.917399"}, {"row_number": 279, "subject": "ICICI: Mend's Malicious Package Communications", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:36:42.934133"}, {"row_number": 280, "subject": "ICICI: No need to touch base", "activity_type": "Meeting", "error": "UI automation failed", "timestamp": "2025-05-29T21:36:44.949348"}, {"row_number": 281, "subject": "ICICI: NPS Send Date", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:36:46.967769"}, {"row_number": 282, "subject": "ICICI: NPS Informed", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:36:48.984951"}, {"row_number": 283, "subject": "ICICI: NPS Informed", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:36:51.003561"}, {"row_number": 284, "subject": "ICICI: Internal sync with AM", "activity_type": "Meeting", "error": "UI automation failed", "timestamp": "2025-05-29T21:36:53.015846"}, {"row_number": 285, "subject": "ICICI: Touch Status", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:36:55.033661"}, {"row_number": 286, "subject": "ICICI: NPS Informed", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:36:57.053676"}, {"row_number": 287, "subject": "ICICI: Sales Manager Region", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:36:59.067027"}, {"row_number": 288, "subject": "ICICI: Team Manager Region", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:37:01.083850"}, {"row_number": 289, "subject": "ICICI: Team Manager", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:37:03.098940"}, {"row_number": 290, "subject": "ICICI: SCA CD Variance Stages", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:37:05.115088"}, {"row_number": 291, "subject": "ICICI: Setup a call with customers and explains our Policies best practice", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:37:07.133102"}, {"row_number": 292, "subject": "ICICI: Touch Status", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:37:09.150125"}, {"row_number": 293, "subject": "ICICI: Setup a call with customers and explains our Policies best practice", "activity_type": "Email", "error": "UI automation failed", "timestamp": "2025-05-29T21:37:11.163749"}, {"row_number": 294, "subject": "ICICI: Setup a call with customers and explains our Policies best practice", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:37:13.180949"}, {"row_number": 295, "subject": "ICICI: Outage in app.whitesourcesoftware.com", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:37:15.191508"}, {"row_number": 296, "subject": "ICICI: Touch Status", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:37:17.205118"}, {"row_number": 297, "subject": "ICICI: Check the usage and project number", "activity_type": "Meeting", "error": "UI automation failed", "timestamp": "2025-05-29T21:37:19.221395"}, {"row_number": 298, "subject": "ICICI: CSAT - Spring4Shell, Platinum&Gold", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:37:21.236542"}, {"row_number": 299, "subject": "ICICI: Touch Status", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:37:23.258082"}, {"row_number": 300, "subject": "ICICI: Team Manager Region", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:37:25.276565"}, {"row_number": 301, "subject": "ICICI: Spring4Shell HIGH SEVERITY Vulnerability in Spring core - Gold & Platinum", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:37:27.288859"}, {"row_number": 302, "subject": "ICICI: <PERSON><PERSON>", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:37:29.307467"}, {"row_number": 303, "subject": "ICICI: Touch Status", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:37:31.325196"}, {"row_number": 304, "subject": "ICICI: VBA migration", "activity_type": "Meeting", "error": "UI automation failed", "timestamp": "2025-05-29T21:37:33.344793"}, {"row_number": 305, "subject": "ICICI: Plz check with the usage is low", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:37:35.359623"}, {"row_number": 306, "subject": "ICICI: March 2022 Newsletter- Dedicated", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:37:37.375014"}, {"row_number": 307, "subject": "ICICI: <PERSON><PERSON>", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:37:39.394214"}, {"row_number": 308, "subject": "ICICI: BP customer- no touch base is needed", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:37:41.409653"}, {"row_number": 309, "subject": "ICICI: Touch Status", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:37:43.429633"}, {"row_number": 310, "subject": "ICICI: Customer Communication Regarding LBA to VBA Migration - SCA-ICICI Bank - for SCA-ICICI Bank", "activity_type": "Email", "error": "UI automation failed", "timestamp": "2025-05-29T21:37:45.447198"}, {"row_number": 311, "subject": "ICICI: Team Manager", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:37:47.462818"}, {"row_number": 312, "subject": "ICICI: <PERSON><PERSON>", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:37:49.479589"}, {"row_number": 313, "subject": "ICICI: Log4j Vulnerability webinar Dedicated CSM", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:37:51.494813"}, {"row_number": 314, "subject": "ICICI: Vulnerability in Apache Log4j2 library Part 2  Dedicated CSM Accounts", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:37:53.510330"}, {"row_number": 315, "subject": "ICICI: Severity Vulnerability (CVE-2021-44228) in the Log4j2 -Dedicated CSM", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:37:55.526104"}, {"row_number": 316, "subject": "ICICI: Customer Communication Regarding LBA to VBA Migration - SCA-ICICI Bank", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:37:57.536864"}, {"row_number": 317, "subject": "ICICI: Contact SCA-ICICI Bank about moving to VBA", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:37:59.552644"}, {"row_number": 318, "subject": "ICICI: <PERSON><PERSON>", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:38:01.574291"}, {"row_number": 319, "subject": "ICICI: SCA CD Variance Stages", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:38:03.589571"}, {"row_number": 320, "subject": "ICICI: Partner account", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:38:05.608933"}, {"row_number": 321, "subject": "ICICI: Sales Manager Region", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:38:07.624816"}, {"row_number": 322, "subject": "ICICI: <PERSON><PERSON>", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:38:09.644413"}]
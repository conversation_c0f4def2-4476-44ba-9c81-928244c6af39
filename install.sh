#!/bin/bash

echo "🐺 Wild Weasel Installation Script"
echo "=================================="

# Check if Python 3 is installed
if ! command -v python3 &> /dev/null; then
    echo "❌ Python 3 is not installed. Please install Python 3.8+ first."
    exit 1
fi

echo "✅ Python 3 found: $(python3 --version)"

# Install Python dependencies
echo "📦 Installing Python dependencies..."
pip3 install -r requirements.txt

# Install Playwright browsers
echo "🎭 Installing Playwright browsers..."
playwright install chromium

# Create .env file if it doesn't exist
if [ ! -f .env ]; then
    echo "⚙️ Creating .env configuration file..."
    cp .env.template .env
    echo "📝 Please edit .env file with your Gainsight credentials"
else
    echo "✅ .env file already exists"
fi

# Run pre-mission check
echo ""
echo "🔍 Running pre-mission check..."
python3 pre_mission_check.py

echo ""
echo "🐺 Wild Weasel installation complete!"
echo ""
echo "Next steps:"
echo "1. Edit .env file with your Gainsight credentials"
echo "2. Run: python3 wild_weasel_agent.py"
echo ""
echo "🎯 Ready to execute Phase 1: Email Migration"

#!/usr/bin/env python3
"""
🐺 Wild Weasel CSV UI Automation - Gainsight Timeline Activities
================================================================================
Mission: Create activities from CSV via complete UI automation
Built for enterprise-grade migrations with intelligent field handling
Processing: /Users/<USER>/Desktop/totango/ICICI_processed_gainsight_mapped_enhanced_demo.csv
================================================================================
"""

import csv
import os
import sys
import time
import logging
from datetime import datetime
from typing import Dict, List, Any, Optional
from playwright.sync_api import sync_playwright, TimeoutError as PlaywrightTimeoutError

# Setup comprehensive logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("/Users/<USER>/Desktop/wild_weasel_gainsight_migration/wild_weasel_csv_ui.log"),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger("WildWeasel-CSV-UI")

class WildWeaselCSVUIAutomation:
    """Enterprise Wild Weasel CSV UI automation for Gainsight activities"""
    
    def __init__(self):
        self.config = {
            "gainsight_url": "https://demo-emea1.gainsightcloud.com",
            "login_url": "https://demo-emea1.gainsightcloud.com/v1/ui/home",
            "target_c360_url": "https://demo-emea1.gainsightcloud.com/v1/ui/customersuccess360?cid=1P02IPMAEL4M3CQGY4K5FHU22D2HHT11KCKU#/8fd79692-8e98-4543-b2ec-bc0dba3776ca",
            
            # CSV Source Configuration
            "csv_file_path": "/Users/<USER>/Desktop/totango/ICICI_processed_gainsight_mapped_enhanced_demo.csv",
            
            # Output files
            "success_log": "/Users/<USER>/Desktop/wild_weasel_gainsight_migration/csv_ui_migration_success.json",
            "failed_log": "/Users/<USER>/Desktop/wild_weasel_gainsight_migration/csv_ui_migration_failed.json",
            "progress_log": "/Users/<USER>/Desktop/wild_weasel_gainsight_migration/csv_ui_migration_progress.json"
        }
        
        self.activities = []
        self.migration_results = {
            "successful": [],
            "failed": [],
            "total_processed": 0,
            "start_time": None,
            "end_time": None,
            "current_activity": 0
        }
        
        # Embedded credentials
        self.credentials = {
            "username": "<EMAIL>",
            "password": "@Ramprasad826ie"
        }
    
    def mission_brief(self):
        """Display the comprehensive mission brief"""
        print("🐺" + "="*100)
        print("  WILD WEASEL CSV UI AUTOMATION - ENTERPRISE MIGRATION AGENT")
        print("="*102)
        print("🎯 MISSION CAPABILITIES:")
        print("  ✅ CSV PROCESSING:")
        print("     → Intelligent CSV parsing with null handling")
        print("     → Date format conversion (YYYY-MM-DD HH:MM:SS → M/D/YYYY)")
        print("     → Time extraction and formatting (HH:MM:SS → HH:MM)")
        print("     → Dynamic field mapping and validation")
        print("  ✅ UI AUTOMATION:")
        print("     → Complete browser-based activity creation")
        print("     → Modal dialog handling and form filling")
        print("     → Dropdown selection with intelligent matching")
        print("     → Multi-strategy element detection")
        print("  ✅ ENTERPRISE FEATURES:")
        print("     → Progress tracking and resume capability")
        print("     → Comprehensive error handling and recovery")
        print("     → Real-time logging and status reporting")
        print("     → Memory-optimized processing for large datasets")
        print("="*102)
        print(f"📁 Source CSV: {self.config['csv_file_path']}")
        print(f"🎯 Target C360: {self.config['target_c360_url']}")
        print(f"👤 Username: {self.credentials['username']}")
        print(f"📊 Total Activities to Process: {len(self.activities)}")
        print("="*102)
    
    def load_csv_activities(self):
        """Load and process activities from CSV file with intelligent parsing"""
        try:
            if not os.path.exists(self.config['csv_file_path']):
                logger.error(f"❌ CSV file not found: {self.config['csv_file_path']}")
                return False
            
            logger.info("📖 Loading CSV activities...")
            self.activities = []
            
            with open(self.config['csv_file_path'], 'r', encoding='utf-8') as csvfile:
                # Use DictReader for intelligent column mapping
                reader = csv.DictReader(csvfile)
                
                # Log detected columns
                columns = reader.fieldnames
                logger.info(f"📋 Detected CSV columns: {columns}")
                
                for row_num, row in enumerate(reader, start=1):
                    try:
                        # Extract and clean data with null handling
                        activity = {
                            "row_number": row_num,
                            "subject": self._clean_field(row.get("Subject", "")),
                            "activity_date": self._clean_field(row.get("Activity Date", "")),
                            "activity_type": self._clean_field(row.get("Activity Type", "")),
                            "plain_text": self._clean_field(row.get("Plain Text", "")),
                            "author_name": self._clean_field(row.get("Author Name", "")),
                            "flow_type": self._clean_field(row.get("Flow Type", "")),
                            "touchpoint_reason": self._clean_field(row.get("Touchpoint Reason", "")),
                            "company": self._clean_field(row.get("Company", "ICICI")),
                            
                            # Processed fields for UI automation
                            "gainsight_date": self._convert_date_format(row.get("Activity Date", "")),
                            "gainsight_time": self._extract_time_format(row.get("Activity Date", "")),
                            "has_subject": bool(self._clean_field(row.get("Subject", ""))),
                            "has_activity_type": bool(self._clean_field(row.get("Activity Type", ""))),
                            "has_plain_text": bool(self._clean_field(row.get("Plain Text", ""))),
                            "has_author_name": bool(self._clean_field(row.get("Author Name", ""))),
                            "has_flow_type": bool(self._clean_field(row.get("Flow Type", ""))),
                            "has_touchpoint_reason": bool(self._clean_field(row.get("Touchpoint Reason", "")))
                        }
                        
                        # Only add activities with essential data
                        if activity["has_subject"] and activity["has_activity_type"]:
                            self.activities.append(activity)
                        else:
                            logger.warning(f"⚠️ Row {row_num}: Missing essential data (Subject or Activity Type)")
                            
                    except Exception as e:
                        logger.error(f"❌ Error processing CSV row {row_num}: {e}")
                        continue
            
            logger.info(f"📊 Successfully loaded {len(self.activities)} activities from CSV")
            return len(self.activities) > 0
                
        except Exception as e:
            logger.error(f"❌ Failed to load CSV activities: {e}")
            return False
    
    def _clean_field(self, value: str) -> str:
        """Clean and sanitize field values"""
        if not value or str(value).strip().lower() in ['', 'null', 'none', 'n/a']:
            return ""
        return str(value).strip()
    
    def _convert_date_format(self, date_str: str) -> str:
        """Convert date from 'YYYY-MM-DD HH:MM:SS' to 'M/D/YYYY' format"""
        try:
            if not date_str:
                return ""
            
            # Parse the date (handle various formats)
            if ' ' in date_str:
                date_part = date_str.split(' ')[0]
            else:
                date_part = date_str
            
            # Parse YYYY-MM-DD format
            dt = datetime.strptime(date_part, '%Y-%m-%d')
            # Convert to M/D/YYYY format (Gainsight format)
            return dt.strftime('%-m/%-d/%Y')  # No leading zeros
            
        except Exception as e:
            logger.warning(f"⚠️ Date conversion failed for '{date_str}': {e}")
            return ""
    
    def _extract_time_format(self, date_str: str) -> str:
        """Extract time from 'YYYY-MM-DD HH:MM:SS' and format as 'HH:MM'"""
        try:
            if not date_str or ' ' not in date_str:
                return ""
            
            # Extract time part
            time_part = date_str.split(' ')[1]
            
            # Parse HH:MM:SS and convert to HH:MM
            time_obj = datetime.strptime(time_part, '%H:%M:%S')
            return time_obj.strftime('%H:%M')
            
        except Exception as e:
            logger.warning(f"⚠️ Time extraction failed for '{date_str}': {e}")
            return ""
    
    def login_to_gainsight(self, page):
        """Login to Gainsight with enhanced reliability"""
        try:
            logger.info("🔐 Initiating Gainsight login...")
            
            page.goto(self.config["login_url"])
            page.wait_for_load_state("networkidle", timeout=15000)
            
            # Fill credentials with multiple strategies
            username_selectors = [
                "input[name='username']",
                "input[type='email']",
                "#username",
                "input[placeholder*='email' i]",
                "input[placeholder*='username' i]"
            ]
            
            password_selectors = [
                "input[name='password']",
                "input[type='password']",
                "#password",
                "input[placeholder*='password' i]"
            ]
            
            # Fill username
            username_filled = False
            for selector in username_selectors:
                try:
                    if page.locator(selector).count() > 0:
                        page.fill(selector, self.credentials["username"])
                        username_filled = True
                        logger.info(f"✅ Username filled using: {selector}")
                        break
                except:
                    continue
            
            if not username_filled:
                logger.error("❌ Could not find username field")
                return False
            
            # Fill password
            password_filled = False
            for selector in password_selectors:
                try:
                    if page.locator(selector).count() > 0:
                        page.fill(selector, self.credentials["password"])
                        password_filled = True
                        logger.info(f"✅ Password filled using: {selector}")
                        break
                except:
                    continue
            
            if not password_filled:
                logger.error("❌ Could not find password field")
                return False
            
            # Click login button
            login_selectors = [
                "button:has-text('Log In')",
                "button:has-text('Login')",
                "button:has-text('Sign In')",
                "button[type='submit']",
                "input[type='submit']"
            ]
            
            login_clicked = False
            for selector in login_selectors:
                try:
                    if page.locator(selector).count() > 0:
                        page.click(selector)
                        login_clicked = True
                        logger.info(f"✅ Login button clicked: {selector}")
                        break
                except:
                    continue
            
            if not login_clicked:
                logger.error("❌ Could not find login button")
                return False
            
            # Wait for successful login
            page.wait_for_function(
                "() => window.location.href.includes('/home') || window.location.href.includes('/dashboard') || window.location.href.includes('/ui/')",
                timeout=30000
            )
            
            logger.info("✅ Successfully logged into Gainsight!")
            return True
                
        except Exception as e:
            logger.error(f"❌ Login failed: {e}")
            page.screenshot(path="/Users/<USER>/Desktop/wild_weasel_gainsight_migration/login_error_csv_ui.png")
            return False
    
    def navigate_to_timeline(self, page):
        """Navigate to C360 Timeline with enhanced reliability"""
        try:
            logger.info("🎯 Navigating to C360 Timeline...")
            
            page.goto(self.config["target_c360_url"])
            page.wait_for_load_state("networkidle", timeout=30000)
            time.sleep(3)
            
            # Find and click Timeline tab with multiple strategies
            timeline_selectors = [
                'a:has-text("Timeline")',
                'li:has-text("Timeline")',
                'div[role="tab"]:has-text("Timeline")',
                'button:has-text("Timeline")',
                'span:has-text("Timeline")',
                '[data-testid*="timeline" i]',
                '.timeline-tab',
                '#timeline-tab'
            ]
            
            timeline_found = False
            for selector in timeline_selectors:
                try:
                    if page.locator(selector).count() > 0:
                        page.click(selector)
                        time.sleep(3)
                        timeline_found = True
                        logger.info(f"📌 Timeline clicked using: {selector}")
                        break
                except:
                    continue
            
            if not timeline_found:
                # JavaScript fallback for Timeline
                timeline_clicked = page.evaluate("""() => {
                    const elements = document.querySelectorAll('*');
                    for (const el of elements) {
                        const text = el.innerText || el.textContent || '';
                        if (text.trim().toLowerCase() === 'timeline' && el.offsetParent !== null) {
                            el.click();
                            return true;
                        }
                    }
                    return false;
                }""")
                
                if timeline_clicked:
                    logger.info("📌 Timeline clicked via JavaScript fallback")
                    time.sleep(3)
                    timeline_found = True
            
            if timeline_found:
                page.screenshot(path="/Users/<USER>/Desktop/wild_weasel_gainsight_migration/timeline_csv_ui.png")
                logger.info("✅ Successfully navigated to Timeline")
                return True
            else:
                logger.error("❌ Failed to find Timeline tab")
                return False
                
        except Exception as e:
            logger.error(f"❌ Timeline navigation failed: {e}")
            page.screenshot(path="/Users/<USER>/Desktop/wild_weasel_gainsight_migration/timeline_nav_error.png")
            return False
    
    def create_activity_from_csv(self, page, activity_data, activity_index):
        """Create a single activity using CSV data via UI automation"""
        try:
            subject = activity_data["subject"]
            activity_type = activity_data["activity_type"]
            logger.info(f"📝 Creating activity {activity_index + 1}: {subject[:50]}...")
            
            # Step 1: Click the CREATE button using modern Playwright approach
            logger.info("🔘 Looking for CREATE button using modern approach...")
            
            # Modern approach: Use role-based locators first
            create_button_selectors = [
                page.get_by_role("button", name="CREATE"),
                page.get_by_role("button", name="Create"),
                page.get_by_text("CREATE"),
                page.get_by_text("Create"),
                page.locator('gs-cs360-header button'),
                page.locator('.gs-cs360-header button'),
                page.locator('button:has-text("+")')
            ]
            
            create_clicked = False
            for selector in create_button_selectors:
                try:
                    # Modern approach: Use wait_for() with timeout
                    selector.wait_for(state="visible", timeout=10000)
                    selector.click()
                    logger.info(f"🔘 CREATE button clicked successfully")
                    create_clicked = True
                    time.sleep(2)  # Wait for dropdown
                    break
                except Exception as e:
                    logger.warning(f"⚠️ CREATE selector failed: {e}")
                    continue
            
            if not create_clicked:
                logger.error("❌ Could not click CREATE button using modern approach")
                return False
            
            # Step 2: Wait for dropdown and click "Activity" option using modern approach
            logger.info("🔍 Waiting for dropdown menu using modern approach...")
            
            # Modern approach: Use role-based locators and wait_for
            try:
                # Wait for dropdown menu to appear
                page.locator('.ant-dropdown-menu, .dropdown-menu, ul[role="menu"]').wait_for(state="visible", timeout=5000)
                logger.info("📋 Dropdown menu detected")
            except:
                logger.info("📋 Using fallback wait for dropdown")
                time.sleep(2)
            
            # Modern approach: Use text-based locators for Activity option
            activity_selectors = [
                page.get_by_role("menuitem", name="Activity"),
                page.get_by_text("Activity"),
                page.locator('li:has-text("Activity")'),
                page.locator('.ant-dropdown-menu-item:has-text("Activity")'),
                page.locator('[role="menuitem"]:has-text("Activity")')
            ]
            
            activity_clicked = False
            for selector in activity_selectors:
                try:
                    selector.wait_for(state="visible", timeout=3000)
                    selector.click()
                    time.sleep(3)  # Wait for activity modal
                    activity_clicked = True
                    logger.info(f"📋 Activity option clicked successfully")
                    break
                except Exception as e:
                    logger.warning(f"⚠️ Activity selector failed: {e}")
                    continue
            
            if not activity_clicked:
                logger.error("❌ Could not click Activity option from dropdown")
                return False
            
            # Wait for activity modal to fully load
            logger.info("⏳ Waiting for activity modal to load...")
            time.sleep(3)
            
            # Wait for modal to appear
            time.sleep(2)
            
            # Step 3: Select Activity Type if available
            if activity_data["has_activity_type"]:
                logger.info(f"📧 Selecting activity type: {activity_type}")
                success = self._select_activity_type(page, activity_type)
                if not success:
                    logger.warning(f"⚠️ Could not select activity type: {activity_type}")
            
            # Step 4: Fill Subject field if available
            if activity_data["has_subject"]:
                logger.info("📝 Filling subject field...")
                success = self._fill_subject_field(page, activity_data["subject"])
                if not success:
                    logger.warning("⚠️ Could not fill subject field")
            
            # Step 5: Fill Activity Date and Time (Enhanced with popup handling)
            if activity_data["gainsight_date"] or activity_data["gainsight_time"]:
                logger.info("📅⏰ Handling date and time fields...")
                
                # First try to click on date field to trigger popup
                date_trigger_success = False
                date_trigger_selectors = [
                    'input[placeholder="Select date"]',
                    'input[placeholder*="Date" i]',
                    'calendar-input',
                    'nz-date-picker',
                    '.calendar-input'
                ]
                
                for selector in date_trigger_selectors:
                    try:
                        if page.locator(selector).count() > 0:
                            page.click(selector)
                            time.sleep(1)
                            date_trigger_success = True
                            logger.info(f"📅 Date field clicked: {selector}")
                            break
                    except:
                        continue
                
                if date_trigger_success:
                    # Try the enhanced popup handler first
                    popup_success = self._wait_for_and_handle_datetime_popup(
                        page, 
                        activity_data["gainsight_date"], 
                        activity_data["gainsight_time"]
                    )
                    
                    if popup_success:
                        logger.info("✅ Date and time filled via popup handler")
                    else:
                        # Fallback to individual field filling
                        if activity_data["gainsight_date"]:
                            logger.info(f"📅 Fallback: Filling activity date: {activity_data['gainsight_date']}")
                            date_success = self._fill_activity_date(page, activity_data["gainsight_date"])
                            if not date_success:
                                logger.warning("⚠️ Could not fill activity date")
                        
                        if activity_data["gainsight_time"]:
                            logger.info(f"⏰ Fallback: Filling activity time: {activity_data['gainsight_time']}")
                            time_success = self._fill_activity_time(page, activity_data["gainsight_time"])
                            if not time_success:
                                logger.warning("⚠️ Could not fill activity time")
                else:
                    # Original individual approach if no date trigger found
                    if activity_data["gainsight_date"]:
                        logger.info(f"📅 Filling activity date: {activity_data['gainsight_date']}")
                        success = self._fill_activity_date(page, activity_data["gainsight_date"])
                        if not success:
                            logger.warning("⚠️ Could not fill activity date")
                    
                    if activity_data["gainsight_time"]:
                        logger.info(f"⏰ Filling activity time: {activity_data['gainsight_time']}")
                        success = self._fill_activity_time(page, activity_data["gainsight_time"])
                        if not success:
                            logger.warning("⚠️ Could not fill activity time")
            
            # Step 6: Fill Note/Plain Text if available
            if activity_data["has_plain_text"]:
                logger.info("📄 Filling note field...")
                success = self._fill_note_field(page, activity_data["plain_text"])
                if not success:
                    logger.warning("⚠️ Could not fill note field")
            
            # Step 7: Fill Internal Recipients if available
            if activity_data["has_author_name"]:
                logger.info(f"👤 Setting internal recipient: {activity_data['author_name']}")
                success = self._fill_internal_recipients(page, activity_data["author_name"])
                if not success:
                    logger.warning("⚠️ Could not set internal recipient")
            
            # Step 8: Select Touchpoint Reason if available
            if activity_data["has_touchpoint_reason"]:
                logger.info(f"🎯 Selecting touchpoint reason: {activity_data['touchpoint_reason']}")
                success = self._select_touchpoint_reason(page, activity_data["touchpoint_reason"])
                if not success:
                    logger.warning("⚠️ Could not select touchpoint reason")
            
            # Step 9: Select Flow Type if available
            if activity_data["has_flow_type"]:
                logger.info(f"🔄 Selecting flow type: {activity_data['flow_type']}")
                success = self._select_flow_type(page, activity_data["flow_type"])
                if not success:
                    logger.warning("⚠️ Could not select flow type")
            
            # Step 10: Submit activity using modern approach
            logger.info("💾 Submitting activity using modern approach...")
            
            # Modern approach: Use role-based locators and better waiting
            submit_selectors = [
                page.get_by_role("button", name="Log Activity"),
                page.get_by_role("button", name="Save"),
                page.get_by_role("button", name="Create"),
                page.get_by_text("Log Activity"),
                page.locator('button:has-text("Log Activity")'),
                page.locator('button:has-text("Save")'),
                page.locator('button[type="submit"]')
            ]
            
            submit_clicked = False
            for selector in submit_selectors:
                try:
                    # Wait for button to be visible and enabled
                    selector.wait_for(state="visible", timeout=5000)
                    selector.click()
                    logger.info(f"✅ Activity submitted successfully")
                    submit_clicked = True
                    time.sleep(3)  # Wait for submission to complete
                    break
                except Exception as e:
                    logger.warning(f"⚠️ Submit selector failed: {e}")
                    continue
            
            if not submit_clicked:
                logger.error("❌ Could not submit activity")
                return False
            
            # Verify success
            time.sleep(3)
            logger.info(f"✅ Activity created successfully: {subject[:30]}...")
            return True
            
        except Exception as e:
            logger.error(f"❌ CSV activity creation failed: {e}")
            page.screenshot(path=f"/Users/<USER>/Desktop/wild_weasel_gainsight_migration/activity_error_{activity_index}_{int(time.time())}.png")
            return False
    
    def _select_activity_type(self, page, activity_type):
        """Select activity type from dropdown"""
        try:
            # Click activity type dropdown
            dropdown_selectors = [
                'gs-activity-type-field nz-select',
                'nz-select[placeholder*="Activity Type" i]',
                'label:has-text("Activity Type") ~ * nz-select',
                '.activity-type-field nz-select'
            ]
            
            dropdown_opened = False
            for selector in dropdown_selectors:
                try:
                    if page.locator(selector).count() > 0:
                        page.click(selector)
                        time.sleep(1)
                        dropdown_opened = True
                        break
                except:
                    continue
            
            if not dropdown_opened:
                return False
            
            # Select the activity type option
            option_selectors = [
                f'nz-option:has-text("{activity_type}")',
                f'li:has-text("{activity_type}")',
                f'.ant-select-dropdown-menu-item:has-text("{activity_type}")'
            ]
            
            for selector in option_selectors:
                try:
                    if page.locator(selector).count() > 0:
                        page.click(selector)
                        time.sleep(1)
                        return True
                except:
                    continue
            
            # JavaScript fallback for activity type selection
            js_success = page.evaluate(f"""(activityType) => {{
                const options = document.querySelectorAll('nz-option, li, .ant-select-dropdown-menu-item');
                for (const option of options) {{
                    const text = option.innerText || option.textContent || '';
                    if (text.trim().toLowerCase() === activityType.toLowerCase()) {{
                        option.click();
                        return true;
                    }}
                }}
                return false;
            }}""", activity_type)
            
            return js_success
            
        except Exception as e:
            logger.error(f"❌ Activity type selection failed: {e}")
            return False
    
    def _fill_subject_field(self, page, subject):
        """Fill the subject field"""
        try:
            subject_selector = '#cdk-overlay-12 > gs-composer > div.gs-timeline-composer.cdk-drag.ng-star-inserted > div.gs-activity-composer > form > div.gs-activity-composer__section > div.gs-activity-composer__right.ng-star-inserted > gs-fields > div > form > div > div:nth-child(3) > gs-text > nz-form-item > nz-form-control > div > span > input'
            
            # Try the provided selector first
            try:
                if page.locator(subject_selector).count() > 0:
                    page.fill(subject_selector, subject)
                    return True
            except:
                pass
            
            # Alternative selectors
            alt_selectors = [
                'input[placeholder*="Subject" i]',
                'gs-text input',
                'nz-form-item:has(label:has-text("Subject")) input',
                'label:has-text("Subject") ~ * input'
            ]
            
            for selector in alt_selectors:
                try:
                    if page.locator(selector).count() > 0:
                        page.fill(selector, subject)
                        return True
                except:
                    continue
            
            return False
            
        except Exception as e:
            logger.error(f"❌ Subject field filling failed: {e}")
            return False
    
    def _fill_activity_date(self, page, date_value):
        """Modern Playwright approach - fill() automatically clears existing values"""
        try:
            logger.info(f"📅 Setting date using modern Playwright approach: {date_value}")
            
            # Modern Playwright best practice: Use fill() - it automatically clears existing values
            date_selectors = [
                'input[placeholder="Select date"]',
                'input[placeholder*="Date" i]',
                'calendar-input input',
                'nz-date-picker input',
                '.ant-calendar-picker-input'
            ]
            
            for selector in date_selectors:
                try:
                    element = page.locator(selector)
                    if element.count() > 0:
                        logger.info(f"📅 Found date field: {selector}")
                        
                        # Modern approach: fill() automatically handles clearing
                        element.fill(date_value)
                        
                        # Press Enter to confirm
                        element.press('Enter')
                        
                        logger.info(f"✅ Date filled and confirmed: {date_value}")
                        return True
                        
                except Exception as e:
                    logger.warning(f"⚠️ Date selector {selector} failed: {e}")
                    continue
            
            # Fallback: Shadow DOM approach (Playwright handles automatically)
            try:
                # Playwright automatically traverses shadow DOM with CSS selectors
                shadow_date_input = page.locator('gs-timeline-widget-overlay input[placeholder*="date" i]')
                if shadow_date_input.count() > 0:
                    shadow_date_input.fill(date_value)
                    shadow_date_input.press('Enter')
                    logger.info("✅ Date filled via shadow DOM traversal")
                    return True
            except Exception as e:
                logger.warning(f"⚠️ Shadow DOM date approach failed: {e}")
            
            logger.warning("⚠️ Could not find or fill date field")
            return False
            
        except Exception as e:
            logger.error(f"❌ Date filling failed: {e}")
            return False
    
    def _fill_activity_time(self, page, time_value):
        """Modern Playwright approach - fill() automatically clears existing values"""
        try:
            # Convert 24-hour format to 12-hour format if needed
            formatted_time = self._convert_to_12hour_format(time_value)
            logger.info(f"⏰ Setting time using modern Playwright approach: {formatted_time}")
            
            # Modern Playwright best practice: Use fill() - it automatically clears existing values
            time_selectors = [
                'input[placeholder*="AM"]',
                'input[placeholder*="PM"]', 
                'input[placeholder*="Time" i]',
                'nz-time-picker input',
                'time-input input',
                '.ant-time-picker-input'
            ]
            
            for selector in time_selectors:
                try:
                    element = page.locator(selector)
                    if element.count() > 0:
                        logger.info(f"⏰ Found time field: {selector}")
                        
                        # Modern approach: fill() automatically handles clearing
                        element.fill(formatted_time)
                        
                        # Press Enter to confirm
                        element.press('Enter')
                        
                        logger.info(f"✅ Time filled and confirmed: {formatted_time}")
                        return True
                        
                except Exception as e:
                    logger.warning(f"⚠️ Time selector {selector} failed: {e}")
                    continue
            
            # Fallback: Shadow DOM approach (Playwright handles automatically)
            try:
                # Playwright automatically traverses shadow DOM with CSS selectors
                shadow_time_input = page.locator('gs-timeline-widget-overlay input[placeholder*="AM"], gs-timeline-widget-overlay input[placeholder*="PM"]')
                if shadow_time_input.count() > 0:
                    shadow_time_input.fill(formatted_time)
                    shadow_time_input.press('Enter')
                    logger.info("✅ Time filled via shadow DOM traversal")
                    return True
            except Exception as e:
                logger.warning(f"⚠️ Shadow DOM time approach failed: {e}")
            
            logger.warning("⚠️ Could not find or fill time field")
            return False
            
        except Exception as e:
            logger.error(f"❌ Time filling failed: {e}")
            return False
    
    def _wait_for_and_handle_datetime_popup(self, page, date_value, time_value):
        """Wait for and handle the date-range popup specifically"""
        try:
            logger.info("🔍 Waiting for date-range popup to appear...")
            
            # Wait for the popup to appear in shadow DOM
            popup_appeared = page.evaluate("""() => {
                return new Promise((resolve) => {
                    let attempts = 0;
                    const maxAttempts = 20;
                    
                    const checkPopup = () => {
                        attempts++;
                        const shadowHost = document.querySelector('body > gs-timeline-widget-overlay');
                        
                        if (shadowHost && shadowHost.shadowRoot) {
                            const popup = shadowHost.shadowRoot.querySelector('[id*="cdk-overlay"] date-range-popup');
                            if (popup) {
                                console.log('Date-range popup found!');
                                resolve(true);
                                return;
                            }
                        }
                        
                        // Also check regular DOM
                        const regularPopup = document.querySelector('date-range-popup, .date-range-popup');
                        if (regularPopup) {
                            console.log('Regular date-range popup found!');
                            resolve(true);
                            return;
                        }
                        
                        if (attempts >= maxAttempts) {
                            console.log('Date-range popup not found after maximum attempts');
                            resolve(false);
                        } else {
                            setTimeout(checkPopup, 200);
                        }
                    };
                    
                    checkPopup();
                });
            }""")
            
            if not popup_appeared:
                logger.warning("⚠️ Date-range popup did not appear within timeout")
                return False
            
            # Handle the popup once it appears
            logger.info("📅 Date-range popup detected, filling values...")
            
            # Fill date and time in the popup
            success = page.evaluate(f"""(dateValue, timeValue) => {{
                try {{
                    const shadowHost = document.querySelector('body > gs-timeline-widget-overlay');
                    let popup = null;
                    
                    if (shadowHost && shadowHost.shadowRoot) {{
                        popup = shadowHost.shadowRoot.querySelector('[id*="cdk-overlay"] date-range-popup');
                    }}
                    
                    if (!popup) {{
                        popup = document.querySelector('date-range-popup, .date-range-popup');
                    }}
                    
                    if (!popup) {{
                        console.error('Could not find date-range popup');
                        return false;
                    }}
                    
                    // Find and fill date input
                    const dateInput = popup.querySelector('calendar-input input, input[placeholder*="date" i]');
                    if (dateInput) {{
                        dateInput.value = '';
                        dateInput.focus();
                        dateInput.value = dateValue;
                        dateInput.dispatchEvent(new Event('input', {{ bubbles: true }}));
                        dateInput.dispatchEvent(new Event('change', {{ bubbles: true }}));
                        console.log('Date filled in popup:', dateValue);
                    }}
                    
                    // Find and fill time input
                    const timeInput = popup.querySelector('nz-time-picker input, input[placeholder*="AM"], input[placeholder*="PM"], input[placeholder*="time" i]');
                    if (timeInput) {{
                        timeInput.value = '';
                        timeInput.focus();
                        timeInput.value = timeValue;
                        timeInput.dispatchEvent(new Event('input', {{ bubbles: true }}));
                        timeInput.dispatchEvent(new Event('change', {{ bubbles: true }}));
                        console.log('Time filled in popup:', timeValue);
                    }}
                    
                    // Look for and click OK/Done/Apply button
                    const confirmButtons = popup.querySelectorAll('button');
                    for (const button of confirmButtons) {{
                        const text = button.innerText || button.textContent || '';
                        if (text.trim().toLowerCase().match(/ok|done|apply|confirm|save/)) {{
                            button.click();
                            console.log('Popup confirmed with button:', text.trim());
                            return true;
                        }}
                    }}
                    
                    return true;
                }} catch (error) {{
                    console.error('Error handling date-range popup:', error);
                    return false;
                }}
            }}""", date_value, self._convert_to_12hour_format(time_value))
            
            if success:
                logger.info("✅ Date-range popup handled successfully")
                time.sleep(2)  # Wait for popup to close
                return True
            else:
                logger.warning("⚠️ Failed to handle date-range popup")
                return False
                
        except Exception as e:
            logger.error(f"❌ Error handling date-range popup: {e}")
            return False

    def _convert_to_12hour_format(self, time_24):
        """Convert 24-hour time to 12-hour format"""
        try:
            if not time_24:
                return ""
            
            # Parse the time
            hour, minute = map(int, time_24.split(':'))
            
            # Convert to 12-hour format
            if hour == 0:
                return f"12:{minute:02d} AM"
            elif hour < 12:
                return f"{hour}:{minute:02d} AM"
            elif hour == 12:
                return f"12:{minute:02d} PM"
            else:
                return f"{hour-12}:{minute:02d} PM"
                
        except Exception as e:
            logger.warning(f"⚠️ Time conversion failed for '{time_24}': {e}")
            return time_24  # Return original if conversion fails
    
    def _fill_note_field(self, page, note_text):
        """Modern Playwright approach with automatic shadow DOM traversal"""
        try:
            logger.info(f"📝 Filling note field with CSV Plain Text using modern approach: {note_text[:100]}...")
            
            if not note_text or note_text.strip() == "":
                logger.warning("⚠️ No Plain Text data from CSV to fill")
                return False
            
            # Modern Playwright approach: CSS selectors automatically traverse shadow DOM
            note_selectors = [
                'gs-rich-text-editor div[contenteditable="true"]',
                '.rte-editor.rte-editor-focus',
                '.rte-editor',
                'div[contenteditable="true"]',
                'textarea[placeholder*="notes" i]',
                'textarea[placeholder*="mention" i]'
            ]
            
            for selector in note_selectors:
                try:
                    element = page.locator(selector)
                    if element.count() > 0:
                        logger.info(f"📝 Found note field: {selector}")
                        
                        # Modern approach: fill() for contenteditable elements
                        if 'contenteditable' in selector or 'rte-editor' in selector:
                            # For contenteditable elements, use click + fill
                            element.click()
                            element.fill(note_text)
                        else:
                            # For textarea elements, use direct fill
                            element.fill(note_text)
                        
                        logger.info(f"✅ Note filled with CSV Plain Text: {selector}")
                        return True
                        
                except Exception as e:
                    logger.warning(f"⚠️ Note selector {selector} failed: {e}")
                    continue
            
            # Fallback: Direct shadow DOM CSS selector (Playwright handles automatically)
            try:
                shadow_note_element = page.locator('gs-timeline-widget-overlay gs-rich-text-editor div[contenteditable="true"]')
                if shadow_note_element.count() > 0:
                    shadow_note_element.click()
                    shadow_note_element.fill(note_text)
                    logger.info("✅ Note filled via shadow DOM traversal")
                    return True
            except Exception as e:
                logger.warning(f"⚠️ Shadow DOM note approach failed: {e}")
            
            logger.warning("⚠️ Could not find or fill note field")
            return False
            
        except Exception as e:
            logger.error(f"❌ Note field filling failed: {e}")
            return False
    
    def _fill_internal_recipients(self, page, author_name):
        """Fill internal recipients field"""
        try:
            # Click on the "Search Users" input to open the dropdown
            search_selectors = [
                'input[placeholder="Search Users"]',
                'input[placeholder*="Search" i]',
                'input[placeholder*="Users" i]'
            ]
            
            search_clicked = False
            for selector in search_selectors:
                try:
                    if page.locator(selector).count() > 0:
                        page.click(selector)
                        search_clicked = True
                        break
                except:
                    continue
            
            if not search_clicked:
                return False
            
            # Wait for overlay and type the name
            overlay_selectors = [
                '#cdk-overlay-17 input',
                '.cdk-overlay-container input',
                '.ant-select-dropdown input'
            ]
            
            for selector in overlay_selectors:
                try:
                    page.wait_for_selector(selector, timeout=3000)
                    page.fill(selector, author_name)
                    time.sleep(1)
                    break
                except:
                    continue
            
            # Click the matching user from dropdown
            user_selectors = [
                f'text={author_name}',
                f'li:has-text("{author_name}")',
                f'.ant-select-dropdown-menu-item:has-text("{author_name}")'
            ]
            
            for selector in user_selectors:
                try:
                    if page.locator(selector).count() > 0:
                        page.click(selector)
                        return True
                except:
                    continue
            
            return False
            
        except Exception as e:
            logger.error(f"❌ Internal recipients filling failed: {e}")
            return False
    
    def _select_touchpoint_reason(self, page, touchpoint_reason):
        """Select touchpoint reason from dropdown"""
        try:
            # Click the touchpoint reason dropdown
            dropdown_selectors = [
                'label:text("Touchpoint Reason") ~ div select',
                'gs-touchpoint-reason-field select',
                'nz-select[placeholder*="Touchpoint" i]'
            ]
            
            dropdown_opened = False
            for selector in dropdown_selectors:
                try:
                    if page.locator(selector).count() > 0:
                        page.click(selector)
                        time.sleep(1)
                        dropdown_opened = True
                        break
                except:
                    continue
            
            if not dropdown_opened:
                return False
            
            # Wait for dropdown options and select matching option
            page.wait_for_selector('.cdk-overlay-container', timeout=3000)
            
            # Use the provided JavaScript approach
            js_success = page.evaluate(f"""(value) => {{
                const items = Array.from(document.querySelectorAll('.cdk-overlay-container div, li, span'));
                const target = items.find(el => el.textContent?.trim().toLowerCase() === value.toLowerCase());
                if (target) {{
                    target.scrollIntoView();
                    target.click();
                    return true;
                }}
                return false;
            }}""", touchpoint_reason)
            
            return js_success
            
        except Exception as e:
            logger.error(f"❌ Touchpoint reason selection failed: {e}")
            return False
    
    def _select_flow_type(self, page, flow_type):
        """Select flow type from dropdown"""
        try:
            flow_type_selector = '#cdk-overlay-12 > gs-composer > div.gs-timeline-composer.cdk-drag.ng-star-inserted > div.gs-activity-composer > form > div.gs-activity-composer__section > div.gs-activity-composer__right.ng-star-inserted > gs-fields > div > form > div > div:nth-child(9) > gs-picklist-field > nz-form-item > nz-form-control > div > span > nz-select > div > div'
            
            # Try the provided selector first
            try:
                if page.locator(flow_type_selector).count() > 0:
                    page.click(flow_type_selector)
                    time.sleep(1)
                else:
                    # Alternative selectors
                    alt_selectors = [
                        'gs-picklist-field nz-select',
                        'nz-select[placeholder*="Flow" i]',
                        'label:has-text("Flow Type") ~ * nz-select'
                    ]
                    
                    dropdown_opened = False
                    for selector in alt_selectors:
                        try:
                            if page.locator(selector).count() > 0:
                                page.click(selector)
                                time.sleep(1)
                                dropdown_opened = True
                                break
                        except:
                            continue
                    
                    if not dropdown_opened:
                        return False
                
                # Select matching flow type option
                js_success = page.evaluate(f"""(value) => {{
                    const items = Array.from(document.querySelectorAll('.cdk-overlay-container div, li, span, nz-option'));
                    const target = items.find(el => el.textContent?.trim().toLowerCase() === value.toLowerCase());
                    if (target) {{
                        target.scrollIntoView();
                        target.click();
                        return true;
                    }}
                    return false;
                }}""", flow_type)
                
                return js_success
                
            except Exception as e:
                logger.error(f"❌ Flow type dropdown click failed: {e}")
                return False
            
        except Exception as e:
            logger.error(f"❌ Flow type selection failed: {e}")
            return False
    
    def migrate_csv_activities(self, page):
        """Migrate all CSV activities using UI automation"""
        logger.info("🔄 Starting CSV UI automation migration...")
        
        successful_count = 0
        failed_count = 0
        
        for i, activity in enumerate(self.activities):
            self.migration_results["current_activity"] = i + 1
            logger.info(f"📝 Processing activity {i+1}/{len(self.activities)}: {activity['subject'][:50]}...")
            
            try:
                if self.create_activity_from_csv(page, activity, i):
                    successful_count += 1
                    self.migration_results["successful"].append({
                        "row_number": activity["row_number"],
                        "subject": activity["subject"],
                        "activity_type": activity["activity_type"],
                        "method": "CSV_UI_AUTOMATION",
                        "timestamp": datetime.now().isoformat()
                    })
                    logger.info(f"✅ CSV UI Success: {activity['subject'][:50]}")
                else:
                    failed_count += 1
                    self.migration_results["failed"].append({
                        "row_number": activity["row_number"],
                        "subject": activity["subject"],
                        "activity_type": activity["activity_type"],
                        "error": "UI automation failed",
                        "timestamp": datetime.now().isoformat()
                    })
                    logger.error(f"❌ CSV UI failed for: {activity['subject'][:50]}")
                
                # Save progress periodically
                if i % 10 == 0:
                    self._save_progress()
                
            except Exception as e:
                failed_count += 1
                self.migration_results["failed"].append({
                    "row_number": activity["row_number"],
                    "subject": activity["subject"],
                    "activity_type": activity["activity_type"],
                    "error": f"UI exception: {str(e)}",
                    "timestamp": datetime.now().isoformat()
                })
                logger.error(f"❌ CSV UI exception for {activity['subject'][:50]}: {e}")
            
            # Delay between activities
            time.sleep(2)
        
        logger.info(f"🔄 CSV UI Migration complete: {successful_count} successful, {failed_count} failed")
        return successful_count, failed_count
    
    def _save_progress(self):
        """Save current progress"""
        try:
            import json
            progress_data = {
                "current_activity": self.migration_results["current_activity"],
                "total_activities": len(self.activities),
                "successful_count": len(self.migration_results["successful"]),
                "failed_count": len(self.migration_results["failed"]),
                "timestamp": datetime.now().isoformat()
            }
            
            with open(self.config["progress_log"], 'w') as f:
                json.dump(progress_data, f, indent=2, default=str)
                
        except Exception as e:
            logger.warning(f"⚠️ Failed to save progress: {e}")
    
    def save_results(self):
        """Save comprehensive migration results"""
        try:
            import json
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            
            # Save successful migrations
            success_file = self.config["success_log"].replace('.json', f'_{timestamp}.json')
            with open(success_file, 'w') as f:
                json.dump(self.migration_results["successful"], f, indent=2, default=str)
            
            # Save failed migrations
            failed_file = self.config["failed_log"].replace('.json', f'_{timestamp}.json')
            with open(failed_file, 'w') as f:
                json.dump(self.migration_results["failed"], f, indent=2, default=str)
            
            logger.info(f"💾 Results saved: {success_file}, {failed_file}")
            
        except Exception as e:
            logger.error(f"❌ Failed to save results: {e}")
    
    def generate_comprehensive_report(self):
        """Generate comprehensive final report"""
        total_activities = len(self.activities)
        successful = len(self.migration_results["successful"])
        failed = len(self.migration_results["failed"])
        success_rate = (successful / total_activities * 100) if total_activities > 0 else 0
        
        report = f"""
🐺 WILD WEASEL CSV UI AUTOMATION REPORT
{'='*100}
📊 MIGRATION STATISTICS:
  Total Activities: {total_activities}
  ✅ Successful: {successful}
  ❌ Failed: {failed}
  📈 Success Rate: {success_rate:.1f}%

🔧 METHOD: CSV-Driven UI Browser Automation
  → Complete CSV parsing and processing
  → Intelligent date/time format conversion
  → Dynamic field mapping with null handling
  → Advanced browser automation with fallback strategies

📋 DATA SOURCE: ICICI Bank Activity Migration
  CSV File: {self.config['csv_file_path']}
  Target: Gainsight Timeline Activities via UI
  Company: ICICI Bank

🔍 FIELD PROCESSING SUMMARY:
  → Subject: {sum(1 for a in self.activities if a['has_subject'])} activities
  → Activity Type: {sum(1 for a in self.activities if a['has_activity_type'])} activities
  → Plain Text: {sum(1 for a in self.activities if a['has_plain_text'])} activities
  → Author Name: {sum(1 for a in self.activities if a['has_author_name'])} activities
  → Flow Type: {sum(1 for a in self.activities if a['has_flow_type'])} activities
  → Touchpoint Reason: {sum(1 for a in self.activities if a['has_touchpoint_reason'])} activities

🐺 Wild Weasel CSV UI Automation Status: {'✅ MISSION ACCOMPLISHED' if failed == 0 else f'⚠️ PARTIALLY COMPLETED ({successful}/{total_activities} successful)'}
{'='*100}
"""
        
        print(report)
        
        # Save report to file
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        report_file = f"/Users/<USER>/Desktop/wild_weasel_gainsight_migration/csv_ui_migration_report_{timestamp}.txt"
        try:
            with open(report_file, 'w') as f:
                f.write(report)
            logger.info(f"📄 Report saved to: {report_file}")
        except Exception as e:
            logger.warning(f"⚠️ Failed to save report: {e}")
    
    def execute_csv_ui_mission(self):
        """Execute the complete CSV UI automation mission"""
        try:
            self.mission_brief()
            
            # Load CSV activities
            if not self.load_csv_activities():
                logger.error("❌ Mission aborted: Could not load CSV activities")
                return False
            
            self.migration_results["start_time"] = datetime.now()
            self.migration_results["total_processed"] = len(self.activities)
            
            # Execute UI automation
            with sync_playwright() as playwright:
                browser = playwright.chromium.launch(
                    headless=False, 
                    slow_mo=500
                )
                context = browser.new_context(
                    viewport=None,  # Use full screen
                    user_agent="Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36"
                )
                page = context.new_page()
                
                # Maximize the page after creation
                page.set_viewport_size({"width": 1920, "height": 1080})
                page.evaluate("window.moveTo(0, 0); window.resizeTo(screen.width, screen.height);")
                page = context.new_page()
                
                # Login
                if not self.login_to_gainsight(page):
                    logger.error("❌ Mission aborted: Login failed")
                    browser.close()
                    return False
                
                # Navigate to timeline
                if not self.navigate_to_timeline(page):
                    logger.error("❌ Mission aborted: Timeline navigation failed")
                    browser.close()
                    return False
                
                # Migrate CSV activities
                self.migrate_csv_activities(page)
                
                # Keep browser open for a moment to see final state
                time.sleep(5)
                browser.close()
            
            self.migration_results["end_time"] = datetime.now()
            
            # Save results and generate report
            self.save_results()
            self.generate_comprehensive_report()
            
            logger.info("🐺 Wild Weasel CSV UI Automation mission completed!")
            return True
            
        except Exception as e:
            logger.error(f"❌ CSV UI Mission execution failed: {e}")
            return False

def main():
    """Main execution function"""
    try:
        logger.info("🐺 Initializing Wild Weasel CSV UI Automation Agent...")
        agent = WildWeaselCSVUIAutomation()
        agent.execute_csv_ui_mission()
        
    except KeyboardInterrupt:
        print("\n🐺 Wild Weasel CSV UI Automation interrupted by user")
        sys.exit(0)
    except Exception as e:
        print(f"\n❌ Wild Weasel CSV UI Automation failed: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()

2025-05-30 08:33:53,390 - INFO - 🐺 Starting UI Automation Test...
2025-05-30 08:33:53,390 - ERROR - ❌ CSV file not found: /Users/<USER>/Desktop/totango/icici_activities_for_gainsight.csv
2025-05-30 08:36:34,621 - INFO - 🐺 Starting UI Automation Test...
2025-05-30 08:36:34,621 - ERROR - ❌ CSV file not found: /Users/<USER>/Desktop/totango/icici_activities_for_gainsight.csv
2025-05-30 08:38:10,707 - INFO - 🐺 Starting UI Automation Test...
2025-05-30 08:38:10,708 - INFO - 📊 Loaded 3 test activities
2025-05-30 08:38:18,933 - INFO - 📝 Creating test activity 1: ICICI: Mend Platform Access...
2025-05-30 08:38:28,936 - ERROR - ❌ Test activity failed: Page.wait_for_selector: Timeout 10000ms exceeded.
Call log:
  - waiting for locator("button:has-text(\"Add Activity\")") to be visible

2025-05-30 08:38:32,031 - INFO - 📝 Creating test activity 2: ICICI: Mend Platform Access...
2025-05-30 08:38:35,630 - ERROR - ❌ Test activity failed: Page.wait_for_selector: Target page, context or browser has been closed
Call log:
  - waiting for locator("button:has-text(\"Add Activity\")") to be visible

2025-05-30 08:38:35,733 - ERROR - ❌ Test failed: Page.screenshot: Target page, context or browser has been closed
2025-05-30 08:39:19,354 - INFO - 🐺 Starting UI Automation Test...
2025-05-30 08:39:19,355 - INFO - 📊 Loaded 3 test activities
2025-05-30 08:39:27,499 - INFO - 📝 Creating test activity 1: ICICI: Mend Platform Access...
2025-05-30 08:39:37,503 - ERROR - ❌ Test activity failed: Page.wait_for_selector: Timeout 10000ms exceeded.
Call log:
  - waiting for locator("button:has-text(\"Add Activity\")") to be visible

2025-05-30 08:39:40,589 - INFO - 📝 Creating test activity 2: ICICI: Mend Platform Access...
2025-05-30 08:39:45,569 - ERROR - ❌ Test activity failed: Page.wait_for_selector: Target page, context or browser has been closed
Call log:
  - waiting for locator("button:has-text(\"Add Activity\")") to be visible

2025-05-30 08:39:45,662 - ERROR - ❌ Test failed: Page.screenshot: Target page, context or browser has been closed

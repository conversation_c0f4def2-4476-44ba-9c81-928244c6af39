#!/bin/bash
# 🐺 Wild Weasel Quick Launcher
# Simple script to launch Wild Weasel components

echo "🐺==============================================================================="
echo "  WILD WEASEL v5.0 Enhanced - Quick Launcher"
echo "==============================================================================="
echo ""

# Check if we're in the right directory
if [ ! -f "wild_weasel_agent_v5_enhanced.py" ]; then
    echo "❌ Error: Not in Wild Weasel directory"
    echo "Please run this script from: /Users/<USER>/Desktop/wild_weasel_gainsight_migration"
    exit 1
fi

# Check Python
echo "🔍 Checking Python installation..."
python3 --version
if [ $? -ne 0 ]; then
    echo "❌ Python 3 not found. Please install Python 3."
    exit 1
fi

# Check required packages
echo "📦 Checking required packages..."
python3 -c "import playwright, requests, json, logging" 2>/dev/null
if [ $? -ne 0 ]; then
    echo "⚠️  Some packages may be missing. Installing requirements..."
    pip3 install playwright requests
    playwright install chromium
fi

echo "✅ System checks passed!"
echo ""

# Show options
echo "Choose your Wild Weasel mission:"
echo ""
echo "1. 🎯 Quick Test Runner (RECOMMENDED for first time)"
echo "   → Interactive menu with all options"
echo ""
echo "2. 🔧 Fix Payloads Only"
echo "   → Fix empty ID fields and validate structure"
echo ""
echo "3. 🚀 Run V5 Enhanced Migration"
echo "   → Full migration with enhanced features"
echo ""
echo "4. 🔄 Run V4 Final Migration"
echo "   → Fallback migration option"
echo ""
echo "5. 📖 Show README"
echo "   → View complete documentation"
echo ""

read -p "Enter your choice (1-5): " choice

case $choice in
    1)
        echo "🎯 Launching Quick Test Runner..."
        python3 quick_test_runner.py
        ;;
    2)
        echo "🔧 Running Payload Fixer..."
        python3 payload_fixer.py
        ;;
    3)
        echo "🚀 Launching V5 Enhanced Migration..."
        echo "⚠️  This will open a browser window. Have your Gainsight credentials ready!"
        read -p "Press Enter to continue or Ctrl+C to cancel..."
        python3 wild_weasel_agent_v5_enhanced.py
        ;;
    4)
        echo "🔄 Launching V4 Final Migration..."
        echo "⚠️  This will open a browser window. Have your Gainsight credentials ready!"
        read -p "Press Enter to continue or Ctrl+C to cancel..."
        python3 wild_weasel_agent_v4_final.py
        ;;
    5)
        echo "📖 Showing README..."
        if command -v less >/dev/null 2>&1; then
            less README_ENHANCED.md
        else
            cat README_ENHANCED.md
        fi
        ;;
    *)
        echo "❌ Invalid choice. Please run the script again."
        exit 1
        ;;
esac

echo ""
echo "🐺 Wild Weasel mission complete!"
echo "==============================================================================="

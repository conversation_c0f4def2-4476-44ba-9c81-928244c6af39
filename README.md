# Wild Weasel v4.0 Final - Gainsight Migration Automation

## Overview
This enhanced Wild Weasel automation system converts Totango email activities to Gainsight format and automates the process of logging them into Gainsight through the web interface.

## Files Created

### 1. `converted_gainsight_activities.json`
- Converted data from the original Totango format to a simplified Gainsight-ready format
- Contains 4 email activities with proper structure for UI automation
- Each record includes: subject, content, plainText, activityDate, author, authorEmail, type

### 2. `wild_weasel_agent_v4_final.py`
- Enhanced UI automation script using Playwright
- Properly fills subject and notes fields in Gainsight
- Includes robust error handling and retry mechanisms
- Comprehensive logging for debugging

## Key Improvements in v4.0

### Data Conversion
- ✅ Proper HTML content cleaning and plain text extraction
- ✅ Structured notes formatting with metadata
- ✅ Date conversion from timestamps to readable format
- ✅ Author information preservation

### UI Automation Enhancements
- ✅ Multiple selector strategies for each form field
- ✅ Robust subject field filling
- ✅ Comprehensive notes content formatting
- ✅ Retry mechanisms for failed operations
- ✅ Detailed logging and error screenshots
- ✅ Support for different Gainsight UI variations

### Form Field Handling
The script now properly handles:
- **Subject Field**: Multiple selector strategies to find and fill the subject input
- **Notes Field**: Supports both textarea and contenteditable fields
- **Activity Type**: Automatically selects "Email" type
- **Save/Submit**: Multiple strategies to submit the form

## Setup Instructions

### 1. Install Dependencies
```bash
pip install playwright beautifulsoup4
playwright install
```

### 2. Configure Credentials
Edit the script and update these variables:
```python
GAINSIGHT_USERNAME = "your_actual_username"
GAINSIGHT_PASSWORD = "your_actual_password"
```

### 3. Run the Automation
```bash
cd /Users/<USER>/Desktop/wild_weasel_gainsight_migration
python3 wild_weasel_agent_v4_final.py
```

## Data Structure

### Converted JSON Format
```json
{
  "id": 1,
  "subject": "Re: Queries on Licensing Policy related details - ICICI Bank",
  "content": "<html content>",
  "plainText": "Clean text version",
  "activityDate": *************,
  "author": "Oksana Shtril",
  "authorEmail": "<EMAIL>",
  "type": "EMAIL"
}
```

### Notes Content Format
The script formats notes with this structure:
```
Email Activity - Migrated from Totango

Subject: [Original Subject]

Author: [Author Name] ([Email])
Date: [Formatted Date]

Content:
[Clean plain text content]

---
Migration Source: Wild Weasel v4.0 Final
Migration Date: [Current timestamp]
```

## Automation Flow

1. **Login**: Attempts multiple login strategies for Gainsight
2. **Navigation**: Searches for and navigates to ICICI company C360 page
3. **Activity Logging**: For each email activity:
   - Clicks "Log Activity" button
   - Selects "Email" activity type
   - Fills subject field with cleaned subject text
   - Fills notes field with formatted content
   - Saves the activity
4. **Error Handling**: Takes screenshots on errors and retries failed operations

## Key Features

### Robust Field Detection
The script uses multiple selector strategies for each field:
- Subject field: 7 different selector patterns
- Notes field: 13 different selector patterns including contenteditable
- Buttons: Multiple text and attribute-based selectors

### Content Cleaning
- Removes HTML tags from content
- Normalizes whitespace
- Handles special characters
- Preserves important formatting in notes

### Error Recovery
- Retry mechanisms for failed operations
- Screenshot capture on errors
- Comprehensive logging
- Graceful fallbacks for missing elements

## Troubleshooting

### Common Issues

1. **Login Issues**
   - Verify credentials in the script
   - Check if Gainsight URL is correct
   - Look for login error screenshots

2. **Field Not Found**
   - The script tries multiple selectors
   - Check browser console for any errors
   - Screenshots are taken on failures

3. **Activity Not Saved**
   - Verify all required fields are filled
   - Check for validation errors in Gainsight
   - Review automation logs

### Debug Mode
Set `headless=False` in the script to watch the automation in action:
```python
self.browser = await playwright.chromium.launch(
    headless=False,  # Watch the automation
    slow_mo=1000     # Slow down for debugging
)
```

## Logging
The script creates detailed logs in:
- Console output
- `wild_weasel_automation.log` file

Log levels include:
- INFO: General progress
- WARNING: Non-fatal issues
- ERROR: Failures with details

## Success Metrics
The script tracks:
- Total activities processed
- Successfully logged activities
- Failed activities with reasons
- Overall completion status

## Wild Weasel Mission Statement
> "Migrations are hard, painful, in some cases boring (unless you're a torture junkie). Wild Weasel is designed to be sneaky. Its mission is to log into a SAAS platform as an agent, look for structured or unstructured information, download and classify that information, and report back on status."

This v4.0 Final version fulfills the Wild Weasel charter by:
1. ✅ Logging into Gainsight as an agent
2. ✅ Processing structured information from Totango
3. ✅ Properly formatting and uploading to Gainsight
4. ✅ Comprehensive status reporting
5. ✅ Ready for cleanup operations

## Next Steps
1. Configure credentials
2. Test with a single activity first
3. Run full migration
4. Verify data in Gainsight
5. Clean up source data as needed

---
*Wild Weasel v4.0 Final - Mission Ready for Gainsight Migration*

#!/usr/bin/env python3
"""
🐺 Wild Weasel - Multi-Activity Type Totango Data Analysis & Extraction
================================================================================
Mission: Extract ALL activity types using proper ID mapping from Totango timeline data
Target: Prepare structured data for Gainsight migration with correct type mapping

FEATURES:
- Extracts all configured activity types (Email, Calls, Meetings, Notes, etc.)
- Maps to correct Gainsight types (both default and custom)
- Handles activity-specific transformations
- Comprehensive reporting and validation
- API-ready format for Gainsight Timeline

GAINSIGHT TYPE MAPPING:
Default Types: Email→Email, Telephone Call→Call, Web Meeting→Meeting, Internal Note→Update
Custom Types: In-Person Meeting, Gong Call, Feedback, Inbound, Slack
"""

import json
import os
import sys
from datetime import datetime
from typing import List, Dict, Any, Tuple
import logging

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger("WildWeasel-MultiActivity")

class TotangoActivityExtractor:
    """Extract and transform multiple activity types from Totango data for Gainsight migration"""
    
    def __init__(self, totango_data_dir: str = "/Users/<USER>/Desktop/Totango"):
        self.totango_data_dir = totango_data_dir
        self.icici_file = os.path.join(totango_data_dir, "ICICI.json")
        self.id_mapping_file = os.path.join(totango_data_dir, "ID.json")
        self.touchpoint_reasons_file = os.path.join(totango_data_dir, "Touchpoint_reason.JSON")
        
        # All available activity types with CORRECT Gainsight type mapping
        self.all_activity_types = {
            "internal_note": {
                "id": "991d1b35-4f9e-41fe-b23b-58adb2154e1c",
                "display_name": "Internal Note",
                "gainsight_type": "Update",  # Default Gainsight type
                "used": 2491,
                "category": "default"
            },
            "feedback": {
                "id": "02d73562-65ea-49a0-8ce3-b4283fbfec0b", 
                "display_name": "Feedback",
                "gainsight_type": "Feedback",  # Custom Gainsight type
                "used": 3,
                "category": "custom"
            },
            "gong_call": {
                "id": "0d28957e-c390-4f16-b23e-94e340939b6a",
                "display_name": "Gong Call", 
                "gainsight_type": "Gong Call",  # Custom Gainsight type
                "used": 2823,
                "category": "custom"
            },
            "email": {
                "id": "68c0d13a-40f1-47e4-9bb4-3d1fb6a16515",
                "display_name": "Email",
                "gainsight_type": "Email",  # Default Gainsight type
                "used": 27361,
                "category": "default"
            },
            "inbound": {
                "id": "7695ab9e-cde8-43a2-be91-eb5aa121655f",
                "display_name": "Inbound",
                "gainsight_type": "Inbound",  # Custom Gainsight type
                "used": 3391,
                "category": "custom"
            },
            "telephone_call": {
                "id": "8d08857d-c9c4-4192-90ff-7f295e25a4d9",
                "display_name": "Telephone call",
                "gainsight_type": "Call",  # Default Gainsight type (mapped from "Telephone Call")
                "used": 22,
                "category": "default"
            },
            "web_meeting": {
                "id": "93b4649c-8459-4f56-be3f-be75f7506ee0",
                "display_name": "Web meeting", 
                "gainsight_type": "Meeting",  # Default Gainsight type (mapped from "Web Meeting")
                "used": 12597,
                "category": "default"
            },
            "slack": {
                "id": "bd296ff0-8e5b-4447-b0aa-24c8b146fc09",
                "display_name": "Slack",
                "gainsight_type": "Slack",  # Custom Gainsight type
                "used": 30,
                "category": "custom"
            },
            "in_person_meeting": {
                "id": "ccd1842d-ac26-49c8-be4c-7e1686de4609",
                "display_name": "In person meeting",
                "gainsight_type": "In-Person Meeting",  # Custom Gainsight type
                "used": 14,
                "category": "custom"
            }
        }
        
        # Configure which activity types you want to extract
        # Ordered by usage volume (highest first)
        self.target_activity_types = [
            "email",             # 27,361 → Email (default)
            "web_meeting",       # 12,597 → Meeting (default)  
            "inbound",           # 3,391 → Inbound (custom)
            "gong_call",         # 2,823 → Gong Call (custom)
            "internal_note",     # 2,491 → Update (default)
            "slack",             # 30 → Slack (custom)
            "telephone_call",    # 22 → Call (default)
            "in_person_meeting", # 14 → In-Person Meeting (custom)
            "feedback"           # 3 → Feedback (custom)
        ]
        
        logger.info(f"🎯 Configured to extract {len(self.target_activity_types)} activity types")
        self.show_target_summary()
    
    def show_target_summary(self):
        """Display summary of target activity types and expected volumes"""
        total_expected = 0
        default_types = []
        custom_types = []
        
        print("\n" + "="*80)
        print("🎯 TARGET ACTIVITY TYPES CONFIGURATION")
        print("="*80)
        
        for activity_key in self.target_activity_types:
            if activity_key in self.all_activity_types:
                activity_info = self.all_activity_types[activity_key]
                total_expected += activity_info['used']
                
                type_info = {
                    'totango': activity_info['display_name'],
                    'gainsight': activity_info['gainsight_type'],
                    'count': activity_info['used'],
                    'key': activity_key
                }
                
                if activity_info['category'] == 'default':
                    default_types.append(type_info)
                else:
                    custom_types.append(type_info)
        
        print("📋 DEFAULT GAINSIGHT TYPES:")
        for type_info in default_types:
            print(f"   {type_info['totango']:20} → {type_info['gainsight']:20} ({type_info['count']:,} activities)")
        
        print("\n🔧 CUSTOM GAINSIGHT TYPES (manually created):")
        for type_info in custom_types:
            print(f"   {type_info['totango']:20} → {type_info['gainsight']:20} ({type_info['count']:,} activities)")
        
        print("="*80)
        print(f"📊 Total Expected Activities: {total_expected:,}")
        print(f"📋 Default Types: {len(default_types)} ({sum(t['count'] for t in default_types):,} activities)")
        print(f"🔧 Custom Types: {len(custom_types)} ({sum(t['count'] for t in custom_types):,} activities)")
        print("="*80 + "\n")
    
    def load_supporting_data(self) -> Tuple[Dict, Dict]:
        """Load ID mappings and touchpoint reasons with proper validation"""
        id_mappings = {}
        touchpoint_reasons = {}
        
        try:
            if os.path.exists(self.id_mapping_file):
                with open(self.id_mapping_file, 'r') as f:
                    id_data = json.load(f)
                    # Convert to dict with id as key for easy lookup
                    for item in id_data:
                        id_mappings[item['id']] = item
                logger.info(f"✅ Loaded {len(id_mappings)} ID mappings")
                
                # Verify all target activity types exist
                missing_types = []
                for activity_key, activity_info in self.all_activity_types.items():
                    if activity_key in self.target_activity_types:
                        if activity_info['id'] not in id_mappings:
                            missing_types.append(f"{activity_info['display_name']} ({activity_info['id']})")
                        else:
                            logger.info(f"🎯 Found {activity_info['display_name']}: {activity_info['id']}")
                
                if missing_types:
                    logger.warning(f"⚠️ Missing activity types in ID.json: {', '.join(missing_types)}")
                    
        except Exception as e:
            logger.error(f"❌ Could not load ID mappings: {e}")
            
        try:
            if os.path.exists(self.touchpoint_reasons_file):
                with open(self.touchpoint_reasons_file, 'r') as f:
                    touchpoint_data = json.load(f)
                    # Convert to dict with id as key for easy lookup
                    for item in touchpoint_data:
                        touchpoint_reasons[item['id']] = item
                logger.info(f"✅ Loaded {len(touchpoint_reasons)} touchpoint reasons")
        except Exception as e:
            logger.error(f"❌ Could not load touchpoint reasons: {e}")
            
        return id_mappings, touchpoint_reasons
    
    def is_target_activity(self, activity: Dict[str, Any], id_mappings: Dict) -> Tuple[bool, str, Dict]:
        """Determine if a Totango activity matches any of our target types"""
        properties = activity.get('properties', {})
        
        # Check meeting_type against all target activity types
        meeting_type = properties.get('meeting_type', '')
        for activity_key in self.target_activity_types:
            if activity_key in self.all_activity_types:
                activity_info = self.all_activity_types[activity_key]
                if meeting_type == activity_info['id']:
                    logger.debug(f"✅ Found {activity_info['display_name']} via meeting_type: {meeting_type}")
                    return True, activity_key, activity_info
        
        # Check activity_type_id mapping
        activity_type_id = properties.get('activity_type_id', '')
        if activity_type_id and activity_type_id in id_mappings:
            mapping = id_mappings[activity_type_id]
            display_name = mapping.get('display_name', '')
            
            # Check if display_name matches any of our target types
            for activity_key in self.target_activity_types:
                if activity_key in self.all_activity_types:
                    activity_info = self.all_activity_types[activity_key]
                    if display_name == activity_info['display_name']:
                        logger.debug(f"✅ Found {display_name} via activity_type_id: {activity_type_id}")
                        return True, activity_key, activity_info
        
        return False, None, None
    
    def extract_subject_by_type(self, activity: Dict[str, Any], activity_key: str, display_name: str) -> str:
        """Extract subject based on activity type with enhanced logic"""
        properties = activity.get('properties', {})
        
        # Default subject extraction
        subject = (properties.get('subject') or 
                  properties.get('name') or 
                  properties.get('title') or 
                  f"{display_name} Activity")
        
        # Activity type specific enhancements
        if activity_key == 'gong_call':
            # For Gong calls, add call-specific context
            duration = properties.get('duration_minutes', '')
            participants = properties.get('participants_count', '')
            if duration:
                subject = f"Gong Call: {subject} ({duration} min)"
            elif participants:
                subject = f"Gong Call: {subject} ({participants} participants)"
            else:
                subject = f"Gong Call: {subject}"
                
        elif activity_key == 'inbound':
            # For inbound activities, mark as inbound
            if not subject.lower().startswith('inbound'):
                subject = f"Inbound: {subject}"
                
        elif activity_key == 'slack':
            # For Slack, include channel context
            channel = properties.get('channel', '')
            if channel:
                subject = f"Slack [{channel}]: {subject}"
            else:
                subject = f"Slack: {subject}"
                
        elif activity_key == 'internal_note':
            # For internal notes, ensure internal designation
            if not any(keyword in subject.lower() for keyword in ['internal', 'note']):
                subject = f"Internal Note: {subject}"
                
        elif activity_key == 'feedback':
            # For feedback, ensure feedback designation
            if not subject.lower().startswith('feedback'):
                subject = f"Feedback: {subject}"
                
        elif activity_key == 'web_meeting':
            # For web meetings, add meeting context
            meeting_platform = properties.get('platform', '')
            if meeting_platform:
                subject = f"Web Meeting [{meeting_platform}]: {subject}"
            else:
                subject = f"Web Meeting: {subject}"
                
        elif activity_key == 'in_person_meeting':
            # For in-person meetings, add location if available
            location = properties.get('location', '')
            if location:
                subject = f"In-Person Meeting [{location}]: {subject}"
            else:
                subject = f"In-Person Meeting: {subject}"
                
        elif activity_key == 'telephone_call':
            # For telephone calls, add call context
            phone_number = properties.get('phone_number', '')
            if phone_number:
                subject = f"Call to {phone_number}: {subject}"
            else:
                subject = f"Telephone Call: {subject}"
        
        # Clean subject (remove "Totango Campaign:" prefix if present)
        if subject.startswith('Totango Campaign:'):
            subject = subject.replace('Totango Campaign:', '').strip()
        
        return subject
    
    def extract_content_by_type(self, activity: Dict[str, Any], activity_key: str, display_name: str) -> str:
        """Extract content based on activity type"""
        properties = activity.get('properties', {})
        
        # Base content
        content = (properties.get('description') or 
                  properties.get('content') or 
                  properties.get('notes') or
                  f"{display_name} activity migrated from Totango")
        
        # Add activity-type specific context
        if activity_key == 'gong_call':
            # Add call-specific details
            participants = properties.get('participants_count', 0)
            recording_url = properties.get('recording_url', '')
            duration = properties.get('duration_minutes', '')
            if participants:
                content += f"\n\nCall Details:\n- Participants: {participants}"
            if duration:
                content += f"\n- Duration: {duration} minutes"
            if recording_url:
                content += f"\n- Recording: {recording_url}"
                
        elif activity_key == 'web_meeting' or activity_key == 'in_person_meeting':
            # Add meeting-specific details
            attendees = properties.get('attendees_count', 0)
            location = properties.get('location', '')
            platform = properties.get('platform', '')
            if attendees:
                content += f"\n\nMeeting Details:\n- Attendees: {attendees}"
            if location:
                content += f"\n- Location: {location}"
            if platform:
                content += f"\n- Platform: {platform}"
                
        elif activity_key == 'slack':
            # Add Slack-specific details
            channel = properties.get('channel', '')
            thread_id = properties.get('thread_id', '')
            message_type = properties.get('message_type', '')
            if channel:
                content += f"\n\nSlack Details:\n- Channel: {channel}"
            if thread_id:
                content += f"\n- Thread: {thread_id}"
            if message_type:
                content += f"\n- Type: {message_type}"
                
        elif activity_key == 'inbound':
            # Add inbound-specific details
            source = properties.get('source', '')
            referrer = properties.get('referrer', '')
            if source:
                content += f"\n\nInbound Details:\n- Source: {source}"
            if referrer:
                content += f"\n- Referrer: {referrer}"
                
        elif activity_key == 'feedback':
            # Add feedback-specific details
            rating = properties.get('rating', '')
            category = properties.get('category', '')
            if rating:
                content += f"\n\nFeedback Details:\n- Rating: {rating}"
            if category:
                content += f"\n- Category: {category}"
        
        # Add campaign details if present
        if activity.get('type') == 'campaign_touch':
            targeted_users = properties.get('targeted_users_count', 0)
            campaign_type = properties.get('campaign_schedule_type', 'Unknown')
            if targeted_users:
                content += f"\n\nCampaign Details:\n- Recipients: {targeted_users} users\n- Type: {campaign_type}"
        
        # Add original activity type for reference
        content += f"\n\nOriginal Totango Type: {activity.get('type', 'Unknown')}"
        
        return content
    
    def extract_author_by_type(self, activity: Dict[str, Any], activity_key: str) -> str:
        """Extract author based on activity type"""
        properties = activity.get('properties', {})
        
        author_email = properties.get('from_user', '')
        
        if not author_email:
            # Activity-type specific fallbacks
            if activity_key == 'gong_call':
                author_email = "<EMAIL>"
            elif activity_key == 'slack':
                author_email = "<EMAIL>"
            elif activity_key == 'internal_note':
                author_email = properties.get('created_by', '<EMAIL>')
            elif activity_key == 'inbound':
                author_email = "<EMAIL>"
            elif activity_key == 'feedback':
                author_email = "<EMAIL>"
            else:
                # For campaign touches, use system email
                if activity.get('type') == 'campaign_touch':
                    author_email = "<EMAIL>"
                else:
                    author_email = "<EMAIL>"
        
        return author_email
    
    def extract_attendees(self, activity: Dict[str, Any]) -> Tuple[List[str], List[str]]:
        """Extract internal and external attendees"""
        internal_attendees = []
        external_attendees = []
        
        enriched_users = activity.get('enrichedUsers', [])
        for user in enriched_users:
            email = user.get('email', '')
            name = user.get('name', '')
            if email:
                formatted_attendee = f"{name} <{email}>" if name else email
                if email.endswith('@mend.io') or email.endswith('@whitesourcesoftware.com'):
                    internal_attendees.append(formatted_attendee)
                else:
                    external_attendees.append(formatted_attendee)
                    
        return internal_attendees, external_attendees
    
    def map_touchpoint_reason_correct(self, activity: Dict[str, Any], touchpoint_reasons: Dict) -> str:
        """Map Totango activity to Gainsight touchpoint reason"""
        properties = activity.get('properties', {})
        activity_type = activity.get('type', '')
        
        # Get meeting_type (where activity ID is stored) from properties 
        meeting_type = properties.get('meeting_type', '')
        
        # First, check if meeting_type maps to a touchpoint reason
        if meeting_type and meeting_type in touchpoint_reasons:
            reason_data = touchpoint_reasons[meeting_type]
            return reason_data.get('display_name', 'COMMUNICATION')
        
        # Fallback: Check activity_type_id for touchpoint mapping
        activity_type_id = properties.get('activity_type_id', '')
        if activity_type_id and activity_type_id in touchpoint_reasons:
            reason_data = touchpoint_reasons[activity_type_id]
            return reason_data.get('display_name', 'COMMUNICATION')
        
        # Activity type specific defaults
        if activity_type in ['campaign_touch', 'email']:
            return 'COMMUNICATION'
        elif activity_type in ['call', 'phone']:
            return 'OUTREACH'
        elif activity_type in ['meeting', 'demo']:
            return 'MEETING'
        else:
            return 'COMMUNICATION'
    
    def transform_to_gainsight_format(self, activity: Dict[str, Any], 
                                    id_mappings: Dict, touchpoint_reasons: Dict, 
                                    activity_key: str, activity_info: Dict) -> Dict[str, Any]:
        """Transform Totango activity to proper Gainsight API format with correct type mapping"""
        properties = activity.get('properties', {})
        
        # Get exact Gainsight type from our configuration
        gainsight_type = activity_info['gainsight_type']
        totango_display_name = activity_info['display_name']
        is_custom_type = activity_info['category'] == 'custom'
        
        # Log type mapping for verification
        logger.debug(f"🔄 Mapping: {totango_display_name} → {gainsight_type} ({'Custom' if is_custom_type else 'Default'})")
        
        # Convert timestamp to ISO format
        timestamp_ms = activity.get('timestamp', 0)
        if timestamp_ms:
            activity_date = datetime.fromtimestamp(timestamp_ms / 1000).isoformat()
        else:
            activity_date = datetime.now().isoformat()
        
        # Extract subject with activity-type specific logic
        subject = self.extract_subject_by_type(activity, activity_key, totango_display_name)
        
        # Extract content/description with activity-type specific logic  
        content = self.extract_content_by_type(activity, activity_key, totango_display_name)
        
        # Determine touchpoint reason
        touchpoint_reason = self.map_touchpoint_reason_correct(activity, touchpoint_reasons)
        
        # Extract author information with activity-type specific logic
        author_email = self.extract_author_by_type(activity, activity_key)
        
        # Extract attendees information
        internal_attendees, external_attendees = self.extract_attendees(activity)
        
        # Build Gainsight Timeline API compatible payload
        gainsight_activity = {
            "records": [
                {
                    "ContextName": "Company",
                    "TypeName": gainsight_type,  # Exact Gainsight type name (default or custom)
                    "ExternalId": activity['company']['id'] if 'company' in activity else "0015p00005R7ysqAAB",
                    "Subject": subject[:255],  # Gainsight field limit
                    "Notes": content,
                    "ActivityDate": activity_date,
                    "Author": author_email,
                    # Standard fields
                    "companyExternalId": activity['company']['id'] if 'company' in activity else "0015p00005R7ysqAAB",
                    "internalAttendees": internal_attendees,
                    "externalAttendees": external_attendees,
                    # Enhanced custom fields for tracking
                    "customField_TouchpointReason": touchpoint_reason,
                    "customField_OriginalSource": "Totango Migration",
                    "customField_TotangoActivityType": totango_display_name,
                    "customField_GainsightTypeCategory": "Custom" if is_custom_type else "Default",
                    "customField_OriginalType": activity.get('type', ''),
                    "customField_OriginalId": activity.get('id', ''),
                    "customField_ActivityKey": activity_key,
                    "customField_MigrationTimestamp": datetime.now().isoformat(),
                    "customField_MigrationBatch": f"WildWeasel_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
                }
            ],
            "lookups": {
                "AuthorId": {
                    "fields": {
                        "Author": "Email"
                    },
                    "lookupField": "Gsid",
                    "objectName": "GsUser",
                    "multiMatchOption": "FIRSTMATCH",
                    "onNoMatch": "ERROR"
                },
                "GsCompanyId": {
                    "fields": {
                        "companyExternalId": "ExternalId"
                    },
                    "lookupField": "Gsid",
                    "objectName": "Company",
                    "multiMatchOption": "FIRSTMATCH",
                    "onNoMatch": "ERROR"
                }
            },
            # Enhanced metadata for tracking
            "metadata": {
                "totango_original": activity,
                "migration_source": "Wild Weasel Multi-Type v2.1",
                "activity_type_info": activity_info,
                "gainsight_type_mapping": {
                    "totango_name": totango_display_name,
                    "gainsight_type": gainsight_type,
                    "is_custom_type": is_custom_type
                },
                "processing_timestamp": datetime.now().isoformat()
            }
        }
        
        return gainsight_activity
    
    def extract_activities(self) -> List[Dict[str, Any]]:
        """Main extraction method for multiple activity types"""
        logger.info("🐺 Wild Weasel Multi-Activity Extraction Starting...")
        
        # Load main data
        if not os.path.exists(self.icici_file):
            logger.error(f"❌ ICICI data file not found: {self.icici_file}")
            return []
            
        with open(self.icici_file, 'r') as f:
            all_activities = json.load(f)
            
        logger.info(f"📊 Loaded {len(all_activities)} total Totango activities")
        
        # Load supporting data
        id_mappings, touchpoint_reasons = self.load_supporting_data()
        
        if not id_mappings:
            logger.warning("⚠️ No ID mappings loaded - activity identification may be limited")
        
        # Filter activities
        target_activities = []
        activity_type_breakdown = {}
        found_types_breakdown = {}
        
        for activity in all_activities:
            activity_type = activity.get('type', 'unknown')
            activity_type_breakdown[activity_type] = activity_type_breakdown.get(activity_type, 0) + 1
            
            is_target, activity_key, activity_info = self.is_target_activity(activity, id_mappings)
            if is_target:
                target_activities.append((activity, activity_key, activity_info))
                display_name = activity_info['display_name']
                found_types_breakdown[display_name] = found_types_breakdown.get(display_name, 0) + 1
                
        logger.info(f"🎯 Original Totango Activity Type Breakdown:")
        for atype, count in sorted(activity_type_breakdown.items()):
            logger.info(f"   {atype}: {count}")
            
        logger.info(f"✅ Found Target Activity Types:")
        for found_type, count in sorted(found_types_breakdown.items()):
            logger.info(f"   {found_type}: {count}")
                    
        logger.info(f"✅ Found {len(target_activities)} total target activities")
        
        # Transform to Gainsight format
        gainsight_activities = []
        for activity, activity_key, activity_info in target_activities:
            try:
                transformed = self.transform_to_gainsight_format(
                    activity, id_mappings, touchpoint_reasons, activity_key, activity_info
                )
                gainsight_activities.append(transformed)
            except Exception as e:
                logger.error(f"❌ Error transforming {activity_info['display_name']} activity {activity.get('id', 'unknown')}: {e}")
                
        logger.info(f"🚀 Successfully transformed {len(gainsight_activities)} activities for Gainsight API")
        
        return gainsight_activities
    
    def save_extracted_data(self, activities: List[Dict[str, Any]], output_file: str):
        """Save extracted activities to JSON file"""
        with open(output_file, 'w') as f:
            json.dump(activities, f, indent=2, default=str)
        logger.info(f"💾 Saved {len(activities)} activities to {output_file}")
    
    def get_type_mapping_summary(self) -> Dict[str, Any]:
        """Get summary of type mappings for validation"""
        mapping_summary = {
            "default_types": {},
            "custom_types": {}
        }
        
        for activity_key in self.target_activity_types:
            if activity_key in self.all_activity_types:
                activity_info = self.all_activity_types[activity_key]
                mapping = {
                    "totango_name": activity_info['display_name'],
                    "gainsight_type": activity_info['gainsight_type'],
                    "expected_count": activity_info['used']
                }
                
                if activity_info['category'] == 'default':
                    mapping_summary["default_types"][activity_key] = mapping
                else:
                    mapping_summary["custom_types"][activity_key] = mapping
        
        return mapping_summary
    
    def generate_detailed_report(self, activities: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Generate comprehensive analysis report with type categorization"""
        if not activities:
            return {"error": "No activities to analyze"}
            
        # Analyze activities by various dimensions
        touchpoint_breakdown = {}
        date_range = {"earliest": None, "latest": None}
        author_breakdown = {}
        gainsight_type_breakdown = {}
        totango_type_breakdown = {}
        custom_vs_default = {"default": 0, "custom": 0}
        
        for activity_wrapper in activities:
            # Extract the actual activity record
            if 'records' in activity_wrapper and activity_wrapper['records']:
                activity = activity_wrapper['records'][0]
            else:
                continue
                
            # Touchpoint reasons
            reason = activity.get('customField_TouchpointReason', 'Unknown')
            touchpoint_breakdown[reason] = touchpoint_breakdown.get(reason, 0) + 1
            
            # Authors
            author = activity.get('Author', 'Unknown')
            author_breakdown[author] = author_breakdown.get(author, 0) + 1
            
            # Gainsight activity types
            gainsight_type = activity.get('TypeName', 'Unknown')
            gainsight_type_breakdown[gainsight_type] = gainsight_type_breakdown.get(gainsight_type, 0) + 1
            
            # Original Totango types
            totango_type = activity.get('customField_TotangoActivityType', 'Unknown')
            totango_type_breakdown[totango_type] = totango_type_breakdown.get(totango_type, 0) + 1
            
            # Custom vs Default type tracking
            type_category = activity.get('customField_GainsightTypeCategory', 'Unknown')
            if type_category in custom_vs_default:
                custom_vs_default[type_category] += 1
            
            # Date range
            activity_date = activity.get('ActivityDate')
            if activity_date:
                if not date_range["earliest"] or activity_date < date_range["earliest"]:
                    date_range["earliest"] = activity_date
                if not date_range["latest"] or activity_date > date_range["latest"]:
                    date_range["latest"] = activity_date
        
        return {
            "total_activities": len(activities),
            "date_range": date_range,
            "touchpoint_breakdown": touchpoint_breakdown,
            "author_breakdown": author_breakdown,
            "gainsight_type_breakdown": gainsight_type_breakdown,
            "totango_type_breakdown": totango_type_breakdown,
            "custom_vs_default": custom_vs_default,
            "company_id": activities[0]['records'][0].get('ExternalId') if activities and 'records' in activities[0] and activities[0]['records'] else None,
            "ready_for_migration": len(activities) > 0,
            "api_ready_format": True,
            "type_mapping_summary": self.get_type_mapping_summary()
        }

def main():
    """Main execution function with comprehensive reporting"""
    extractor = TotangoActivityExtractor()
    
    # Extract all configured activity types
    activities = extractor.extract_activities()
    
    if not activities:
        logger.error("❌ No target activities found - check data sources and configuration")
        sys.exit(1)
    
    # Save extracted data
    output_file = "extracted_activities_gainsight_ready.json"
    extractor.save_extracted_data(activities, output_file)
    
    # Generate detailed report
    summary = extractor.generate_detailed_report(activities)
    
    # Display comprehensive summary
    print("\n" + "="*90)
    print("🐺 WILD WEASEL - GAINSIGHT-READY ACTIVITY EXTRACTION SUMMARY")
    print("="*90)
    print(f"✅ Total Activities Extracted: {summary['total_activities']:,}")
    print(f"📅 Date Range: {summary['date_range']['earliest']} to {summary['date_range']['latest']}")
    print(f"🏢 Company ID: {summary['company_id']}")
    print(f"📁 Output File: {output_file}")
    
    print(f"\n🎯 GAINSIGHT TYPE BREAKDOWN:")
    for gainsight_type, count in sorted(summary['gainsight_type_breakdown'].items()):
        # Determine if it's custom or default
        is_custom = any(
            info['gainsight_type'] == gainsight_type and info['category'] == 'custom'
            for info in extractor.all_activity_types.values()
        )
        type_label = "🔧 Custom" if is_custom else "📋 Default"
        print(f"   {type_label:12} {gainsight_type:20} : {count:,}")
    
    print(f"\n📊 TYPE CATEGORY SUMMARY:")
    print(f"   📋 Default Gainsight Types : {summary['custom_vs_default']['default']:,}")
    print(f"   🔧 Custom Gainsight Types  : {summary['custom_vs_default']['custom']:,}")
    
    print(f"\n🔄 TOTANGO → GAINSIGHT MAPPING:")
    type_mapping = summary['type_mapping_summary']
    
    print("   📋 Default Types:")
    for key, mapping in type_mapping['default_types'].items():
        print(f"      {mapping['totango_name']:20} → {mapping['gainsight_type']:20} ({mapping['expected_count']:,})")
    
    print("   🔧 Custom Types:")
    for key, mapping in type_mapping['custom_types'].items():
        print(f"      {mapping['totango_name']:20} → {mapping['gainsight_type']:20} ({mapping['expected_count']:,})")
    
    print(f"\n👤 Author Breakdown (Top 10):")
    top_authors = sorted(summary['author_breakdown'].items(), key=lambda x: x[1], reverse=True)[:10]
    for author, count in top_authors:
        print(f"   {author:40} : {count:,}")
    
    print(f"\n📋 Touchpoint Breakdown:")
    for reason, count in sorted(summary['touchpoint_breakdown'].items()):
        print(f"   {reason:25} : {count:,}")
    
    print(f"\n🚀 Migration Status: {'✅ Ready for Gainsight API' if summary['ready_for_migration'] else '❌ Not Ready'}")
    print("="*90)
    
    # Save comprehensive summary report
    summary_file = "gainsight_migration_summary.json"
    with open(summary_file, "w") as f:
        json.dump(summary, f, indent=2, default=str)
    
    print(f"\n📋 Detailed summary saved to: {summary_file}")
    logger.info("🐺 Wild Weasel Gainsight-Ready Activity Extraction Complete!")

if __name__ == "__main__":
    main()

#!/usr/bin/env python3
"""
🐺 Wild Weasel v5.0 - Data Transformation Validator
=============================================================================
Test script to validate Totango → Gainsight data transformation before migration
"""

import json
import sys
import os
from datetime import datetime, timezone
from typing import Dict, List, Any, Optional

# Add the current directory to path to import our transformer
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def load_sample_data(file_path: str, sample_size: int = 5) -> List[Dict[str, Any]]:
    """Load a sample of Totango data for testing"""
    try:
        with open(file_path, 'r') as f:
            data = json.load(f)
        
        # Return a sample of different event types
        sample_data = []
        event_types_seen = set()
        
        for item in data:
            event_type = item.get('type', 'unknown')
            if len(sample_data) < sample_size and event_type not in event_types_seen:
                sample_data.append(item)
                event_types_seen.add(event_type)
            elif len(sample_data) >= sample_size:
                break
        
        # If we don't have enough variety, just take the first few
        if len(sample_data) < sample_size:
            sample_data = data[:sample_size]
        
        return sample_data
        
    except Exception as e:
        print(f"❌ Error loading sample data: {e}")
        return []

def validate_gainsight_payload(payload: Dict[str, Any]) -> List[str]:
    """Validate that the Gainsight payload has all required fields"""
    errors = []
    
    # Check top-level structure
    required_top_level = ['lastModifiedByUser', 'note', 'meta', 'author', 'contexts']
    for field in required_top_level:
        if field not in payload:
            errors.append(f"Missing top-level field: {field}")
    
    # Check note structure
    if 'note' in payload:
        note = payload['note']
        required_note_fields = ['type', 'subject', 'activityDate', 'content', 'plainText']
        for field in required_note_fields:
            if field not in note:
                errors.append(f"Missing note field: {field}")
        
        # Validate customFields
        if 'customFields' not in note:
            errors.append("Missing note.customFields")
        else:
            custom_fields = note['customFields']
            if 'Ant__Source_System__c' not in custom_fields:
                errors.append("Missing source system tracking field")
            if 'Ant__Source_ID__c' not in custom_fields:
                errors.append("Missing source ID tracking field")
    
    # Check contexts (company mapping)
    if 'contexts' in payload:
        contexts = payload['contexts']
        if not contexts or len(contexts) == 0:
            errors.append("Missing company context")
        else:
            context = contexts[0]
            required_context_fields = ['id', 'obj', 'lbl']
            for field in required_context_fields:
                if field not in context:
                    errors.append(f"Missing context field: {field}")
    
    # Check meta
    if 'meta' in payload:
        meta = payload['meta']
        required_meta_fields = ['activityTypeId', 'source', 'systemType']
        for field in required_meta_fields:
            if field not in meta:
                errors.append(f"Missing meta field: {field}")
    
    return errors

def test_data_transformation():
    """Test the data transformation pipeline"""
    print("🐺" + "="*70)
    print("  WILD WEASEL v5.0 - DATA TRANSFORMATION VALIDATOR")
    print("="*72)
    
    # Import our transformer (this will test if the imports work)
    try:
        from wild_weasel_agent_v5_api_enhanced import TotangoDataTransformer
        print("✅ Successfully imported TotangoDataTransformer")
    except Exception as e:
        print(f"❌ Failed to import transformer: {e}")
        return False
    
    # Load sample data
    data_file = "/Users/<USER>/Desktop/totango/ICICI_processed.json"
    if not os.path.exists(data_file):
        print(f"❌ Totango data file not found: {data_file}")
        return False
    
    print(f"📁 Loading sample data from: {data_file}")
    sample_data = load_sample_data(data_file, sample_size=5)
    
    if not sample_data:
        print("❌ No sample data loaded")
        return False
    
    print(f"📊 Loaded {len(sample_data)} sample events for testing")
    
    # Initialize transformer
    transformer = TotangoDataTransformer()
    print("🔄 Transformer initialized")
    
    # Test transformation
    print("\n🧪 Testing data transformation...")
    all_tests_passed = True
    
    for i, event in enumerate(sample_data):
        event_id = event.get('id', f'unknown_{i}')
        event_type = event.get('type', 'unknown')
        
        print(f"\n--- Test {i+1}: {event_type} (ID: {event_id[:20]}...) ---")
        
        try:
            # Transform the event
            gainsight_payload = transformer.transform_to_gainsight(event)
            
            if gainsight_payload is None:
                print(f"⚠️  Transformation returned None (may be expected for some events)")
                continue
            
            # Validate the payload
            validation_errors = validate_gainsight_payload(gainsight_payload)
            
            if validation_errors:
                print("❌ Validation failed:")
                for error in validation_errors:
                    print(f"  • {error}")
                all_tests_passed = False
            else:
                print("✅ Transformation and validation passed")
                
                # Show some key details
                note = gainsight_payload.get('note', {})
                print(f"  📝 Subject: {note.get('subject', 'N/A')[:50]}...")
                print(f"  📅 Activity Date: {note.get('activityDate', 'N/A')}")
                print(f"  🏢 Company: {gainsight_payload.get('contexts', [{}])[0].get('lbl', 'N/A')}")
                print(f"  🔗 Source ID: {note.get('customFields', {}).get('Ant__Source_ID__c', 'N/A')}")
        
        except Exception as e:
            print(f"❌ Transformation failed with exception: {e}")
            all_tests_passed = False
    
    # Summary
    print("\n" + "="*72)
    if all_tests_passed:
        print("🎉 ALL TRANSFORMATION TESTS PASSED!")
        print("✅ Your data transformation pipeline is ready for migration")
    else:
        print("⚠️  SOME TESTS FAILED")
        print("❌ Please review the errors above before running migration")
    
    print("="*72)
    return all_tests_passed

def show_sample_payload():
    """Show a sample transformed payload for inspection"""
    print("\n🔍 SAMPLE TRANSFORMED PAYLOAD:")
    print("="*50)
    
    # Load one sample event
    data_file = "/Users/<USER>/Desktop/totango/ICICI_processed.json"
    sample_data = load_sample_data(data_file, sample_size=1)
    
    if sample_data:
        from wild_weasel_agent_v5_api_enhanced import TotangoDataTransformer
        transformer = TotangoDataTransformer()
        
        payload = transformer.transform_to_gainsight(sample_data[0])
        if payload:
            print(json.dumps(payload, indent=2)[:2000] + "...")
        else:
            print("No payload generated for sample event")

def main():
    """Main validation function"""
    try:
        # Run transformation tests
        success = test_data_transformation()
        
        # Optionally show sample payload
        if success:
            show_sample_payload()
        
        if success:
            print("\n🚀 Ready to run Wild Weasel v5.0 API Enhanced migration!")
            print("Execute: python3 wild_weasel_agent_v5_api_enhanced.py")
        else:
            print("\n🔧 Please fix the issues above before running migration")
            
    except KeyboardInterrupt:
        print("\n🐺 Validation interrupted by user")
        sys.exit(0)
    except Exception as e:
        print(f"\n❌ Validation failed: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()

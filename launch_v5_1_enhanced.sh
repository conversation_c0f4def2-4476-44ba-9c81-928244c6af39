#!/bin/bash

# 🐺 Wild Weasel v5.1 Enhanced Meeting Type Mapping Launcher
# Smart launcher script with enhanced meeting type mapping support

echo "🐺 Wild Weasel v5.1 Enhanced Meeting Type Mapping Launcher"
echo "========================================================"
echo ""

# Function to check if file exists
check_file() {
    if [ ! -f "$1" ]; then
        echo "❌ Error: $1 not found"
        return 1
    fi
    return 0
}

# Function to check Python dependencies
check_dependencies() {
    echo "🔍 Checking dependencies..."
    
    # Check if Python 3 is available
    if ! command -v python3 &> /dev/null; then
        echo "❌ Python 3 is not installed"
        return 1
    fi
    
    # Check key packages
    python3 -c "import playwright, aiofiles, aiohttp" 2>/dev/null
    if [ $? -ne 0 ]; then
        echo "⚠️  Some dependencies missing. Run setup first."
        return 1
    fi
    
    echo "✅ Dependencies check passed"
    return 0
}

# Function to check data file
check_data() {
    echo "📊 Checking Totango data file..."
    
    data_file="/Users/<USER>/Desktop/totango/ICICI_processed.json"
    if [ ! -f "$data_file" ]; then
        echo "❌ Totango data file not found: $data_file"
        echo "   Please ensure your data is in the correct location"
        return 1
    fi
    
    # Check file size
    file_size=$(wc -c < "$data_file")
    if [ $file_size -lt 100 ]; then
        echo "❌ Data file appears to be empty or too small"
        return 1
    fi
    
    # Count entries (approximate)
    entry_count=$(grep -o '"id":' "$data_file" | wc -l)
    echo "✅ Data file found with approximately $entry_count events"
    
    # Check for meeting types in data
    meeting_type_count=$(grep -o '"meeting_type"' "$data_file" | wc -l)
    meeting_type_name_count=$(grep -o '"meeting_type_name"' "$data_file" | wc -l)
    total_meeting_types=$((meeting_type_count + meeting_type_name_count))
    
    if [ $total_meeting_types -gt 0 ]; then
        echo "✅ Found $total_meeting_types events with meeting type information"
    else
        echo "ℹ️  No meeting type fields found in data (will use event type mapping)"
    fi
    
    return 0
}

# Function to check credentials
check_credentials() {
    echo "🔐 Checking credentials..."
    
    if [ -f ".env" ]; then
        if grep -q "your_username_here" .env || grep -q "your_password_here" .env; then
            echo "⚠️  Please update your credentials in .env file"
            return 1
        fi
        echo "✅ Credentials file found"
        return 0
    else
        echo "⚠️  No .env file found. You'll be prompted for credentials."
        return 0
    fi
}

# Main menu
show_menu() {
    echo ""
    echo "🚀 Wild Weasel v5.1 - What would you like to do?"
    echo "1. 🔧 Setup environment (install dependencies)"
    echo "2. 🧪 Validate meeting type mapping" 
    echo "3. 🧪 Test transformation (legacy v5.0)"
    echo "4. 🐺 Run v5.1 enhanced migration (with meeting types)"
    echo "5. 🐺 Run v5.0 migration (basic API)"
    echo "6. 📊 View previous results"
    echo "7. 🆘 Help & troubleshooting"
    echo "8. 🚪 Exit"
    echo ""
    echo -n "Choose an option (1-8): "
}

# Setup function
run_setup() {
    echo ""
    echo "🔧 Running setup..."
    
    if check_file "setup_v5_enhanced.sh"; then
        chmod +x setup_v5_enhanced.sh
        ./setup_v5_enhanced.sh
    else
        echo "❌ Setup script not found"
        return 1
    fi
}

# Enhanced validation function
run_meeting_validation() {
    echo ""
    echo "🧪 Running enhanced meeting type mapping validation..."
    
    if check_file "validate_meeting_mapping_v5_1.py"; then
        python3 validate_meeting_mapping_v5_1.py
    else
        echo "❌ Enhanced validation script not found"
        return 1
    fi
}

# Legacy validation function
run_legacy_validation() {
    echo ""
    echo "🧪 Running legacy data transformation validation..."
    
    if check_file "validate_transformation_v5.py"; then
        python3 validate_transformation_v5.py
    else
        echo "❌ Legacy validation script not found"
        return 1
    fi
}

# Enhanced migration function
run_enhanced_migration() {
    echo ""
    echo "🐺 Starting Wild Weasel v5.1 Enhanced Migration (with Meeting Type Mapping)..."
    echo ""
    
    # Pre-flight checks
    echo "🚀 Pre-flight checks..."
    
    if ! check_dependencies; then
        echo "💡 Try running option 1 (Setup environment) first"
        return 1
    fi
    
    if ! check_data; then
        echo "💡 Please ensure your Totango data is properly prepared"
        return 1
    fi
    
    check_credentials
    
    echo ""
    echo "🎯 ENHANCED FEATURES in v5.1:"
    echo "  ✅ Intelligent meeting type mapping"
    echo "  ✅ Support for custom Gainsight activity types"
    echo "  ✅ Enhanced API bulk processing"
    echo "  ✅ Detailed activity type statistics"
    echo ""
    echo "⚠️  IMPORTANT: This will migrate ALL events in your Totango data file."
    echo "   Enhanced mapping will automatically detect and transform meeting types."
    echo ""
    echo -n "Are you sure you want to proceed with v5.1 enhanced migration? (y/N): "
    read confirm
    
    if [[ $confirm == [yY] || $confirm == [yY][eE][sS] ]]; then
        echo ""
        echo "🚀 Launching v5.1 enhanced migration..."
        
        if check_file "wild_weasel_agent_v5_1_meeting_enhanced.py"; then
            python3 wild_weasel_agent_v5_1_meeting_enhanced.py
        else
            echo "❌ Enhanced migration script not found"
            return 1
        fi
    else
        echo "Migration cancelled."
    fi
}

# Basic migration function
run_basic_migration() {
    echo ""
    echo "🐺 Starting Wild Weasel v5.0 Basic API Migration..."
    echo ""
    
    # Pre-flight checks
    echo "🚀 Pre-flight checks..."
    
    if ! check_dependencies; then
        echo "💡 Try running option 1 (Setup environment) first"
        return 1
    fi
    
    if ! check_data; then
        echo "💡 Please ensure your Totango data is properly prepared"
        return 1
    fi
    
    check_credentials
    
    echo ""
    echo "ℹ️  NOTE: Using basic v5.0 without enhanced meeting type mapping."
    echo "   For better results, consider using option 4 (v5.1 enhanced)."
    echo ""
    echo -n "Are you sure you want to proceed with basic v5.0 migration? (y/N): "
    read confirm
    
    if [[ $confirm == [yY] || $confirm == [yY][eE][sS] ]]; then
        echo ""
        echo "🚀 Launching v5.0 basic migration..."
        
        if check_file "wild_weasel_agent_v5_api_enhanced.py"; then
            python3 wild_weasel_agent_v5_api_enhanced.py
        else
            echo "❌ Basic migration script not found"
            return 1
        fi
    else
        echo "Migration cancelled."
    fi
}

# View results function
view_results() {
    echo ""
    echo "📊 Previous migration results:"
    echo ""
    
    # Find result files
    result_files=$(ls -t migration_results_v5*.json 2>/dev/null | head -5)
    
    if [ -z "$result_files" ]; then
        echo "No previous results found."
        return
    fi
    
    echo "Recent result files:"
    for file in $result_files; do
        file_date=$(echo $file | grep -o '[0-9]\{8\}_[0-9]\{6\}')
        if [ ! -z "$file_date" ]; then
            formatted_date=$(echo $file_date | sed 's/_/ /')
            echo "  📄 $file (from $formatted_date)"
        else
            echo "  📄 $file"
        fi
    done
    
    echo ""
    echo "📈 Latest statistics:"
    latest_stats=$(ls -t migration_stats_v5*.json 2>/dev/null | head -1)
    if [ ! -z "$latest_stats" ]; then
        echo "From: $latest_stats"
        python3 -c "
import json
try:
    with open('$latest_stats', 'r') as f:
        stats = json.load(f)
    print(f\"  Total: {stats.get('total_items', 'N/A')} events\")
    print(f\"  Successful: {stats.get('successful', 'N/A')}\")
    print(f\"  Failed: {stats.get('failed', 'N/A')}\")
    print(f\"  Success Rate: {stats.get('success_rate', 'N/A'):.1f}%\" if isinstance(stats.get('success_rate'), (int, float)) else f\"  Success Rate: {stats.get('success_rate', 'N/A')}\")
    print(f\"  Duration: {stats.get('duration', 'N/A')}\")
    
    # Show activity type breakdown if available
    activity_stats = stats.get('activity_type_stats', {})
    if activity_stats:
        print(\"  Activity Types:\")
        for activity_type, count in sorted(activity_stats.items(), key=lambda x: x[1], reverse=True):
            print(f\"    • {activity_type}: {count}\")
    
except Exception as e:
    print(f\"  Error reading stats: {e}\")
"
    else
        echo "No statistics files found."
    fi
}

# Help function
show_help() {
    echo ""
    echo "🆘 Wild Weasel v5.1 Help & Troubleshooting"
    echo "==========================================="
    echo ""
    echo "📋 Prerequisites:"
    echo "  • Python 3.8+ installed"
    echo "  • Gainsight account credentials"
    echo "  • Totango data file at: /Users/<USER>/Desktop/totango/ICICI_processed.json"
    echo ""
    echo "🎯 What's New in v5.1:"
    echo "  • Enhanced meeting type mapping (meeting_type → Gainsight activity type)"
    echo "  • Support for custom Gainsight activity types (In-Person Meeting, Gong Call, etc.)"
    echo "  • Improved activity type statistics and reporting"
    echo "  • Better fallback mechanisms for unknown meeting types"
    echo ""
    echo "🔄 Meeting Type Mappings:"
    echo "  • Email → Email"
    echo "  • Telephone Call → Call" 
    echo "  • Web Meeting → Meeting"
    echo "  • Internal Note → Update"
    echo "  • In-Person Meeting → In-Person Meeting (custom)"
    echo "  • Gong Call → Gong Call (custom)"
    echo "  • Slack → Slack (custom)"
    echo "  • Feedback → Feedback (custom)"
    echo "  • Inbound → Inbound (custom)"
    echo ""
    echo "🔧 Common Issues:"
    echo "  • 'Module not found' errors → Run option 1 (Setup environment)"
    echo "  • 'Authentication failed' → Check credentials in .env file"
    echo "  • 'Data file not found' → Ensure Totango data is in correct location"
    echo "  • Browser errors → Run 'playwright install chromium'"
    echo "  • 'Meeting type not mapped' → Check validate_meeting_mapping_v5_1.py output"
    echo ""
    echo "📊 What gets migrated:"
    echo "  • All Totango events transformed to Gainsight timeline activities"
    echo "  • Meeting types intelligently mapped to correct activity types"
    echo "  • Original meeting type preserved in custom field"
    echo "  • Company mapping: ICICI Bank (0015p00005R7ysqAAB → 1P02IPMAEL4M3CQGY4K5FHU22D2HHT11KCKU)"
    echo ""
    echo "🚀 Recommended process:"
    echo "  1. Setup environment (option 1)"
    echo "  2. Validate meeting mapping (option 2)" 
    echo "  3. Run v5.1 enhanced migration (option 4)"
    echo ""
    echo "📞 Need more help?"
    echo "  • Check logs in wild_weasel_v5_api.log"
    echo "  • Review migration results and statistics files"
    echo "  • Run validation scripts to test transformations"
}

# Main script
main() {
    # Check if we're in the right directory
    if [ ! -f "wild_weasel_agent_v5_1_meeting_enhanced.py" ]; then
        echo "❌ Error: Please run this script from the wild_weasel_gainsight_migration directory"
        echo "   Current directory: $(pwd)"
        echo "   Looking for: wild_weasel_agent_v5_1_meeting_enhanced.py"
        exit 1
    fi
    
    while true; do
        show_menu
        read choice
        
        case $choice in
            1)
                run_setup
                ;;
            2)
                run_meeting_validation
                ;;
            3)
                run_legacy_validation
                ;;
            4)
                run_enhanced_migration
                ;;
            5)
                run_basic_migration
                ;;
            6)
                view_results
                ;;
            7)
                show_help
                ;;
            8)
                echo ""
                echo "🐺 Thanks for using Wild Weasel v5.1!"
                echo "Happy migrating with enhanced meeting type mapping! 🚀"
                exit 0
                ;;
            *)
                echo "❌ Invalid option. Please choose 1-8."
                ;;
        esac
        
        echo ""
        echo -n "Press Enter to continue..."
        read
    done
}

# Run main function
main

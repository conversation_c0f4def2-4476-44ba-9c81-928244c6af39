[{"row_number": 1, "subject": "ICICI: Mend Platform Access", "activity_type": "Update", "error": "UI exception: Page.screenshot: Target page, context or browser has been closed", "timestamp": "2025-05-29T20:48:52.646872"}, {"row_number": 2, "subject": "ICICI: Mend Platform Access", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T20:48:54.661553"}, {"row_number": 3, "subject": "ICICI: Mend Platform Access", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T20:48:56.678751"}, {"row_number": 4, "subject": "ICICI: Mend Platform Access", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T20:48:58.699495"}, {"row_number": 5, "subject": "ICICI: Mend Platform Access", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T20:49:00.713177"}, {"row_number": 6, "subject": "ICICI: Mend Platform Access", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T20:49:02.726606"}, {"row_number": 7, "subject": "ICICI: <PERSON><PERSON><PERSON> to Delighted", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T20:49:04.746290"}, {"row_number": 8, "subject": "ICICI: NPS Send Date", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T20:49:06.764957"}, {"row_number": 9, "subject": "ICICI: NPS Last Sent", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T20:49:08.792206"}, {"row_number": 10, "subject": "ICICI: Mend Platform Access", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T20:49:10.816012"}, {"row_number": 11, "subject": "ICICI: May Newsletter: AI Risk Insights, Smarter Compliance and Cloud Integration!", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T20:49:12.836224"}, {"row_number": 12, "subject": "ICICI: Mend Platform Access", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T20:49:14.849628"}, {"row_number": 13, "subject": "ICICI: Mend Platform Access", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T20:49:16.870324"}, {"row_number": 14, "subject": "ICICI: MP Activity Frequency", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T20:49:18.886867"}, {"row_number": 15, "subject": "ICICI: <PERSON> <PERSON><PERSON><PERSON> (7d)", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T20:49:20.913152"}, {"row_number": 16, "subject": "ICICI: Mend Platform Access", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T20:49:22.932102"}, {"row_number": 17, "subject": "ICICI: SCA CD Variance Stages", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T20:49:24.951909"}, {"row_number": 18, "subject": "ICICI: <div><br>Upcoming NPS Survey&nbsp;</div>", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T20:49:26.975491"}, {"row_number": 19, "subject": "ICICI: Mend Platform Access", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T20:49:28.994053"}, {"row_number": 20, "subject": "ICICI: MP Activity", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T20:49:31.013843"}, {"row_number": 21, "subject": "ICICI: MP Activity Frequency", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T20:49:33.024682"}, {"row_number": 22, "subject": "ICICI: MITRE", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T20:49:35.048254"}, {"row_number": 23, "subject": "ICICI: MP Abandonment Date", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T20:49:37.065548"}, {"row_number": 24, "subject": "ICICI: MP Activity", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T20:49:39.084206"}, {"row_number": 25, "subject": "ICICI: Mend Platform Access", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T20:49:41.096019"}, {"row_number": 26, "subject": "ICICI: Mend Platform Access", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T20:49:43.104668"}, {"row_number": 27, "subject": "ICICI: April Newsletter: New Dashboard, Mend AI, and More!", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T20:49:45.136632"}, {"row_number": 28, "subject": "ICICI: Mend Platform Access", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T20:49:47.143785"}, {"row_number": 29, "subject": "ICICI: Mend Platform Access", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T20:49:49.157271"}, {"row_number": 30, "subject": "ICICI: Mend Platform Access", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T20:49:51.175394"}, {"row_number": 31, "subject": "ICICI: MP Activity Frequency", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T20:49:53.191415"}, {"row_number": 32, "subject": "ICICI: MP Activity", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T20:49:55.212387"}, {"row_number": 33, "subject": "ICICI: <PERSON> Newsletter: Introducing Mend AI and More!", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T20:49:57.228406"}, {"row_number": 34, "subject": "ICICI: MP Abandonment Date", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T20:49:59.244565"}, {"row_number": 35, "subject": "ICICI: MP Activity", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T20:50:01.260975"}, {"row_number": 36, "subject": "ICICI: MP Activity Frequency", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T20:50:03.281427"}, {"row_number": 37, "subject": "ICICI: Mend Platform Access", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T20:50:05.297016"}, {"row_number": 38, "subject": "ICICI: MP Activity Frequency", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T20:50:07.315160"}, {"row_number": 39, "subject": "ICICI: <PERSON> <PERSON><PERSON><PERSON> (7d)", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T20:50:09.333547"}, {"row_number": 40, "subject": "ICICI: Mend Platform Access", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T20:50:11.350435"}, {"row_number": 41, "subject": "ICICI: Active - CN", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T20:50:13.367215"}, {"row_number": 42, "subject": "ICICI: Active - SAST", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T20:50:15.377768"}, {"row_number": 43, "subject": "ICICI: Active - SCA", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T20:50:17.395266"}, {"row_number": 44, "subject": "ICICI: Mend Platform Access", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T20:50:19.411638"}, {"row_number": 45, "subject": "ICICI: Invicti Campaign", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T20:50:21.430902"}, {"row_number": 46, "subject": "ICICI: MP Activity", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T20:50:23.446799"}, {"row_number": 47, "subject": "ICICI: Mend Platform Access", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T20:50:25.463569"}, {"row_number": 48, "subject": "ICICI: MP Abandonment Date", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T20:50:27.481689"}, {"row_number": 49, "subject": "ICICI: MP Activity", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T20:50:29.498738"}, {"row_number": 50, "subject": "ICICI: MP Activity", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T20:50:31.515639"}, {"row_number": 51, "subject": "ICICI: February Newsletter: AI-Powered Code Remediation and More!", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T20:50:33.532038"}, {"row_number": 52, "subject": "ICICI: MP Abandonment Date", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T20:50:35.547573"}, {"row_number": 53, "subject": "ICICI: MP Activity", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T20:50:37.565104"}, {"row_number": 54, "subject": "ICICI: Mend Platform Access", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T20:50:39.582988"}, {"row_number": 55, "subject": "ICICI: MP Activity", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T20:50:41.603512"}, {"row_number": 56, "subject": "ICICI: Mend Platform Access", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T20:50:43.618741"}, {"row_number": 57, "subject": "ICICI: MP Abandonment Date", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T20:50:45.635725"}, {"row_number": 58, "subject": "ICICI: MP Activity", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T20:50:47.652943"}, {"row_number": 59, "subject": "ICICI: MP Activity", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T20:50:49.669814"}, {"row_number": 60, "subject": "ICICI: MP Activity Frequency", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T20:50:51.689120"}, {"row_number": 61, "subject": "ICICI: SAST Planned Downtime_App_copy", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T20:50:53.703293"}, {"row_number": 62, "subject": "ICICI: Mend Platform Access", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T20:50:55.718238"}, {"row_number": 63, "subject": "ICICI: MP Abandonment Date", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T20:50:57.733181"}, {"row_number": 64, "subject": "ICICI: MP Activity", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T20:50:59.751362"}, {"row_number": 65, "subject": "ICICI: MP Activity", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T20:51:01.766166"}, {"row_number": 66, "subject": "ICICI: MP Abandonment Date", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T20:51:03.784153"}, {"row_number": 67, "subject": "ICICI: MP Activity", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T20:51:05.800135"}, {"row_number": 68, "subject": "ICICI: AI Design Partners", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T20:51:07.821027"}, {"row_number": 69, "subject": "ICICI: Mend Platform Access", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T20:51:09.838107"}, {"row_number": 70, "subject": "ICICI: MP Activity", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T20:51:11.856910"}, {"row_number": 71, "subject": "ICICI: <PERSON> <PERSON><PERSON><PERSON> (14d)", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T20:51:13.874483"}, {"row_number": 72, "subject": "ICICI: Mend Platform Access", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T20:51:15.886711"}, {"row_number": 73, "subject": "ICICI: MP Abandonment Date", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T20:51:17.902042"}, {"row_number": 74, "subject": "ICICI: MP Activity", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T20:51:19.918504"}, {"row_number": 75, "subject": "ICICI: MP Activity", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T20:51:21.935672"}, {"row_number": 76, "subject": "ICICI: Mend Platform Access", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T20:51:23.951003"}, {"row_number": 77, "subject": "ICICI: MP Abandonment Date", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T20:51:25.966054"}, {"row_number": 78, "subject": "ICICI: MP Activity", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T20:51:27.984468"}, {"row_number": 79, "subject": "ICICI: MP Activity Frequency", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T20:51:30.001684"}, {"row_number": 80, "subject": "ICICI: MP Activity", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T20:51:32.018046"}, {"row_number": 81, "subject": "ICICI: January Newsletter:  Keep Your Code Secure, Exciting Updates from Mend.io!", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T20:51:34.032355"}, {"row_number": 82, "subject": "ICICI: Mend Platform Access", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T20:51:36.047370"}, {"row_number": 83, "subject": "ICICI: MP Abandonment Date", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T20:51:38.062804"}, {"row_number": 84, "subject": "ICICI: MP Activity", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T20:51:40.078697"}, {"row_number": 85, "subject": "ICICI: MP Activity", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T20:51:42.087975"}, {"row_number": 86, "subject": "ICICI: Mend Platform Access", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T20:51:44.108244"}, {"row_number": 87, "subject": "ICICI: MP Activity Frequency", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T20:51:46.121896"}, {"row_number": 88, "subject": "ICICI: MP Abandonment Date", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T20:51:48.134687"}, {"row_number": 89, "subject": "ICICI: MP Activity", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T20:51:50.148771"}, {"row_number": 90, "subject": "ICICI: <PERSON><PERSON><PERSON> to Delighted", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T20:51:52.167223"}, {"row_number": 91, "subject": "ICICI: <PERSON><PERSON><PERSON> to Delighted", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T20:51:54.181752"}, {"row_number": 92, "subject": "ICICI: NPS Send Date", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T20:51:56.202160"}, {"row_number": 93, "subject": "ICICI: NPS Last Sent", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T20:51:58.220675"}, {"row_number": 94, "subject": "ICICI: NPS Send Date", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T20:52:00.236208"}, {"row_number": 95, "subject": "ICICI: NPS Last Sent", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T20:52:02.256228"}, {"row_number": 96, "subject": "ICICI: Mend Platform Access", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T20:52:04.277350"}, {"row_number": 97, "subject": "ICICI: Mend Platform Access", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T20:52:06.292670"}, {"row_number": 98, "subject": "ICICI: Risk Status", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T20:52:08.312402"}, {"row_number": 99, "subject": "ICICI: Risk Next Step(s)", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T20:52:10.330064"}, {"row_number": 100, "subject": "ICICI: Primary Risk Reason", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T20:52:12.346041"}, {"row_number": 101, "subject": "ICICI: Mend Platform Access", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T20:52:14.355080"}, {"row_number": 102, "subject": "ICICI: <div><br>Upcoming NPS Survey&nbsp;</div>", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T20:52:16.373368"}, {"row_number": 103, "subject": "ICICI: Mend Platform Access", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T20:52:18.389080"}, {"row_number": 104, "subject": "ICICI: Mend Platform Access", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T20:52:20.404526"}, {"row_number": 105, "subject": "ICICI: Risk KPI", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T20:52:22.421013"}, {"row_number": 106, "subject": "ICICI: <PERSON> <PERSON> <PERSON><PERSON><PERSON>", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T20:52:24.436775"}, {"row_number": 107, "subject": "ICICI: Mend Platform Access", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T20:52:26.452665"}, {"row_number": 108, "subject": "ICICI: December Newsletter: Sharper Risk Insights & Updates", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T20:52:28.468581"}, {"row_number": 109, "subject": "ICICI: Mend Platform Access", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T20:52:30.486944"}, {"row_number": 110, "subject": "ICICI: Solana: MSC Critical Security Event", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T20:52:32.507750"}, {"row_number": 111, "subject": "ICICI: Mend Platform Access", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T20:52:34.523987"}, {"row_number": 112, "subject": "ICICI: Mend Platform Access", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T20:52:36.542700"}, {"row_number": 113, "subject": "ICICI: Mend Platform Access", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T20:52:38.561189"}, {"row_number": 114, "subject": "ICICI: SAST Planned Downtime_copy", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T20:52:40.579485"}, {"row_number": 115, "subject": "ICICI: Mend Platform Access", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T20:52:42.594352"}, {"row_number": 116, "subject": "ICICI: Mend Platform Access", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T20:52:44.613224"}, {"row_number": 117, "subject": "ICICI: Mend Platform Access", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T20:52:46.630536"}, {"row_number": 118, "subject": "ICICI: SAST Planned Downtime", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T20:52:48.647953"}, {"row_number": 119, "subject": "ICICI: Mend Platform Access", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T20:52:50.664532"}, {"row_number": 120, "subject": "ICICI: <PERSON><PERSON><PERSON> to Delighted", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T20:52:52.680969"}, {"row_number": 121, "subject": "ICICI: NPS Send Date", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T20:52:54.696706"}, {"row_number": 122, "subject": "ICICI: NPS Last Sent", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T20:52:56.710141"}, {"row_number": 123, "subject": "ICICI: Mend Platform Access", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T20:52:58.723209"}, {"row_number": 124, "subject": "ICICI: November newsletter 2024", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T20:53:00.747520"}, {"row_number": 125, "subject": "ICICI: Mend Platform Access", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T20:53:02.765952"}, {"row_number": 126, "subject": "ICICI: <PERSON> <PERSON><PERSON><PERSON> (7d)", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T20:53:04.785842"}, {"row_number": 127, "subject": "ICICI: Mend Platform Access", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T20:53:06.794703"}, {"row_number": 128, "subject": "ICICI: <div><br>Upcoming NPS Survey&nbsp;</div>", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T20:53:08.815668"}, {"row_number": 129, "subject": "ICICI: October newsletter 2024", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T20:53:10.832294"}, {"row_number": 130, "subject": "ICICI: Risk Status", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T20:53:12.846883"}, {"row_number": 131, "subject": "ICICI: Primary Risk Reason", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T20:53:14.863275"}, {"row_number": 132, "subject": "ICICI: Risk Next Step(s)", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T20:53:16.879740"}, {"row_number": 133, "subject": "ICICI: Risk Status", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T20:53:18.895277"}, {"row_number": 134, "subject": "ICICI: Primary Risk Reason", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T20:53:20.912682"}, {"row_number": 135, "subject": "ICICI: Risk Next Step(s)", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T20:53:22.930360"}, {"row_number": 136, "subject": "ICICI: Support: IP Address Change", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T20:53:24.946305"}, {"row_number": 137, "subject": "ICICI: Business Model Launch", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T20:53:26.960444"}, {"row_number": 138, "subject": "ICICI: New Business Model_Webinar Follow up", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T20:53:28.977415"}, {"row_number": 139, "subject": "ICICI: MP Activity Frequency", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T20:53:30.991797"}, {"row_number": 140, "subject": "ICICI: <PERSON><PERSON>", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T20:53:33.008611"}, {"row_number": 141, "subject": "ICICI: New Business Model", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T20:53:35.024143"}, {"row_number": 142, "subject": "ICICI: Slack to #risk", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T20:53:37.046732"}, {"row_number": 143, "subject": "ICICI: Risk Next Step(s)", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T20:53:39.067612"}, {"row_number": 144, "subject": "ICICI: Primary Risk Reason", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T20:53:41.083851"}, {"row_number": 145, "subject": "ICICI: Risk Status", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T20:53:43.096215"}, {"row_number": 146, "subject": "ICICI: <PERSON><PERSON>", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T20:53:45.107931"}, {"row_number": 147, "subject": "ICICI: MP Activity Frequency", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T20:53:47.123322"}, {"row_number": 148, "subject": "ICICI: MP Activity Frequency", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T20:53:49.141916"}, {"row_number": 149, "subject": "ICICI: NPS Send Date", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T20:53:51.157292"}, {"row_number": 150, "subject": "ICICI: MP Activity Frequency", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T20:53:53.176686"}, {"row_number": 151, "subject": "ICICI: MP Activity", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T20:53:55.190879"}, {"row_number": 152, "subject": "ICICI: MP Activity Status", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T20:53:57.207119"}, {"row_number": 153, "subject": "ICICI: <PERSON><PERSON>", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T20:53:59.222163"}, {"row_number": 154, "subject": "ICICI: MP Activity Status", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T20:54:01.241323"}, {"row_number": 155, "subject": "ICICI: Product Roadmap H2 2024", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T20:54:03.259506"}, {"row_number": 156, "subject": "ICICI: <PERSON><PERSON><PERSON> to Delighted", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T20:54:05.278311"}, {"row_number": 157, "subject": "ICICI: NPS Last Sent", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T20:54:07.294081"}, {"row_number": 158, "subject": "ICICI: NPS Send Date", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T20:54:09.312482"}, {"row_number": 159, "subject": "ICICI: NPS Last Sent", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T20:54:11.328636"}, {"row_number": 160, "subject": "ICICI: NPS Send Date", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T20:54:13.344370"}, {"row_number": 161, "subject": "ICICI: <PERSON><PERSON><PERSON> to Delighted", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T20:54:15.361873"}, {"row_number": 162, "subject": "ICICI: Vulnerability Insights with MITRE Data", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T20:54:17.380931"}, {"row_number": 163, "subject": "ICICI: <PERSON><PERSON>", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T20:54:19.395012"}, {"row_number": 164, "subject": "ICICI: Mend Platform Access", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T20:54:21.410275"}, {"row_number": 165, "subject": "ICICI: CSM Managed", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T20:54:23.424712"}, {"row_number": 166, "subject": "ICICI: <div><br>Upcoming NPS Survey&nbsp;</div>", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T20:54:25.445518"}, {"row_number": 167, "subject": "ICICI: <PERSON><PERSON>", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T20:54:27.464246"}, {"row_number": 168, "subject": "ICICI: <PERSON><PERSON>", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T20:54:29.478822"}, {"row_number": 169, "subject": "ICICI: <PERSON><PERSON>", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T20:54:31.492895"}, {"row_number": 170, "subject": "ICICI: <PERSON><PERSON>", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T20:54:33.508510"}, {"row_number": 171, "subject": "ICICI: <PERSON> Follow Up Email", "activity_type": "Meeting", "error": "UI automation failed", "timestamp": "2025-05-29T20:54:35.525128"}, {"row_number": 172, "subject": "ICICI: <PERSON> Follow Up Email", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T20:54:37.546018"}, {"row_number": 173, "subject": "ICICI: <PERSON> Follow Up Email", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T20:54:39.561856"}, {"row_number": 174, "subject": "ICICI: Slack to #risk", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T20:54:41.581445"}, {"row_number": 175, "subject": "ICICI: <PERSON><PERSON>", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T20:54:43.595501"}, {"row_number": 176, "subject": "ICICI: <PERSON><PERSON><PERSON> - CSM Satisfaction Survey/Kelle Intro", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T20:54:45.614506"}, {"row_number": 177, "subject": "ICICI: <div><span class=\"mention-wrapper\" data-reactroot=\"\"><span class=\"field\">ICICI Bank</span>'s renewal date has passed</span></div>", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T20:54:47.631946"}, {"row_number": 178, "subject": "ICICI: Correction - CVE - 2024 - 3094_copy", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T20:54:49.650415"}, {"row_number": 179, "subject": "ICICI: <PERSON><PERSON>", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T20:54:51.668113"}, {"row_number": 180, "subject": "ICICI: All Other Customers - CVE - 2024 - 3094", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T20:54:53.684472"}, {"row_number": 181, "subject": "ICICI: <PERSON><PERSON>", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T20:54:55.699336"}, {"row_number": 182, "subject": "ICICI: License Utilization Status", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T20:54:57.719617"}, {"row_number": 183, "subject": "ICICI: License Utilization Status", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T20:54:59.736826"}, {"row_number": 184, "subject": "ICICI: <PERSON><PERSON>", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T20:55:01.755866"}, {"row_number": 185, "subject": "ICICI: <PERSON><PERSON>", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T20:55:03.773097"}, {"row_number": 186, "subject": "ICICI: <PERSON><PERSON>", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T20:55:05.791390"}, {"row_number": 187, "subject": "ICICI: <PERSON><PERSON>", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T20:55:07.808168"}, {"row_number": 188, "subject": "ICICI: Executive Engaged", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T20:55:09.822234"}, {"row_number": 189, "subject": "ICICI: AI Survey - Gold/ Platinum Customers - Follow Up", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T20:55:11.836729"}, {"row_number": 190, "subject": "ICICI: <PERSON><PERSON>", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T20:55:13.851692"}, {"row_number": 191, "subject": "ICICI: <PERSON><PERSON>", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T20:55:15.871220"}, {"row_number": 192, "subject": "ICICI: AI Survey - Int. Gold & Platinum Customers Oops_copy", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T20:55:17.886402"}, {"row_number": 193, "subject": "ICICI: AI Survey - Gold & Platinum Customers", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T20:55:19.894592"}, {"row_number": 194, "subject": "ICICI: <PERSON><PERSON>", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T20:55:21.906426"}, {"row_number": 195, "subject": "ICICI: <PERSON><PERSON>", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T20:55:23.925035"}, {"row_number": 196, "subject": "ICICI: <PERSON><PERSON>", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T20:55:25.936382"}, {"row_number": 197, "subject": "ICICI: <PERSON><PERSON>", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T20:55:27.948845"}, {"row_number": 198, "subject": "ICICI: Slack to #risk", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T20:55:29.960318"}, {"row_number": 199, "subject": "ICICI: Risk Update", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T20:55:31.979777"}, {"row_number": 200, "subject": "ICICI: Risk Status", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T20:55:33.996129"}, {"row_number": 201, "subject": "ICICI: <PERSON><PERSON>", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T20:55:36.014618"}, {"row_number": 202, "subject": "ICICI: <PERSON><PERSON>", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T20:55:38.031147"}, {"row_number": 203, "subject": "ICICI: NPS Send Date", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T20:55:40.051182"}, {"row_number": 204, "subject": "ICICI: NPS Informed", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T20:55:42.070339"}, {"row_number": 205, "subject": "ICICI: NPS Informed", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T20:55:44.148662"}, {"row_number": 206, "subject": "ICICI: Touch Status 2", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T20:55:46.164067"}, {"row_number": 207, "subject": "ICICI: <PERSON><PERSON>", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T20:55:48.177160"}, {"row_number": 208, "subject": "ICICI: <PERSON><PERSON>", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T20:55:50.196494"}, {"row_number": 209, "subject": "ICICI: <PERSON><PERSON>", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T20:55:52.209622"}, {"row_number": 210, "subject": "ICICI: <PERSON><PERSON>", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T20:55:54.230252"}, {"row_number": 211, "subject": "ICICI: <PERSON><PERSON>", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T20:55:56.250974"}, {"row_number": 212, "subject": "ICICI: <PERSON><PERSON>", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T20:55:58.267800"}, {"row_number": 213, "subject": "ICICI: Slack to #risk", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T20:56:00.285688"}, {"row_number": 214, "subject": "ICICI: Risk Update", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T20:56:02.296693"}, {"row_number": 215, "subject": "ICICI: Risk Status", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T20:56:04.315095"}, {"row_number": 216, "subject": "ICICI: <PERSON><PERSON>", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T20:56:06.331662"}, {"row_number": 217, "subject": "ICICI: <PERSON><PERSON>", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T20:56:08.347173"}, {"row_number": 218, "subject": "ICICI: <PERSON><PERSON>", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T20:56:10.361630"}, {"row_number": 219, "subject": "ICICI: <PERSON><PERSON>", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T20:56:12.380416"}, {"row_number": 220, "subject": "ICICI: <PERSON><PERSON>", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T20:56:14.397036"}, {"row_number": 221, "subject": "ICICI: <PERSON><PERSON>", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T20:56:16.416426"}, {"row_number": 222, "subject": "ICICI: <PERSON><PERSON>", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T20:56:18.447530"}, {"row_number": 223, "subject": "ICICI: <PERSON><PERSON>", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T20:56:20.465364"}, {"row_number": 224, "subject": "ICICI: <PERSON><PERSON>", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T20:56:22.487955"}, {"row_number": 225, "subject": "ICICI: <PERSON><PERSON>", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T20:56:24.508178"}, {"row_number": 226, "subject": "ICICI: <PERSON><PERSON>", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T20:56:26.521860"}, {"row_number": 227, "subject": "ICICI: ICICI Bank <> Mend - Catch Up Meeting External", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T20:56:28.541040"}, {"row_number": 228, "subject": "ICICI: <PERSON><PERSON>", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T20:56:30.556288"}, {"row_number": 229, "subject": "ICICI: SCA CD Variance Stages", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T20:56:32.569202"}, {"row_number": 230, "subject": "ICICI: License Utilization Status", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T20:56:34.584989"}, {"row_number": 231, "subject": "ICICI: Mend Vulnerability found by WithSecure", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T20:56:36.604030"}, {"row_number": 232, "subject": "ICICI: ICICI Bank <> Mend - Catch Up Meeting", "activity_type": "Meeting", "error": "UI automation failed", "timestamp": "2025-05-29T20:56:38.625446"}, {"row_number": 233, "subject": "ICICI: ICICI Bank<>Mend.io organization migration from LBA to VBA", "activity_type": "Meeting", "error": "UI automation failed", "timestamp": "2025-05-29T20:56:40.640490"}, {"row_number": 234, "subject": "ICICI: <PERSON><PERSON>", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T20:56:42.659101"}, {"row_number": 235, "subject": "ICICI: No need to touch base", "activity_type": "Meeting", "error": "UI automation failed", "timestamp": "2025-05-29T20:56:44.679246"}, {"row_number": 236, "subject": "ICICI: Unified Agent Hotfix now available-due to Java upgrade issue", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T20:56:46.692827"}, {"row_number": 237, "subject": "ICICI: Unified Agent - Java upgrade issue", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T20:56:48.712413"}, {"row_number": 238, "subject": "ICICI: LBA email change sent to the partner", "activity_type": "Meeting", "error": "UI automation failed", "timestamp": "2025-05-29T20:56:50.728275"}, {"row_number": 239, "subject": "ICICI: ICICI Bank<>MEND", "activity_type": "Meeting", "error": "UI automation failed", "timestamp": "2025-05-29T20:56:52.743857"}, {"row_number": 240, "subject": "ICICI: <PERSON><PERSON>", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T20:56:54.764304"}, {"row_number": 241, "subject": "ICICI: <PERSON><PERSON>", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T20:56:56.779575"}, {"row_number": 242, "subject": "ICICI: Request for Developer Training for ICICI", "activity_type": "Meeting", "error": "UI automation failed", "timestamp": "2025-05-29T20:56:58.794778"}, {"row_number": 243, "subject": "ICICI: Re: Queries on Licensing Policy related details - ICICI Bank", "activity_type": "Email", "error": "UI automation failed", "timestamp": "2025-05-29T20:57:00.810293"}, {"row_number": 244, "subject": "ICICI: Re: Mend integration with LDAP and SAML - ICICI", "activity_type": "Email", "error": "UI automation failed", "timestamp": "2025-05-29T20:57:02.828618"}, {"row_number": 245, "subject": "ICICI: <PERSON><PERSON>", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T20:57:04.844234"}, {"row_number": 246, "subject": "ICICI: NPS Send Date", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T20:57:06.861923"}, {"row_number": 247, "subject": "ICICI: NPS Informed", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T20:57:08.878053"}, {"row_number": 248, "subject": "ICICI: <PERSON><PERSON>", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T20:57:10.892747"}, {"row_number": 249, "subject": "ICICI: <PERSON><PERSON>", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T20:57:12.911450"}, {"row_number": 250, "subject": "ICICI: No need to touch base- working with the partner", "activity_type": "Meeting", "error": "UI automation failed", "timestamp": "2025-05-29T20:57:14.930008"}, {"row_number": 251, "subject": "ICICI: Inform Key Contacts of upcoming NPS survey", "activity_type": "Meeting", "error": "UI automation failed", "timestamp": "2025-05-29T20:57:16.947992"}, {"row_number": 252, "subject": "ICICI: <PERSON> is working to arrange a meeting with the bank and partner", "activity_type": "Meeting", "error": "UI automation failed", "timestamp": "2025-05-29T20:57:18.965500"}, {"row_number": 253, "subject": "ICICI: <PERSON><PERSON>", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T20:57:20.982375"}, {"row_number": 254, "subject": "ICICI: <PERSON><PERSON>", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T20:57:22.999106"}, {"row_number": 255, "subject": "ICICI: Discussion with ICICI Team - Mend - WhiteSource !!", "activity_type": "Meeting", "error": "UI automation failed", "timestamp": "2025-05-29T20:57:25.012847"}, {"row_number": 256, "subject": "ICICI: <PERSON><PERSON>", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T20:57:27.033244"}, {"row_number": 257, "subject": "ICICI: Update from Luis- partner manager", "activity_type": "Meeting", "error": "UI automation failed", "timestamp": "2025-05-29T20:57:29.052284"}, {"row_number": 258, "subject": "ICICI: <PERSON><PERSON>", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T20:57:31.071518"}, {"row_number": 259, "subject": "ICICI: Repo Integration-Accelerate your remediation", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T20:57:33.087839"}, {"row_number": 260, "subject": "ICICI: RSA 2023 conference", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T20:57:35.102599"}, {"row_number": 261, "subject": "ICICI: <PERSON><PERSON>", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T20:57:37.113687"}, {"row_number": 262, "subject": "ICICI: FEE ticket in place", "activity_type": "Meeting", "error": "UI automation failed", "timestamp": "2025-05-29T20:57:39.131822"}, {"row_number": 263, "subject": "ICICI: FEE ticket in place", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T20:57:41.143505"}, {"row_number": 264, "subject": "ICICI: FEE ticket in place", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T20:57:43.156134"}, {"row_number": 265, "subject": "ICICI: Discussion with ICICI Team - Mend - WhiteSource !!", "activity_type": "Meeting", "error": "UI automation failed", "timestamp": "2025-05-29T20:57:45.173042"}, {"row_number": 266, "subject": "ICICI: ICICI/Meteonic - Next Steps & Alignment", "activity_type": "Meeting", "error": "UI automation failed", "timestamp": "2025-05-29T20:57:47.193097"}, {"row_number": 267, "subject": "ICICI: Sunny to set up a joint meeting?", "activity_type": "Meeting", "error": "UI automation failed", "timestamp": "2025-05-29T20:57:49.211651"}, {"row_number": 268, "subject": "ICICI: Sunny to set up a joint meeting?", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T20:57:51.229900"}, {"row_number": 269, "subject": "ICICI: <PERSON><PERSON>", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T20:57:53.247891"}, {"row_number": 270, "subject": "ICICI: Sales Manager Region", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T20:57:55.263298"}, {"row_number": 271, "subject": "ICICI: SCA CD Variance Stages", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T20:57:57.281111"}, {"row_number": 272, "subject": "ICICI: <PERSON><PERSON>", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T20:57:59.294694"}, {"row_number": 273, "subject": "ICICI: Sunny to set up a joint meeting?", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T20:58:01.311901"}, {"row_number": 274, "subject": "ICICI: ********- zoom discussions in place", "activity_type": "Meeting", "error": "UI automation failed", "timestamp": "2025-05-29T20:58:03.327567"}, {"row_number": 275, "subject": "ICICI: Mend/Meteonic sync", "activity_type": "Meeting", "error": "UI automation failed", "timestamp": "2025-05-29T20:58:05.346387"}, {"row_number": 276, "subject": "ICICI: Case#********- call with Eng", "activity_type": "Meeting", "error": "UI automation failed", "timestamp": "2025-05-29T20:58:07.366350"}, {"row_number": 277, "subject": "ICICI: Working to set a meting with a the bank and the BP", "activity_type": "Meeting", "error": "UI automation failed", "timestamp": "2025-05-29T20:58:09.382773"}, {"row_number": 278, "subject": "ICICI: Meteonic will do onsite in the bank on Jan25th", "activity_type": "Meeting", "error": "UI automation failed", "timestamp": "2025-05-29T20:58:11.398960"}, {"row_number": 279, "subject": "ICICI: Mend's Malicious Package Communications", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T20:58:13.412609"}, {"row_number": 280, "subject": "ICICI: No need to touch base", "activity_type": "Meeting", "error": "UI automation failed", "timestamp": "2025-05-29T20:58:15.429876"}, {"row_number": 281, "subject": "ICICI: NPS Send Date", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T20:58:17.447389"}, {"row_number": 282, "subject": "ICICI: NPS Informed", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T20:58:19.467380"}, {"row_number": 283, "subject": "ICICI: NPS Informed", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T20:58:21.481766"}, {"row_number": 284, "subject": "ICICI: Internal sync with AM", "activity_type": "Meeting", "error": "UI automation failed", "timestamp": "2025-05-29T20:58:23.498758"}, {"row_number": 285, "subject": "ICICI: Touch Status", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T20:58:25.515163"}, {"row_number": 286, "subject": "ICICI: NPS Informed", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T20:58:27.534349"}, {"row_number": 287, "subject": "ICICI: Sales Manager Region", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T20:58:29.550130"}, {"row_number": 288, "subject": "ICICI: Team Manager Region", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T20:58:31.564801"}, {"row_number": 289, "subject": "ICICI: Team Manager", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T20:58:33.580500"}, {"row_number": 290, "subject": "ICICI: SCA CD Variance Stages", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T20:58:35.600557"}, {"row_number": 291, "subject": "ICICI: Setup a call with customers and explains our Policies best practice", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T20:58:37.618921"}, {"row_number": 292, "subject": "ICICI: Touch Status", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T20:58:39.637438"}, {"row_number": 293, "subject": "ICICI: Setup a call with customers and explains our Policies best practice", "activity_type": "Email", "error": "UI automation failed", "timestamp": "2025-05-29T20:58:41.653762"}, {"row_number": 294, "subject": "ICICI: Setup a call with customers and explains our Policies best practice", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T20:58:43.665903"}, {"row_number": 295, "subject": "ICICI: Outage in app.whitesourcesoftware.com", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T20:58:45.682015"}, {"row_number": 296, "subject": "ICICI: Touch Status", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T20:58:47.697038"}, {"row_number": 297, "subject": "ICICI: Check the usage and project number", "activity_type": "Meeting", "error": "UI automation failed", "timestamp": "2025-05-29T20:58:49.715164"}, {"row_number": 298, "subject": "ICICI: CSAT - Spring4Shell, Platinum&Gold", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T20:58:51.730346"}, {"row_number": 299, "subject": "ICICI: Touch Status", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T20:58:53.748006"}, {"row_number": 300, "subject": "ICICI: Team Manager Region", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T20:58:55.765656"}, {"row_number": 301, "subject": "ICICI: Spring4Shell HIGH SEVERITY Vulnerability in Spring core - Gold & Platinum", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T20:58:57.778970"}, {"row_number": 302, "subject": "ICICI: <PERSON><PERSON>", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T20:58:59.792326"}, {"row_number": 303, "subject": "ICICI: Touch Status", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T20:59:01.811096"}, {"row_number": 304, "subject": "ICICI: VBA migration", "activity_type": "Meeting", "error": "UI automation failed", "timestamp": "2025-05-29T20:59:03.829137"}, {"row_number": 305, "subject": "ICICI: Plz check with the usage is low", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T20:59:05.848422"}, {"row_number": 306, "subject": "ICICI: March 2022 Newsletter- Dedicated", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T20:59:07.870411"}, {"row_number": 307, "subject": "ICICI: <PERSON><PERSON>", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T20:59:09.889870"}, {"row_number": 308, "subject": "ICICI: BP customer- no touch base is needed", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T20:59:11.906679"}, {"row_number": 309, "subject": "ICICI: Touch Status", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T20:59:13.922411"}, {"row_number": 310, "subject": "ICICI: Customer Communication Regarding LBA to VBA Migration - SCA-ICICI Bank - for SCA-ICICI Bank", "activity_type": "Email", "error": "UI automation failed", "timestamp": "2025-05-29T20:59:15.942275"}, {"row_number": 311, "subject": "ICICI: Team Manager", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T20:59:17.961818"}, {"row_number": 312, "subject": "ICICI: <PERSON><PERSON>", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T20:59:19.982766"}, {"row_number": 313, "subject": "ICICI: Log4j Vulnerability webinar Dedicated CSM", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T20:59:21.996146"}, {"row_number": 314, "subject": "ICICI: Vulnerability in Apache Log4j2 library Part 2  Dedicated CSM Accounts", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T20:59:24.013483"}, {"row_number": 315, "subject": "ICICI: Severity Vulnerability (CVE-2021-44228) in the Log4j2 -Dedicated CSM", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T20:59:26.032290"}, {"row_number": 316, "subject": "ICICI: Customer Communication Regarding LBA to VBA Migration - SCA-ICICI Bank", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T20:59:28.052512"}, {"row_number": 317, "subject": "ICICI: Contact SCA-ICICI Bank about moving to VBA", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T20:59:30.066718"}, {"row_number": 318, "subject": "ICICI: <PERSON><PERSON>", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T20:59:32.087141"}, {"row_number": 319, "subject": "ICICI: SCA CD Variance Stages", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T20:59:34.103690"}, {"row_number": 320, "subject": "ICICI: Partner account", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T20:59:36.116536"}, {"row_number": 321, "subject": "ICICI: Sales Manager Region", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T20:59:38.138104"}, {"row_number": 322, "subject": "ICICI: <PERSON><PERSON>", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T20:59:40.151653"}]
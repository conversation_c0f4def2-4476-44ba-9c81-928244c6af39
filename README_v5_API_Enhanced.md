# 🐺 Wild Weasel v5.0 - API Enhanced Bulk Migration Agent

> **Mission**: Enhanced with Playwright API testing capabilities for efficient bulk migration from Totango to Gainsight

## 🚀 Key Enhancements in v5.0

### ✅ Playwright API Integration
- **Authenticated API Context**: Uses <PERSON>wright's API request context with session cookies
- **No UI Clicking Required**: Direct API calls for maximum efficiency 
- **Session Management**: Automatic cookie handling and authentication persistence

### ✅ Bulk Processing Pipeline
- **Intelligent Data Transformation**: Totango events → Gainsight API payloads
- **Parallel Batch Processing**: Configurable batch sizes with rate limiting
- **Advanced Retry Logic**: Exponential backoff with configurable retry attempts

### ✅ Enhanced Monitoring & Reporting
- **Real-time Statistics**: Live progress tracking during migration
- **Detailed Error Logging**: Comprehensive error tracking and categorization
- **Performance Metrics**: Processing times and throughput analysis

### ✅ Resilient Architecture
- **Graceful Error Handling**: Continue processing even when individual items fail
- **Intermediate Saves**: Periodic backup of results during long migrations
- **Resource Cleanup**: Proper cleanup of browser and API resources

## 📊 Data Transformation

The enhanced agent transforms Totango events into Gainsight timeline activities:

**Source**: Totango Events (`/Users/<USER>/Desktop/totango/ICICI_processed.json`)
- Event types: `automated_attribute_change`, `webhook`, `campaign_touch`, `account_alert`
- Company mapping: ICICI Bank (`0015p00005R7ysqAAB` → `1P02IPMAEL4M3CQGY4K5FHU22D2HHT11KCKU`)

**Target**: Gainsight Timeline Activities
- Proper activity type mapping
- Source system tracking fields
- Rich content with event details
- Timestamp preservation

## 🛠️ Setup & Installation

### Quick Start
```bash
# 1. Run the smart launcher
./launch_v5.sh

# 2. Choose option 1 to setup environment
# 3. Choose option 2 to validate transformation
# 4. Choose option 3 to run migration
```

### Manual Setup
```bash
# Install dependencies
pip3 install -r requirements_v5_enhanced.txt

# Install Playwright browsers
playwright install chromium

# Set up environment
cp .env.template .env
# Edit .env with your credentials
```

## 🔧 Configuration

### Environment Variables (`.env`)
```bash
# Required credentials
GAINSIGHT_USERNAME=your_username
GAINSIGHT_PASSWORD=your_password

# Optional: Performance tuning
BATCH_SIZE=10                 # Events per batch
MAX_RETRIES=3                 # Retry attempts per event
RATE_LIMIT_DELAY=1.0         # Seconds between requests
REQUEST_TIMEOUT=30000        # Request timeout in ms
LOG_LEVEL=INFO               # Logging level
```

### Data Source
Ensure your Totango data is located at:
```
/Users/<USER>/Desktop/totango/ICICI_processed.json
```

## 🚀 Usage

### Option 1: Smart Launcher (Recommended)
```bash
./launch_v5.sh
```
Interactive menu with guided setup and execution.

### Option 2: Direct Execution
```bash
# Validate transformation first
python3 validate_transformation_v5.py

# Run full migration
python3 wild_weasel_agent_v5_api_enhanced.py
```

### Option 3: Step-by-step
```bash
# 1. Setup
./setup_v5_enhanced.sh

# 2. Validate
python3 validate_transformation_v5.py

# 3. Migrate
python3 wild_weasel_agent_v5_api_enhanced.py
```

## 📈 Migration Process

### Phase 1: Authentication
- Launches Playwright browser
- Authenticates with Gainsight
- Establishes API request context
- Extracts session cookies

### Phase 2: Data Transformation
- Loads Totango events from JSON
- Transforms each event to Gainsight payload
- Validates payload structure
- Creates processing batches

### Phase 3: Bulk Migration
- Processes events in parallel batches
- For each event:
  1. Create draft via `/v2/activity/drafts` API
  2. Create activity via `/v2/activity` API
  3. Track results and retry on failure
- Real-time progress reporting

### Phase 4: Results & Cleanup
- Saves comprehensive results
- Generates migration report
- Cleans up browser resources

## 📊 Output Files

All output files include timestamp for easy tracking:

### Results Files
- `migration_results_v5_YYYYMMDD_HHMMSS.json` - All migration results
- `migration_failed_v5_YYYYMMDD_HHMMSS.json` - Failed migrations only
- `migration_stats_v5_YYYYMMDD_HHMMSS.json` - Summary statistics
- `intermediate_results_YYYYMMDD_HHMMSS.json` - Periodic backups

### Reports
- `migration_report_v5_api_YYYYMMDD_HHMMSS.txt` - Human-readable report
- `wild_weasel_v5_api.log` - Detailed execution log

## 🔍 Monitoring & Troubleshooting

### Real-time Monitoring
The agent provides live updates during migration:
```
🔄 Processing batch 1/50 (10 events)
📊 Batch 1 complete: 9/10 successful
📈 Overall progress: 45/500 (92.3% success rate)
```

### Common Issues

**Authentication Errors**
- Verify credentials in `.env` file
- Check Gainsight demo environment accessibility
- Ensure no MFA/2FA enabled for the account

**Data Transformation Errors**
- Run validation script first: `python3 validate_transformation_v5.py`
- Check Totango data file format and location
- Verify company mapping in transformer

**API Rate Limiting**
- Increase `RATE_LIMIT_DELAY` in `.env`
- Reduce `BATCH_SIZE` for more conservative processing
- Check Gainsight API limits

**Browser/Playwright Issues**
- Run `playwright install chromium`
- Check for conflicting browser processes
- Try running with `headless=True` in code

### Log Analysis
```bash
# View real-time logs
tail -f wild_weasel_v5_api.log

# Search for errors
grep "ERROR" wild_weasel_v5_api.log

# Check success rate
grep "success rate" wild_weasel_v5_api.log
```

## 📋 Migration Statistics

The agent tracks comprehensive statistics:

- **Total Events**: Number of Totango events processed
- **Success Rate**: Percentage of successful migrations
- **Processing Time**: Average time per event
- **Batch Performance**: Success rate per batch
- **API vs UI**: Method breakdown (v5.0 is primarily API)
- **Error Analysis**: Categorized failure reasons

## 🔄 Retry Logic

Advanced retry mechanism with exponential backoff:
1. **First Attempt**: Immediate API call
2. **Retry 1**: Wait 1 second, retry
3. **Retry 2**: Wait 2 seconds, retry  
4. **Retry 3**: Wait 4 seconds, final attempt
5. **Failure**: Mark as failed and continue

## 🎯 Performance Optimization

### Recommended Settings by Data Size

**Small Dataset (< 100 events)**
```bash
BATCH_SIZE=5
RATE_LIMIT_DELAY=0.5
MAX_RETRIES=2
```

**Medium Dataset (100-1000 events)**  
```bash
BATCH_SIZE=10
RATE_LIMIT_DELAY=1.0
MAX_RETRIES=3
```

**Large Dataset (1000+ events)**
```bash
BATCH_SIZE=15
RATE_LIMIT_DELAY=1.5
MAX_RETRIES=3
```

## 🆘 Support & Troubleshooting

### Pre-Migration Checklist
- [ ] Totango data file exists and is valid JSON
- [ ] Gainsight credentials are correct
- [ ] Internet connection is stable
- [ ] No other browser automation running
- [ ] Sufficient disk space for results

### Emergency Stops
- **Keyboard Interrupt**: `Ctrl+C` will gracefully stop and save current progress
- **Browser Issues**: Close any open Chromium/Chrome browsers and restart
- **API Limits**: Reduce batch size and increase delays

### Data Recovery
If migration is interrupted:
1. Check `intermediate_results_*.json` files for partial progress
2. Review `migration_stats_*.json` for last known position
3. Restart migration (it will start fresh but you can compare results)

## 🔒 Security Considerations

- Credentials are stored in `.env` file (add to `.gitignore`)
- Browser automation uses standard security practices
- API calls use authenticated session cookies
- No sensitive data is logged or exposed

## 🚀 Future Enhancements

Potential improvements for v6.0:
- Resume functionality for interrupted migrations
- UI fallback for failed API calls
- Advanced filtering and data validation
- Multi-company support
- Real-time dashboard for monitoring

---

## 📞 Quick Reference

### Essential Commands
```bash
# Setup everything
./launch_v5.sh

# Test transformation
python3 validate_transformation_v5.py

# Run migration  
python3 wild_weasel_agent_v5_api_enhanced.py

# View logs
tail -f wild_weasel_v5_api.log
```

### Key Files
- `wild_weasel_agent_v5_api_enhanced.py` - Main migration agent
- `validate_transformation_v5.py` - Data validation tool
- `launch_v5.sh` - Interactive launcher
- `setup_v5_enhanced.sh` - Environment setup
- `.env` - Configuration file

---

🐺 **Wild Weasel v5.0** - Making migrations sneaky fast with API power!

*Mission Status: Enhanced and Ready for Bulk Operations* ✅

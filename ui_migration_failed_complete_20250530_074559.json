[{"row_number": "1", "activity": {"row_number": "1", "subject": "ICICI: Mend Platform Access", "activity_date": "2025-05-29 03:30:19", "activity_type": "Update", "content_html": "<p>Automated update: Mend Platform Access changed from 'Access Provided' to 'Access Provided'</p>", "plain_text": "Automated update: Mend Platform Access changed from 'Access Provided' to 'Access Provided'", "author_name": "<PERSON>", "author_email": "<EMAIL>", "flow_type": "Onboarding", "touchpoint_reason": "", "external_attendees": "", "company": "ICICI", "original_activity_type": "automated_attribute_change", "original_meeting_type": "No Meeting Type", "source": "ICICI_MIGRATION"}, "error": "CSV UI automation failed", "timestamp": "2025-05-29T23:05:07.843375", "subject": "ICICI: Mend Platform Access"}, {"row_number": "2", "activity": {"row_number": "2", "subject": "ICICI: Mend Platform Access", "activity_date": "2025-05-25 03:30:16", "activity_type": "Update", "content_html": "<p>Automated update: Mend Platform Access changed from 'Access Provided' to 'Access Provided'</p>", "plain_text": "Automated update: Mend Platform Access changed from 'Access Provided' to 'Access Provided'", "author_name": "<PERSON>", "author_email": "<EMAIL>", "flow_type": "Onboarding", "touchpoint_reason": "", "external_attendees": "", "company": "ICICI", "original_activity_type": "automated_attribute_change", "original_meeting_type": "No Meeting Type", "source": "ICICI_MIGRATION"}, "error": "CSV UI automation failed", "timestamp": "2025-05-29T23:05:40.850554", "subject": "ICICI: Mend Platform Access"}, {"row_number": "3", "activity": {"row_number": "3", "subject": "ICICI: Mend Platform Access", "activity_date": "2025-05-21 03:30:16", "activity_type": "Update", "content_html": "<p>Automated update: Mend Platform Access changed from 'Access Provided' to 'Access Provided'</p>", "plain_text": "Automated update: Mend Platform Access changed from 'Access Provided' to 'Access Provided'", "author_name": "<PERSON>", "author_email": "<EMAIL>", "flow_type": "Onboarding", "touchpoint_reason": "", "external_attendees": "", "company": "ICICI", "original_activity_type": "automated_attribute_change", "original_meeting_type": "No Meeting Type", "source": "ICICI_MIGRATION"}, "error": "CSV UI automation failed", "timestamp": "2025-05-29T23:06:13.863591", "subject": "ICICI: Mend Platform Access"}, {"row_number": "4", "activity": {"row_number": "4", "subject": "ICICI: Mend Platform Access", "activity_date": "2025-05-19 03:30:17", "activity_type": "Update", "content_html": "<p>Automated update: Mend Platform Access changed from 'Access Provided' to 'Access Provided'</p>", "plain_text": "Automated update: Mend Platform Access changed from 'Access Provided' to 'Access Provided'", "author_name": "<PERSON>", "author_email": "<EMAIL>", "flow_type": "Onboarding", "touchpoint_reason": "", "external_attendees": "", "company": "ICICI", "original_activity_type": "automated_attribute_change", "original_meeting_type": "No Meeting Type", "source": "ICICI_MIGRATION"}, "error": "CSV UI automation failed", "timestamp": "2025-05-29T23:06:46.872262", "subject": "ICICI: Mend Platform Access"}, {"row_number": "5", "activity": {"row_number": "5", "subject": "ICICI: Mend Platform Access", "activity_date": "2025-05-17 03:30:17", "activity_type": "Update", "content_html": "<p>Automated update: Mend Platform Access changed from 'Access Provided' to 'Access Provided'</p>", "plain_text": "Automated update: Mend Platform Access changed from 'Access Provided' to 'Access Provided'", "author_name": "<PERSON>", "author_email": "<EMAIL>", "flow_type": "Onboarding", "touchpoint_reason": "", "external_attendees": "", "company": "ICICI", "original_activity_type": "automated_attribute_change", "original_meeting_type": "No Meeting Type", "source": "ICICI_MIGRATION"}, "error": "CSV UI automation failed", "timestamp": "2025-05-29T23:07:19.876899", "subject": "ICICI: Mend Platform Access"}, {"row_number": "6", "activity": {"row_number": "6", "subject": "ICICI: Mend Platform Access", "activity_date": "2025-05-15 03:30:17", "activity_type": "Update", "content_html": "<p>Automated update: Mend Platform Access changed from 'Access Provided' to 'Access Provided'</p>", "plain_text": "Automated update: Mend Platform Access changed from 'Access Provided' to 'Access Provided'", "author_name": "<PERSON>", "author_email": "<EMAIL>", "flow_type": "Onboarding", "touchpoint_reason": "", "external_attendees": "", "company": "ICICI", "original_activity_type": "automated_attribute_change", "original_meeting_type": "No Meeting Type", "source": "ICICI_MIGRATION"}, "error": "CSV UI automation failed", "timestamp": "2025-05-29T23:07:30.579543", "subject": "ICICI: Mend Platform Access"}, {"row_number": "7", "activity": {"row_number": "7", "subject": "ICICI: <PERSON><PERSON><PERSON> to Delighted", "activity_date": "2025-05-10 06:30:27", "activity_type": "Update", "content_html": "<p>Webhook 'Zapier to Delighted' was success</p>", "plain_text": "Webhook 'Zapier to Delighted' was success", "author_name": "<PERSON>", "author_email": "<EMAIL>", "flow_type": "Adoption", "touchpoint_reason": "", "external_attendees": "", "company": "ICICI", "original_activity_type": "webhook", "original_meeting_type": "No Meeting Type", "source": "ICICI_MIGRATION"}, "error": "CSV UI automation failed", "timestamp": "2025-05-29T23:07:33.586829", "subject": "ICICI: <PERSON><PERSON><PERSON> to Delighted"}, {"row_number": "8", "activity": {"row_number": "8", "subject": "ICICI: NPS Send Date", "activity_date": "2025-05-10 06:30:16", "activity_type": "Update", "content_html": "<p>Automated update: NPS Send Date changed from '2025-05-09' to '2025-11-06T05:30:16.572Z'</p>", "plain_text": "Automated update: NPS Send Date changed from '2025-05-09' to '2025-11-06T05:30:16.572Z'", "author_name": "<PERSON>", "author_email": "<EMAIL>", "flow_type": "Adoption", "touchpoint_reason": "", "external_attendees": "", "company": "ICICI", "original_activity_type": "automated_attribute_change", "original_meeting_type": "No Meeting Type", "source": "ICICI_MIGRATION"}, "error": "CSV UI automation failed", "timestamp": "2025-05-29T23:07:36.593099", "subject": "ICICI: NPS Send Date"}, {"row_number": "9", "activity": {"row_number": "9", "subject": "ICICI: NPS Last Sent", "activity_date": "2025-05-10 06:30:16", "activity_type": "Update", "content_html": "<p>Automated update: NPS Last Sent changed from '2024-11-10' to '2025-05-10T05:30:16.572Z'</p>", "plain_text": "Automated update: NPS Last Sent changed from '2024-11-10' to '2025-05-10T05:30:16.572Z'", "author_name": "<PERSON>", "author_email": "<EMAIL>", "flow_type": "Adoption", "touchpoint_reason": "", "external_attendees": "", "company": "ICICI", "original_activity_type": "automated_attribute_change", "original_meeting_type": "No Meeting Type", "source": "ICICI_MIGRATION"}, "error": "CSV UI automation failed", "timestamp": "2025-05-29T23:07:39.599904", "subject": "ICICI: NPS Last Sent"}, {"row_number": "10", "activity": {"row_number": "10", "subject": "ICICI: Mend Platform Access", "activity_date": "2025-05-09 03:30:17", "activity_type": "Update", "content_html": "<p>Automated update: Mend Platform Access changed from 'Access Provided' to 'Access Provided'</p>", "plain_text": "Automated update: Mend Platform Access changed from 'Access Provided' to 'Access Provided'", "author_name": "<PERSON>", "author_email": "<EMAIL>", "flow_type": "Onboarding", "touchpoint_reason": "", "external_attendees": "", "company": "ICICI", "original_activity_type": "automated_attribute_change", "original_meeting_type": "No Meeting Type", "source": "ICICI_MIGRATION"}, "error": "CSV UI automation failed", "timestamp": "2025-05-29T23:07:42.605597", "subject": "ICICI: Mend Platform Access"}, {"row_number": "11", "activity": {"row_number": "11", "subject": "ICICI: May Newsletter: AI Risk Insights, Smarter Compliance and Cloud Integration!", "activity_date": "2025-05-07 18:20:00", "activity_type": "Update", "content_html": "<p>One-time campaign \"May Newsletter: AI Risk Insights, Smarter Compliance and Cloud Integration!\" was sent to 3 users. </p>", "plain_text": "One-time campaign \"May Newsletter: AI Risk Insights, Smarter Compliance and Cloud Integration!\" was sent to 3 users.", "author_name": "<PERSON>", "author_email": "<EMAIL>", "flow_type": "Adoption", "touchpoint_reason": "", "external_attendees": "", "company": "ICICI", "original_activity_type": "campaign_touch", "original_meeting_type": "No Meeting Type", "source": "ICICI_MIGRATION"}, "error": "CSV UI automation failed", "timestamp": "2025-05-29T23:07:55.617977", "subject": "ICICI: May Newsletter: AI Risk Insights, Smarter Compliance and Cloud Integration!"}, {"row_number": "12", "activity": {"row_number": "12", "subject": "ICICI: Mend Platform Access", "activity_date": "2025-05-07 03:30:16", "activity_type": "Update", "content_html": "<p>Automated update: Mend Platform Access changed from 'Access Provided' to 'Access Provided'</p>", "plain_text": "Automated update: Mend Platform Access changed from 'Access Provided' to 'Access Provided'", "author_name": "<PERSON>", "author_email": "<EMAIL>", "flow_type": "Onboarding", "touchpoint_reason": "", "external_attendees": "", "company": "ICICI", "original_activity_type": "automated_attribute_change", "original_meeting_type": "No Meeting Type", "source": "ICICI_MIGRATION"}, "error": "CSV UI automation failed", "timestamp": "2025-05-29T23:07:58.623985", "subject": "ICICI: Mend Platform Access"}, {"row_number": "13", "activity": {"row_number": "13", "subject": "ICICI: Mend Platform Access", "activity_date": "2025-05-03 03:30:16", "activity_type": "Update", "content_html": "<p>Automated update: Mend Platform Access changed from 'Access Provided' to 'Access Provided'</p>", "plain_text": "Automated update: Mend Platform Access changed from 'Access Provided' to 'Access Provided'", "author_name": "<PERSON>", "author_email": "<EMAIL>", "flow_type": "Onboarding", "touchpoint_reason": "", "external_attendees": "", "company": "ICICI", "original_activity_type": "automated_attribute_change", "original_meeting_type": "No Meeting Type", "source": "ICICI_MIGRATION"}, "error": "CSV UI automation failed", "timestamp": "2025-05-29T23:08:01.628314", "subject": "ICICI: Mend Platform Access"}, {"row_number": "14", "activity": {"row_number": "14", "subject": "ICICI: MP Activity Frequency", "activity_date": "2025-05-01 08:30:14", "activity_type": "Update", "content_html": "<p>Automated update: MP Activity Frequency changed from 'Light Use' to 'Abandoned'</p>", "plain_text": "Automated update: MP Activity Frequency changed from 'Light Use' to 'Abandoned'", "author_name": "<PERSON>", "author_email": "<EMAIL>", "flow_type": "Onboarding", "touchpoint_reason": "", "external_attendees": "", "company": "ICICI", "original_activity_type": "automated_attribute_change", "original_meeting_type": "No Meeting Type", "source": "ICICI_MIGRATION"}, "error": "CSV UI automation failed", "timestamp": "2025-05-29T23:08:04.634977", "subject": "ICICI: MP Activity Frequency"}, {"row_number": "15", "activity": {"row_number": "15", "subject": "ICICI: <PERSON> <PERSON><PERSON><PERSON> (7d)", "activity_date": "2025-05-01 08:30:14", "activity_type": "Update", "content_html": "<p>Automated update: MP Vs. Legacy (7d) changed from 'Legacy Only' to 'Legacy Only'</p>", "plain_text": "Automated update: MP Vs. Legacy (7d) changed from 'Legacy Only' to 'Legacy Only'", "author_name": "<PERSON>", "author_email": "<EMAIL>", "flow_type": "Onboarding", "touchpoint_reason": "", "external_attendees": "", "company": "ICICI", "original_activity_type": "automated_attribute_change", "original_meeting_type": "No Meeting Type", "source": "ICICI_MIGRATION"}, "error": "CSV UI automation failed", "timestamp": "2025-05-29T23:08:07.651376", "subject": "ICICI: <PERSON> <PERSON><PERSON><PERSON> (7d)"}, {"row_number": "16", "activity": {"row_number": "16", "subject": "ICICI: Mend Platform Access", "activity_date": "2025-04-29 03:30:16", "activity_type": "Update", "content_html": "<p>Automated update: Mend Platform Access changed from 'Access Provided' to 'Access Provided'</p>", "plain_text": "Automated update: Mend Platform Access changed from 'Access Provided' to 'Access Provided'", "author_name": "<PERSON>", "author_email": "<EMAIL>", "flow_type": "Onboarding", "touchpoint_reason": "", "external_attendees": "", "company": "ICICI", "original_activity_type": "automated_attribute_change", "original_meeting_type": "No Meeting Type", "source": "ICICI_MIGRATION"}, "error": "CSV UI automation failed", "timestamp": "2025-05-29T23:08:10.656394", "subject": "ICICI: Mend Platform Access"}, {"row_number": "17", "activity": {"row_number": "17", "subject": "ICICI: SCA CD Variance Stages", "activity_date": "2025-04-25 08:30:07", "activity_type": "Update", "content_html": "<p>Automated update: SCA CD Variance Stages changed from 'Low' to 'Moderate'</p>", "plain_text": "Automated update: SCA CD Variance Stages changed from 'Low' to 'Moderate'", "author_name": "<PERSON>", "author_email": "<EMAIL>", "flow_type": "Intelligence", "touchpoint_reason": "", "external_attendees": "", "company": "ICICI", "original_activity_type": "automated_attribute_change", "original_meeting_type": "No Meeting Type", "source": "ICICI_MIGRATION"}, "error": "CSV UI automation failed", "timestamp": "2025-05-29T23:08:13.662199", "subject": "ICICI: SCA CD Variance Stages"}, {"row_number": "18", "activity": {"row_number": "18", "subject": "ICICI: <div><br>Upcoming NPS Survey&nbsp;</div>", "activity_date": "2025-04-25 06:30:09", "activity_type": "Update", "content_html": "<p><div>An NPS Survey Send Date is approaching in 14 days for a Display Contact at <span class=\"mention-wrapper\" data-reactroot=\"\"><span class=\"field\">ICICI Bank</span><br></span></div></p>", "plain_text": "An NPS Survey Send Date is approaching in 14 days for a Display Contact at ICICI Bank", "author_name": "<PERSON>", "author_email": "<EMAIL>", "flow_type": "Adoption", "touchpoint_reason": "", "external_attendees": "", "company": "ICICI", "original_activity_type": "account_alert", "original_meeting_type": "No Meeting Type", "source": "ICICI_MIGRATION"}, "error": "CSV UI automation failed", "timestamp": "2025-05-29T23:08:16.664912", "subject": "ICICI: <div><br>Upcoming NPS Survey&nbsp;</div>"}, {"row_number": "19", "activity": {"row_number": "19", "subject": "ICICI: Mend Platform Access", "activity_date": "2025-04-22 03:30:16", "activity_type": "Update", "content_html": "<p>Automated update: Mend Platform Access changed from 'Access Provided' to 'Access Provided'</p>", "plain_text": "Automated update: Mend Platform Access changed from 'Access Provided' to 'Access Provided'", "author_name": "<PERSON>", "author_email": "<EMAIL>", "flow_type": "Onboarding", "touchpoint_reason": "", "external_attendees": "", "company": "ICICI", "original_activity_type": "automated_attribute_change", "original_meeting_type": "No Meeting Type", "source": "ICICI_MIGRATION"}, "error": "CSV UI automation failed", "timestamp": "2025-05-29T23:08:19.670996", "subject": "ICICI: Mend Platform Access"}, {"row_number": "20", "activity": {"row_number": "20", "subject": "ICICI: MP Activity", "activity_date": "2025-04-18 08:30:09", "activity_type": "Update", "content_html": "<p>Automated update: MP Activity changed from 'Active' to 'Abandoned'</p>", "plain_text": "Automated update: MP Activity changed from 'Active' to 'Abandoned'", "author_name": "<PERSON>", "author_email": "<EMAIL>", "flow_type": "Adoption", "touchpoint_reason": "", "external_attendees": "", "company": "ICICI", "original_activity_type": "automated_attribute_change", "original_meeting_type": "No Meeting Type", "source": "ICICI_MIGRATION"}, "error": "CSV UI automation failed", "timestamp": "2025-05-29T23:08:22.677404", "subject": "ICICI: MP Activity"}, {"row_number": "21", "activity": {"row_number": "21", "subject": "ICICI: MP Activity Frequency", "activity_date": "2025-04-17 03:30:10", "activity_type": "Update", "content_html": "<p>Automated update: MP Activity Frequency changed from 'Abandoned' to 'Light Use'</p>", "plain_text": "Automated update: MP Activity Frequency changed from 'Abandoned' to 'Light Use'", "author_name": "<PERSON>", "author_email": "<EMAIL>", "flow_type": "Onboarding", "touchpoint_reason": "", "external_attendees": "", "company": "ICICI", "original_activity_type": "automated_attribute_change", "original_meeting_type": "No Meeting Type", "source": "ICICI_MIGRATION"}, "error": "CSV UI automation failed", "timestamp": "2025-05-29T23:08:35.688799", "subject": "ICICI: MP Activity Frequency"}, {"row_number": "22", "activity": {"row_number": "22", "subject": "ICICI: MITRE", "activity_date": "2025-04-17 01:20:03", "activity_type": "Update", "content_html": "<p>One-time campaign \"MITRE \" was sent to 3 users. </p>", "plain_text": "One-time campaign \"MITRE \" was sent to 3 users.", "author_name": "<PERSON>", "author_email": "<EMAIL>", "flow_type": "Support", "touchpoint_reason": "", "external_attendees": "", "company": "ICICI", "original_activity_type": "campaign_touch", "original_meeting_type": "No Meeting Type", "source": "ICICI_MIGRATION"}, "error": "CSV UI automation failed", "timestamp": "2025-05-29T23:08:38.694992", "subject": "ICICI: MITRE"}, {"row_number": "23", "activity": {"row_number": "23", "subject": "ICICI: MP Abandonment Date", "activity_date": "2025-04-16 10:30:09", "activity_type": "Update", "content_html": "<p>Automated update: MP Abandonment Date changed from '' to ''</p>", "plain_text": "Automated update: MP Abandonment Date changed from '' to ''", "author_name": "<PERSON>", "author_email": "<EMAIL>", "flow_type": "Onboarding", "touchpoint_reason": "", "external_attendees": "", "company": "ICICI", "original_activity_type": "automated_attribute_change", "original_meeting_type": "No Meeting Type", "source": "ICICI_MIGRATION"}, "error": "CSV UI automation failed", "timestamp": "2025-05-29T23:08:41.701139", "subject": "ICICI: MP Abandonment Date"}, {"row_number": "24", "activity": {"row_number": "24", "subject": "ICICI: MP Activity", "activity_date": "2025-04-16 09:30:13", "activity_type": "Update", "content_html": "<p>Automated update: MP Activity changed from 'Inactive' to 'Active'</p>", "plain_text": "Automated update: MP Activity changed from 'Inactive' to 'Active'", "author_name": "<PERSON>", "author_email": "<EMAIL>", "flow_type": "Adoption", "touchpoint_reason": "", "external_attendees": "", "company": "ICICI", "original_activity_type": "automated_attribute_change", "original_meeting_type": "No Meeting Type", "source": "ICICI_MIGRATION"}, "error": "CSV UI automation failed", "timestamp": "2025-05-29T23:08:44.708019", "subject": "ICICI: MP Activity"}, {"row_number": "25", "activity": {"row_number": "25", "subject": "ICICI: Mend Platform Access", "activity_date": "2025-04-15 03:30:12", "activity_type": "Update", "content_html": "<p>Automated update: Mend Platform Access changed from 'Access Provided' to 'Access Provided'</p>", "plain_text": "Automated update: Mend Platform Access changed from 'Access Provided' to 'Access Provided'", "author_name": "<PERSON>", "author_email": "<EMAIL>", "flow_type": "Onboarding", "touchpoint_reason": "", "external_attendees": "", "company": "ICICI", "original_activity_type": "automated_attribute_change", "original_meeting_type": "No Meeting Type", "source": "ICICI_MIGRATION"}, "error": "CSV UI automation failed", "timestamp": "2025-05-29T23:08:47.716529", "subject": "ICICI: Mend Platform Access"}, {"row_number": "26", "activity": {"row_number": "26", "subject": "ICICI: Mend Platform Access", "activity_date": "2025-04-08 03:30:12", "activity_type": "Update", "content_html": "<p>Automated update: Mend Platform Access changed from 'Access Provided' to 'Access Provided'</p>", "plain_text": "Automated update: Mend Platform Access changed from 'Access Provided' to 'Access Provided'", "author_name": "<PERSON>", "author_email": "<EMAIL>", "flow_type": "Onboarding", "touchpoint_reason": "", "external_attendees": "", "company": "ICICI", "original_activity_type": "automated_attribute_change", "original_meeting_type": "No Meeting Type", "source": "ICICI_MIGRATION"}, "error": "CSV UI automation failed", "timestamp": "2025-05-29T23:08:50.719032", "subject": "ICICI: Mend Platform Access"}, {"row_number": "27", "activity": {"row_number": "27", "subject": "ICICI: April Newsletter: New Dashboard, Mend AI, and More!", "activity_date": "2025-04-07 19:00:00", "activity_type": "Update", "content_html": "<p>One-time campaign \"April Newsletter: New Dashboard, Mend AI, and More!\" was sent to 3 users. </p>", "plain_text": "One-time campaign \"April Newsletter: New Dashboard, Mend AI, and More!\" was sent to 3 users.", "author_name": "<PERSON>", "author_email": "<EMAIL>", "flow_type": "Adoption", "touchpoint_reason": "", "external_attendees": "", "company": "ICICI", "original_activity_type": "campaign_touch", "original_meeting_type": "No Meeting Type", "source": "ICICI_MIGRATION"}, "error": "CSV UI automation failed", "timestamp": "2025-05-29T23:08:53.724438", "subject": "ICICI: April Newsletter: New Dashboard, Mend AI, and More!"}, {"row_number": "28", "activity": {"row_number": "28", "subject": "ICICI: Mend Platform Access", "activity_date": "2025-04-02 03:30:11", "activity_type": "Update", "content_html": "<p>Automated update: Mend Platform Access changed from 'Access Provided' to 'Access Provided'</p>", "plain_text": "Automated update: Mend Platform Access changed from 'Access Provided' to 'Access Provided'", "author_name": "<PERSON>", "author_email": "<EMAIL>", "flow_type": "Onboarding", "touchpoint_reason": "", "external_attendees": "", "company": "ICICI", "original_activity_type": "automated_attribute_change", "original_meeting_type": "No Meeting Type", "source": "ICICI_MIGRATION"}, "error": "CSV UI automation failed", "timestamp": "2025-05-29T23:08:56.741206", "subject": "ICICI: Mend Platform Access"}, {"row_number": "29", "activity": {"row_number": "29", "subject": "ICICI: Mend Platform Access", "activity_date": "2025-03-29 02:30:08", "activity_type": "Update", "content_html": "<p>Automated update: Mend Platform Access changed from 'Access Provided' to 'Access Provided'</p>", "plain_text": "Automated update: Mend Platform Access changed from 'Access Provided' to 'Access Provided'", "author_name": "<PERSON>", "author_email": "<EMAIL>", "flow_type": "Onboarding", "touchpoint_reason": "", "external_attendees": "", "company": "ICICI", "original_activity_type": "automated_attribute_change", "original_meeting_type": "No Meeting Type", "source": "ICICI_MIGRATION"}, "error": "CSV UI automation failed", "timestamp": "2025-05-29T23:08:59.750794", "subject": "ICICI: Mend Platform Access"}, {"row_number": "30", "activity": {"row_number": "30", "subject": "ICICI: Mend Platform Access", "activity_date": "2025-03-24 02:30:11", "activity_type": "Update", "content_html": "<p>Automated update: Mend Platform Access changed from 'Access Provided' to 'Access Provided'</p>", "plain_text": "Automated update: Mend Platform Access changed from 'Access Provided' to 'Access Provided'", "author_name": "<PERSON>", "author_email": "<EMAIL>", "flow_type": "Onboarding", "touchpoint_reason": "", "external_attendees": "", "company": "ICICI", "original_activity_type": "automated_attribute_change", "original_meeting_type": "No Meeting Type", "source": "ICICI_MIGRATION"}, "error": "CSV UI automation failed", "timestamp": "2025-05-29T23:09:02.756238", "subject": "ICICI: Mend Platform Access"}, {"row_number": "31", "activity": {"row_number": "31", "subject": "ICICI: MP Activity Frequency", "activity_date": "2025-03-19 07:30:10", "activity_type": "Update", "content_html": "<p>Automated update: MP Activity Frequency changed from 'Light Use' to 'Abandoned'</p>", "plain_text": "Automated update: MP Activity Frequency changed from 'Light Use' to 'Abandoned'", "author_name": "<PERSON>", "author_email": "<EMAIL>", "flow_type": "Onboarding", "touchpoint_reason": "", "external_attendees": "", "company": "ICICI", "original_activity_type": "automated_attribute_change", "original_meeting_type": "No Meeting Type", "source": "ICICI_MIGRATION"}, "error": "CSV UI automation failed", "timestamp": "2025-05-29T23:09:15.768618", "subject": "ICICI: MP Activity Frequency"}, {"row_number": "32", "activity": {"row_number": "32", "subject": "ICICI: MP Activity", "activity_date": "2025-03-06 07:30:08", "activity_type": "Update", "content_html": "<p>Automated update: MP Activity changed from 'Active' to 'Abandoned'</p>", "plain_text": "Automated update: MP Activity changed from 'Active' to 'Abandoned'", "author_name": "<PERSON>", "author_email": "<EMAIL>", "flow_type": "Adoption", "touchpoint_reason": "", "external_attendees": "", "company": "ICICI", "original_activity_type": "automated_attribute_change", "original_meeting_type": "No Meeting Type", "source": "ICICI_MIGRATION"}, "error": "CSV UI automation failed", "timestamp": "2025-05-29T23:09:18.774930", "subject": "ICICI: MP Activity"}, {"row_number": "33", "activity": {"row_number": "33", "subject": "ICICI: <PERSON> Newsletter: Introducing Mend AI and More!", "activity_date": "2025-03-05 16:40:03", "activity_type": "Update", "content_html": "<p>One-time campaign \"March Newsletter: Introducing Mend AI and More!\" was sent to 3 users. </p>", "plain_text": "One-time campaign \"March Newsletter: Introducing Mend AI and More!\" was sent to 3 users.", "author_name": "<PERSON>", "author_email": "<EMAIL>", "flow_type": "Adoption", "touchpoint_reason": "", "external_attendees": "", "company": "ICICI", "original_activity_type": "campaign_touch", "original_meeting_type": "No Meeting Type", "source": "ICICI_MIGRATION"}, "error": "CSV UI automation failed", "timestamp": "2025-05-29T23:09:21.781121", "subject": "ICICI: <PERSON> Newsletter: Introducing Mend AI and More!"}, {"row_number": "34", "activity": {"row_number": "34", "subject": "ICICI: MP Abandonment Date", "activity_date": "2025-03-04 12:30:12", "activity_type": "Update", "content_html": "<p>Automated update: MP Abandonment Date changed from '' to ''</p>", "plain_text": "Automated update: MP Abandonment Date changed from '' to ''", "author_name": "<PERSON>", "author_email": "<EMAIL>", "flow_type": "Onboarding", "touchpoint_reason": "", "external_attendees": "", "company": "ICICI", "original_activity_type": "automated_attribute_change", "original_meeting_type": "No Meeting Type", "source": "ICICI_MIGRATION"}, "error": "CSV UI automation failed", "timestamp": "2025-05-29T23:09:24.787256", "subject": "ICICI: MP Abandonment Date"}, {"row_number": "35", "activity": {"row_number": "35", "subject": "ICICI: MP Activity", "activity_date": "2025-03-04 11:30:09", "activity_type": "Update", "content_html": "<p>Automated update: MP Activity changed from 'Inactive' to 'Active'</p>", "plain_text": "Automated update: MP Activity changed from 'Inactive' to 'Active'", "author_name": "<PERSON>", "author_email": "<EMAIL>", "flow_type": "Adoption", "touchpoint_reason": "", "external_attendees": "", "company": "ICICI", "original_activity_type": "automated_attribute_change", "original_meeting_type": "No Meeting Type", "source": "ICICI_MIGRATION"}, "error": "CSV UI automation failed", "timestamp": "2025-05-29T23:09:27.793939", "subject": "ICICI: MP Activity"}, {"row_number": "36", "activity": {"row_number": "36", "subject": "ICICI: MP Activity Frequency", "activity_date": "2025-03-04 11:30:09", "activity_type": "Update", "content_html": "<p>Automated update: MP Activity Frequency changed from 'Abandoned' to 'Light Use'</p>", "plain_text": "Automated update: MP Activity Frequency changed from 'Abandoned' to 'Light Use'", "author_name": "<PERSON>", "author_email": "<EMAIL>", "flow_type": "Onboarding", "touchpoint_reason": "", "external_attendees": "", "company": "ICICI", "original_activity_type": "automated_attribute_change", "original_meeting_type": "No Meeting Type", "source": "ICICI_MIGRATION"}, "error": "CSV UI automation failed", "timestamp": "2025-05-29T23:09:30.800026", "subject": "ICICI: MP Activity Frequency"}, {"row_number": "37", "activity": {"row_number": "37", "subject": "ICICI: Mend Platform Access", "activity_date": "2025-03-02 02:30:10", "activity_type": "Update", "content_html": "<p>Automated update: Mend Platform Access changed from 'Access Provided' to 'Access Provided'</p>", "plain_text": "Automated update: Mend Platform Access changed from 'Access Provided' to 'Access Provided'", "author_name": "<PERSON>", "author_email": "<EMAIL>", "flow_type": "Onboarding", "touchpoint_reason": "", "external_attendees": "", "company": "ICICI", "original_activity_type": "automated_attribute_change", "original_meeting_type": "No Meeting Type", "source": "ICICI_MIGRATION"}, "error": "CSV UI automation failed", "timestamp": "2025-05-29T23:09:33.803996", "subject": "ICICI: Mend Platform Access"}, {"row_number": "38", "activity": {"row_number": "38", "subject": "ICICI: MP Activity Frequency", "activity_date": "2025-03-01 07:30:10", "activity_type": "Update", "content_html": "<p>Automated update: MP Activity Frequency changed from 'Consistent Use' to 'Abandoned'</p>", "plain_text": "Automated update: MP Activity Frequency changed from 'Consistent Use' to 'Abandoned'", "author_name": "<PERSON>", "author_email": "<EMAIL>", "flow_type": "Onboarding", "touchpoint_reason": "", "external_attendees": "", "company": "ICICI", "original_activity_type": "automated_attribute_change", "original_meeting_type": "No Meeting Type", "source": "ICICI_MIGRATION"}, "error": "CSV UI automation failed", "timestamp": "2025-05-29T23:09:36.808388", "subject": "ICICI: MP Activity Frequency"}, {"row_number": "39", "activity": {"row_number": "39", "subject": "ICICI: <PERSON> <PERSON><PERSON><PERSON> (7d)", "activity_date": "2025-02-26 07:30:16", "activity_type": "Update", "content_html": "<p>Automated update: MP Vs. Legacy (7d) changed from 'Legacy & Platform' to 'Legacy Only'</p>", "plain_text": "Automated update: MP Vs. Legacy (7d) changed from 'Legacy & Platform' to 'Legacy Only'", "author_name": "<PERSON>", "author_email": "<EMAIL>", "flow_type": "Onboarding", "touchpoint_reason": "", "external_attendees": "", "company": "ICICI", "original_activity_type": "automated_attribute_change", "original_meeting_type": "No Meeting Type", "source": "ICICI_MIGRATION"}, "error": "CSV UI automation failed", "timestamp": "2025-05-29T23:09:39.816741", "subject": "ICICI: <PERSON> <PERSON><PERSON><PERSON> (7d)"}, {"row_number": "40", "activity": {"row_number": "40", "subject": "ICICI: Mend Platform Access", "activity_date": "2025-02-23 02:30:11", "activity_type": "Update", "content_html": "<p>Automated update: Mend Platform Access changed from 'Access Provided' to 'Access Provided'</p>", "plain_text": "Automated update: Mend Platform Access changed from 'Access Provided' to 'Access Provided'", "author_name": "<PERSON>", "author_email": "<EMAIL>", "flow_type": "Onboarding", "touchpoint_reason": "", "external_attendees": "", "company": "ICICI", "original_activity_type": "automated_attribute_change", "original_meeting_type": "No Meeting Type", "source": "ICICI_MIGRATION"}, "error": "CSV UI automation failed", "timestamp": "2025-05-29T23:09:42.821371", "subject": "ICICI: Mend Platform Access"}, {"row_number": "41", "activity": {"row_number": "41", "subject": "ICICI: Active - CN", "activity_date": "2025-02-21 14:52:37", "activity_type": "Update", "content_html": "<p>Automated update: Active - CN changed from '' to 'updated'</p>", "plain_text": "Automated update: Active - CN changed from '' to 'updated'", "author_name": "<PERSON>", "author_email": "<EMAIL>", "flow_type": "Intelligence", "touchpoint_reason": "", "external_attendees": "", "company": "ICICI", "original_activity_type": "automated_attribute_change", "original_meeting_type": "No Meeting Type", "source": "ICICI_MIGRATION"}, "error": "CSV UI automation failed", "timestamp": "2025-05-29T23:09:55.831621", "subject": "ICICI: Active - CN"}, {"row_number": "42", "activity": {"row_number": "42", "subject": "ICICI: Active - SAST", "activity_date": "2025-02-21 14:50:31", "activity_type": "Update", "content_html": "<p>Automated update: Active - SAST changed from '' to 'updated'</p>", "plain_text": "Automated update: Active - SAST changed from '' to 'updated'", "author_name": "<PERSON>", "author_email": "<EMAIL>", "flow_type": "Intelligence", "touchpoint_reason": "", "external_attendees": "", "company": "ICICI", "original_activity_type": "automated_attribute_change", "original_meeting_type": "No Meeting Type", "source": "ICICI_MIGRATION"}, "error": "CSV UI automation failed", "timestamp": "2025-05-29T23:09:58.840166", "subject": "ICICI: Active - SAST"}, {"row_number": "43", "activity": {"row_number": "43", "subject": "ICICI: Active - SCA", "activity_date": "2025-02-21 14:39:58", "activity_type": "Update", "content_html": "<p>Automated update: Active - SCA changed from '' to 'Yes'</p>", "plain_text": "Automated update: Active - SCA changed from '' to 'Yes'", "author_name": "<PERSON>", "author_email": "<EMAIL>", "flow_type": "Intelligence", "touchpoint_reason": "", "external_attendees": "", "company": "ICICI", "original_activity_type": "automated_attribute_change", "original_meeting_type": "No Meeting Type", "source": "ICICI_MIGRATION"}, "error": "CSV UI automation failed", "timestamp": "2025-05-29T23:10:01.842936", "subject": "ICICI: Active - SCA"}, {"row_number": "44", "activity": {"row_number": "44", "subject": "ICICI: Mend Platform Access", "activity_date": "2025-02-20 02:30:11", "activity_type": "Update", "content_html": "<p>Automated update: Mend Platform Access changed from 'Access Provided' to 'Access Provided'</p>", "plain_text": "Automated update: Mend Platform Access changed from 'Access Provided' to 'Access Provided'", "author_name": "<PERSON>", "author_email": "<EMAIL>", "flow_type": "Onboarding", "touchpoint_reason": "", "external_attendees": "", "company": "ICICI", "original_activity_type": "automated_attribute_change", "original_meeting_type": "No Meeting Type", "source": "ICICI_MIGRATION"}, "error": "CSV UI automation failed", "timestamp": "2025-05-29T23:10:04.849667", "subject": "ICICI: Mend Platform Access"}, {"row_number": "45", "activity": {"row_number": "45", "subject": "ICICI: Invicti Campaign", "activity_date": "2025-02-18 18:20:00", "activity_type": "Update", "content_html": "<p>One-time campaign \"Invicti Campaign\" was sent to 3 users. </p>", "plain_text": "One-time campaign \"Invicti Campaign\" was sent to 3 users.", "author_name": "<PERSON>", "author_email": "<EMAIL>", "flow_type": "Adoption", "touchpoint_reason": "", "external_attendees": "", "company": "ICICI", "original_activity_type": "campaign_touch", "original_meeting_type": "No Meeting Type", "source": "ICICI_MIGRATION"}, "error": "CSV UI automation failed", "timestamp": "2025-05-29T23:10:07.856047", "subject": "ICICI: Invicti Campaign"}, {"row_number": "46", "activity": {"row_number": "46", "subject": "ICICI: MP Activity", "activity_date": "2025-02-16 07:30:09", "activity_type": "Update", "content_html": "<p>Automated update: MP Activity changed from 'Active' to 'Abandoned'</p>", "plain_text": "Automated update: MP Activity changed from 'Active' to 'Abandoned'", "author_name": "<PERSON>", "author_email": "<EMAIL>", "flow_type": "Adoption", "touchpoint_reason": "", "external_attendees": "", "company": "ICICI", "original_activity_type": "automated_attribute_change", "original_meeting_type": "No Meeting Type", "source": "ICICI_MIGRATION"}, "error": "CSV UI automation failed", "timestamp": "2025-05-29T23:10:10.862762", "subject": "ICICI: MP Activity"}, {"row_number": "47", "activity": {"row_number": "47", "subject": "ICICI: Mend Platform Access", "activity_date": "2025-02-15 07:30:10", "activity_type": "Update", "content_html": "<p>Automated update: Mend Platform Access changed from 'Access Provided' to 'Access Provided'</p>", "plain_text": "Automated update: Mend Platform Access changed from 'Access Provided' to 'Access Provided'", "author_name": "<PERSON>", "author_email": "<EMAIL>", "flow_type": "Onboarding", "touchpoint_reason": "", "external_attendees": "", "company": "ICICI", "original_activity_type": "automated_attribute_change", "original_meeting_type": "No Meeting Type", "source": "ICICI_MIGRATION"}, "error": "CSV UI automation failed", "timestamp": "2025-05-29T23:10:13.867439", "subject": "ICICI: Mend Platform Access"}, {"row_number": "48", "activity": {"row_number": "48", "subject": "ICICI: MP Abandonment Date", "activity_date": "2025-02-14 10:30:12", "activity_type": "Update", "content_html": "<p>Automated update: MP Abandonment Date changed from '' to ''</p>", "plain_text": "Automated update: MP Abandonment Date changed from '' to ''", "author_name": "<PERSON>", "author_email": "<EMAIL>", "flow_type": "Onboarding", "touchpoint_reason": "", "external_attendees": "", "company": "ICICI", "original_activity_type": "automated_attribute_change", "original_meeting_type": "No Meeting Type", "source": "ICICI_MIGRATION"}, "error": "CSV UI automation failed", "timestamp": "2025-05-29T23:10:16.871640", "subject": "ICICI: MP Abandonment Date"}, {"row_number": "49", "activity": {"row_number": "49", "subject": "ICICI: MP Activity", "activity_date": "2025-02-14 09:30:09", "activity_type": "Update", "content_html": "<p>Automated update: MP Activity changed from 'Inactive' to 'Active'</p>", "plain_text": "Automated update: MP Activity changed from 'Inactive' to 'Active'", "author_name": "<PERSON>", "author_email": "<EMAIL>", "flow_type": "Adoption", "touchpoint_reason": "", "external_attendees": "", "company": "ICICI", "original_activity_type": "automated_attribute_change", "original_meeting_type": "No Meeting Type", "source": "ICICI_MIGRATION"}, "error": "CSV UI automation failed", "timestamp": "2025-05-29T23:10:19.879211", "subject": "ICICI: MP Activity"}, {"row_number": "50", "activity": {"row_number": "50", "subject": "ICICI: MP Activity", "activity_date": "2025-02-13 07:30:10", "activity_type": "Update", "content_html": "<p>Automated update: MP Activity changed from 'Active' to 'Abandoned'</p>", "plain_text": "Automated update: MP Activity changed from 'Active' to 'Abandoned'", "author_name": "<PERSON>", "author_email": "<EMAIL>", "flow_type": "Adoption", "touchpoint_reason": "", "external_attendees": "", "company": "ICICI", "original_activity_type": "automated_attribute_change", "original_meeting_type": "No Meeting Type", "source": "ICICI_MIGRATION"}, "error": "CSV UI automation failed", "timestamp": "2025-05-29T23:10:22.885456", "subject": "ICICI: MP Activity"}, {"row_number": "51", "activity": {"row_number": "51", "subject": "ICICI: February Newsletter: AI-Powered Code Remediation and More!", "activity_date": "2025-02-10 20:20:00", "activity_type": "Update", "content_html": "<p>One-time campaign \"February Newsletter: AI-Powered Code Remediation and More!\" was sent to 3 users. </p>", "plain_text": "One-time campaign \"February Newsletter: AI-Powered Code Remediation and More!\" was sent to 3 users.", "author_name": "<PERSON>", "author_email": "<EMAIL>", "flow_type": "Adoption", "touchpoint_reason": "", "external_attendees": "", "company": "ICICI", "original_activity_type": "campaign_touch", "original_meeting_type": "No Meeting Type", "source": "ICICI_MIGRATION"}, "error": "CSV UI automation failed", "timestamp": "2025-05-29T23:10:35.895876", "subject": "ICICI: February Newsletter: AI-Powered Code Remediation and More!"}, {"row_number": "52", "activity": {"row_number": "52", "subject": "ICICI: MP Abandonment Date", "activity_date": "2025-02-10 09:30:12", "activity_type": "Update", "content_html": "<p>Automated update: MP Abandonment Date changed from '' to ''</p>", "plain_text": "Automated update: MP Abandonment Date changed from '' to ''", "author_name": "<PERSON>", "author_email": "<EMAIL>", "flow_type": "Onboarding", "touchpoint_reason": "", "external_attendees": "", "company": "ICICI", "original_activity_type": "automated_attribute_change", "original_meeting_type": "No Meeting Type", "source": "ICICI_MIGRATION"}, "error": "CSV UI automation failed", "timestamp": "2025-05-29T23:10:38.906860", "subject": "ICICI: MP Abandonment Date"}, {"row_number": "53", "activity": {"row_number": "53", "subject": "ICICI: MP Activity", "activity_date": "2025-02-10 08:30:08", "activity_type": "Update", "content_html": "<p>Automated update: MP Activity changed from 'Inactive' to 'Active'</p>", "plain_text": "Automated update: MP Activity changed from 'Inactive' to 'Active'", "author_name": "<PERSON>", "author_email": "<EMAIL>", "flow_type": "Adoption", "touchpoint_reason": "", "external_attendees": "", "company": "ICICI", "original_activity_type": "automated_attribute_change", "original_meeting_type": "No Meeting Type", "source": "ICICI_MIGRATION"}, "error": "CSV UI automation failed", "timestamp": "2025-05-29T23:10:41.916870", "subject": "ICICI: MP Activity"}, {"row_number": "54", "activity": {"row_number": "54", "subject": "ICICI: Mend Platform Access", "activity_date": "2025-02-08 02:30:10", "activity_type": "Update", "content_html": "<p>Automated update: Mend Platform Access changed from 'Access Provided' to 'Access Provided'</p>", "plain_text": "Automated update: Mend Platform Access changed from 'Access Provided' to 'Access Provided'", "author_name": "<PERSON>", "author_email": "<EMAIL>", "flow_type": "Onboarding", "touchpoint_reason": "", "external_attendees": "", "company": "ICICI", "original_activity_type": "automated_attribute_change", "original_meeting_type": "No Meeting Type", "source": "ICICI_MIGRATION"}, "error": "CSV UI automation failed", "timestamp": "2025-05-29T23:10:44.923735", "subject": "ICICI: Mend Platform Access"}, {"row_number": "55", "activity": {"row_number": "55", "subject": "ICICI: MP Activity", "activity_date": "2025-02-06 07:30:12", "activity_type": "Update", "content_html": "<p>Automated update: MP Activity changed from 'Active' to 'Abandoned'</p>", "plain_text": "Automated update: MP Activity changed from 'Active' to 'Abandoned'", "author_name": "<PERSON>", "author_email": "<EMAIL>", "flow_type": "Adoption", "touchpoint_reason": "", "external_attendees": "", "company": "ICICI", "original_activity_type": "automated_attribute_change", "original_meeting_type": "No Meeting Type", "source": "ICICI_MIGRATION"}, "error": "CSV UI automation failed", "timestamp": "2025-05-29T23:10:47.933264", "subject": "ICICI: MP Activity"}, {"row_number": "56", "activity": {"row_number": "56", "subject": "ICICI: Mend Platform Access", "activity_date": "2025-02-04 08:30:13", "activity_type": "Update", "content_html": "<p>Automated update: Mend Platform Access changed from 'Access Provided' to 'Access Provided'</p>", "plain_text": "Automated update: Mend Platform Access changed from 'Access Provided' to 'Access Provided'", "author_name": "<PERSON>", "author_email": "<EMAIL>", "flow_type": "Onboarding", "touchpoint_reason": "", "external_attendees": "", "company": "ICICI", "original_activity_type": "automated_attribute_change", "original_meeting_type": "No Meeting Type", "source": "ICICI_MIGRATION"}, "error": "CSV UI automation failed", "timestamp": "2025-05-29T23:10:50.939501", "subject": "ICICI: Mend Platform Access"}, {"row_number": "57", "activity": {"row_number": "57", "subject": "ICICI: MP Abandonment Date", "activity_date": "2025-02-03 10:30:12", "activity_type": "Update", "content_html": "<p>Automated update: MP Abandonment Date changed from '' to ''</p>", "plain_text": "Automated update: MP Abandonment Date changed from '' to ''", "author_name": "<PERSON>", "author_email": "<EMAIL>", "flow_type": "Onboarding", "touchpoint_reason": "", "external_attendees": "", "company": "ICICI", "original_activity_type": "automated_attribute_change", "original_meeting_type": "No Meeting Type", "source": "ICICI_MIGRATION"}, "error": "CSV UI automation failed", "timestamp": "2025-05-29T23:10:53.945672", "subject": "ICICI: MP Abandonment Date"}, {"row_number": "58", "activity": {"row_number": "58", "subject": "ICICI: MP Activity", "activity_date": "2025-02-03 09:30:10", "activity_type": "Update", "content_html": "<p>Automated update: MP Activity changed from 'Inactive' to 'Active'</p>", "plain_text": "Automated update: MP Activity changed from 'Inactive' to 'Active'", "author_name": "<PERSON>", "author_email": "<EMAIL>", "flow_type": "Adoption", "touchpoint_reason": "", "external_attendees": "", "company": "ICICI", "original_activity_type": "automated_attribute_change", "original_meeting_type": "No Meeting Type", "source": "ICICI_MIGRATION"}, "error": "CSV UI automation failed", "timestamp": "2025-05-29T23:10:56.958168", "subject": "ICICI: MP Activity"}, {"row_number": "59", "activity": {"row_number": "59", "subject": "ICICI: MP Activity", "activity_date": "2025-02-03 08:30:10", "activity_type": "Update", "content_html": "<p>Automated update: MP Activity changed from 'Active' to 'Abandoned'</p>", "plain_text": "Automated update: MP Activity changed from 'Active' to 'Abandoned'", "author_name": "<PERSON>", "author_email": "<EMAIL>", "flow_type": "Adoption", "touchpoint_reason": "", "external_attendees": "", "company": "ICICI", "original_activity_type": "automated_attribute_change", "original_meeting_type": "No Meeting Type", "source": "ICICI_MIGRATION"}, "error": "CSV UI automation failed", "timestamp": "2025-05-29T23:10:59.966642", "subject": "ICICI: MP Activity"}, {"row_number": "60", "activity": {"row_number": "60", "subject": "ICICI: MP Activity Frequency", "activity_date": "2025-02-03 02:30:10", "activity_type": "Update", "content_html": "<p>Automated update: MP Activity Frequency changed from 'Light Use' to 'Moderate Usage'</p>", "plain_text": "Automated update: MP Activity Frequency changed from 'Light Use' to 'Moderate Usage'", "author_name": "<PERSON>", "author_email": "<EMAIL>", "flow_type": "Onboarding", "touchpoint_reason": "", "external_attendees": "", "company": "ICICI", "original_activity_type": "automated_attribute_change", "original_meeting_type": "No Meeting Type", "source": "ICICI_MIGRATION"}, "error": "CSV UI automation failed", "timestamp": "2025-05-29T23:11:02.972528", "subject": "ICICI: MP Activity Frequency"}, {"row_number": "61", "activity": {"row_number": "61", "subject": "ICICI: SAST Planned Downtime_App_copy", "activity_date": "2025-01-29 14:20:09", "activity_type": "Update", "content_html": "<p>One-time campaign \"SAST Planned Downtime_App_copy\" was sent to 3 users. </p>", "plain_text": "One-time campaign \"SAST Planned Downtime_App_copy\" was sent to 3 users.", "author_name": "<PERSON>", "author_email": "<EMAIL>", "flow_type": "Adoption", "touchpoint_reason": "", "external_attendees": "", "company": "ICICI", "original_activity_type": "campaign_touch", "original_meeting_type": "No Meeting Type", "source": "ICICI_MIGRATION"}, "error": "CSV UI automation failed", "timestamp": "2025-05-29T23:11:15.991121", "subject": "ICICI: SAST Planned Downtime_App_copy"}, {"row_number": "62", "activity": {"row_number": "62", "subject": "ICICI: Mend Platform Access", "activity_date": "2025-01-28 07:30:14", "activity_type": "Update", "content_html": "<p>Automated update: Mend Platform Access changed from 'Access Provided' to 'Access Provided'</p>", "plain_text": "Automated update: Mend Platform Access changed from 'Access Provided' to 'Access Provided'", "author_name": "<PERSON>", "author_email": "<EMAIL>", "flow_type": "Onboarding", "touchpoint_reason": "", "external_attendees": "", "company": "ICICI", "original_activity_type": "automated_attribute_change", "original_meeting_type": "No Meeting Type", "source": "ICICI_MIGRATION"}, "error": "CSV UI automation failed", "timestamp": "2025-05-29T23:11:19.000088", "subject": "ICICI: Mend Platform Access"}, {"row_number": "63", "activity": {"row_number": "63", "subject": "ICICI: MP Abandonment Date", "activity_date": "2025-01-27 13:30:12", "activity_type": "Update", "content_html": "<p>Automated update: MP Abandonment Date changed from '' to ''</p>", "plain_text": "Automated update: MP Abandonment Date changed from '' to ''", "author_name": "<PERSON>", "author_email": "<EMAIL>", "flow_type": "Onboarding", "touchpoint_reason": "", "external_attendees": "", "company": "ICICI", "original_activity_type": "automated_attribute_change", "original_meeting_type": "No Meeting Type", "source": "ICICI_MIGRATION"}, "error": "CSV UI automation failed", "timestamp": "2025-05-29T23:11:22.013118", "subject": "ICICI: MP Abandonment Date"}, {"row_number": "64", "activity": {"row_number": "64", "subject": "ICICI: MP Activity", "activity_date": "2025-01-27 12:30:11", "activity_type": "Update", "content_html": "<p>Automated update: MP Activity changed from 'Inactive' to 'Active'</p>", "plain_text": "Automated update: MP Activity changed from 'Inactive' to 'Active'", "author_name": "<PERSON>", "author_email": "<EMAIL>", "flow_type": "Adoption", "touchpoint_reason": "", "external_attendees": "", "company": "ICICI", "original_activity_type": "automated_attribute_change", "original_meeting_type": "No Meeting Type", "source": "ICICI_MIGRATION"}, "error": "CSV UI automation failed", "timestamp": "2025-05-29T23:20:42.617678", "subject": "ICICI: MP Activity"}, {"row_number": "65", "activity": {"row_number": "65", "subject": "ICICI: MP Activity", "activity_date": "2025-01-25 07:30:12", "activity_type": "Update", "content_html": "<p>Automated update: MP Activity changed from 'Active' to 'Abandoned'</p>", "plain_text": "Automated update: MP Activity changed from 'Active' to 'Abandoned'", "author_name": "<PERSON>", "author_email": "<EMAIL>", "flow_type": "Adoption", "touchpoint_reason": "", "external_attendees": "", "company": "ICICI", "original_activity_type": "automated_attribute_change", "original_meeting_type": "No Meeting Type", "source": "ICICI_MIGRATION"}, "error": "CSV UI automation failed", "timestamp": "2025-05-29T23:20:45.632695", "subject": "ICICI: MP Activity"}, {"row_number": "66", "activity": {"row_number": "66", "subject": "ICICI: MP Abandonment Date", "activity_date": "2025-01-23 14:30:13", "activity_type": "Update", "content_html": "<p>Automated update: MP Abandonment Date changed from '' to ''</p>", "plain_text": "Automated update: MP Abandonment Date changed from '' to ''", "author_name": "<PERSON>", "author_email": "<EMAIL>", "flow_type": "Onboarding", "touchpoint_reason": "", "external_attendees": "", "company": "ICICI", "original_activity_type": "automated_attribute_change", "original_meeting_type": "No Meeting Type", "source": "ICICI_MIGRATION"}, "error": "CSV UI automation failed", "timestamp": "2025-05-29T23:20:48.902267", "subject": "ICICI: MP Abandonment Date"}, {"row_number": "67", "activity": {"row_number": "67", "subject": "ICICI: MP Activity", "activity_date": "2025-01-23 13:30:14", "activity_type": "Update", "content_html": "<p>Automated update: MP Activity changed from 'Inactive' to 'Active'</p>", "plain_text": "Automated update: MP Activity changed from 'Inactive' to 'Active'", "author_name": "<PERSON>", "author_email": "<EMAIL>", "flow_type": "Adoption", "touchpoint_reason": "", "external_attendees": "", "company": "ICICI", "original_activity_type": "automated_attribute_change", "original_meeting_type": "No Meeting Type", "source": "ICICI_MIGRATION"}, "error": "CSV UI automation failed", "timestamp": "2025-05-29T23:20:51.936058", "subject": "ICICI: MP Activity"}, {"row_number": "68", "activity": {"row_number": "68", "subject": "ICICI: AI Design Partners", "activity_date": "2025-01-22 20:09:43", "activity_type": "Update", "content_html": "<p>Automated update: AI Design Partners changed from '' to 'Not Started'</p>", "plain_text": "Automated update: AI Design Partners changed from '' to 'Not Started'", "author_name": "<PERSON>", "author_email": "<EMAIL>", "flow_type": "Upsell", "touchpoint_reason": "", "external_attendees": "", "company": "ICICI", "original_activity_type": "automated_attribute_change", "original_meeting_type": "No Meeting Type", "source": "ICICI_MIGRATION"}, "error": "CSV UI automation failed", "timestamp": "2025-05-29T23:20:54.947299", "subject": "ICICI: AI Design Partners"}, {"row_number": "69", "activity": {"row_number": "69", "subject": "ICICI: Mend Platform Access", "activity_date": "2025-01-22 02:30:14", "activity_type": "Update", "content_html": "<p>Automated update: Mend Platform Access changed from 'Access Provided' to 'Access Provided'</p>", "plain_text": "Automated update: Mend Platform Access changed from 'Access Provided' to 'Access Provided'", "author_name": "<PERSON>", "author_email": "<EMAIL>", "flow_type": "Onboarding", "touchpoint_reason": "", "external_attendees": "", "company": "ICICI", "original_activity_type": "automated_attribute_change", "original_meeting_type": "No Meeting Type", "source": "ICICI_MIGRATION"}, "error": "CSV UI automation failed", "timestamp": "2025-05-29T23:20:57.959158", "subject": "ICICI: Mend Platform Access"}, {"row_number": "70", "activity": {"row_number": "70", "subject": "ICICI: MP Activity", "activity_date": "2025-01-17 08:30:15", "activity_type": "Update", "content_html": "<p>Automated update: MP Activity changed from 'Active' to 'Abandoned'</p>", "plain_text": "Automated update: MP Activity changed from 'Active' to 'Abandoned'", "author_name": "<PERSON>", "author_email": "<EMAIL>", "flow_type": "Adoption", "touchpoint_reason": "", "external_attendees": "", "company": "ICICI", "original_activity_type": "automated_attribute_change", "original_meeting_type": "No Meeting Type", "source": "ICICI_MIGRATION"}, "error": "CSV UI automation failed", "timestamp": "2025-05-29T23:21:00.972309", "subject": "ICICI: MP Activity"}, {"row_number": "71", "activity": {"row_number": "71", "subject": "ICICI: <PERSON> <PERSON><PERSON><PERSON> (14d)", "activity_date": "2025-01-16 07:30:15", "activity_type": "Update", "content_html": "<p>Automated update: MP Vs. Legacy (14d) changed from 'Legacy Only' to 'Legacy & Platform'</p>", "plain_text": "Automated update: MP Vs. Legacy (14d) changed from 'Legacy Only' to 'Legacy & Platform'", "author_name": "<PERSON>", "author_email": "<EMAIL>", "flow_type": "Onboarding", "touchpoint_reason": "", "external_attendees": "", "company": "ICICI", "original_activity_type": "automated_attribute_change", "original_meeting_type": "No Meeting Type", "source": "ICICI_MIGRATION"}, "error": "CSV UI automation failed", "timestamp": "2025-05-29T23:35:25.826075", "subject": "ICICI: <PERSON> <PERSON><PERSON><PERSON> (14d)"}, {"row_number": "72", "activity": {"row_number": "72", "subject": "ICICI: Mend Platform Access", "activity_date": "2025-01-16 07:30:10", "activity_type": "Update", "content_html": "<p>Automated update: Mend Platform Access changed from 'Access Provided' to 'Access Provided'</p>", "plain_text": "Automated update: Mend Platform Access changed from 'Access Provided' to 'Access Provided'", "author_name": "<PERSON>", "author_email": "<EMAIL>", "flow_type": "Onboarding", "touchpoint_reason": "", "external_attendees": "", "company": "ICICI", "original_activity_type": "automated_attribute_change", "original_meeting_type": "No Meeting Type", "source": "ICICI_MIGRATION"}, "error": "CSV UI automation failed", "timestamp": "2025-05-29T23:49:44.349160", "subject": "ICICI: Mend Platform Access"}, {"row_number": "73", "activity": {"row_number": "73", "subject": "ICICI: MP Abandonment Date", "activity_date": "2025-01-15 09:30:09", "activity_type": "Update", "content_html": "<p>Automated update: MP Abandonment Date changed from '' to ''</p>", "plain_text": "Automated update: MP Abandonment Date changed from '' to ''", "author_name": "<PERSON>", "author_email": "<EMAIL>", "flow_type": "Onboarding", "touchpoint_reason": "", "external_attendees": "", "company": "ICICI", "original_activity_type": "automated_attribute_change", "original_meeting_type": "No Meeting Type", "source": "ICICI_MIGRATION"}, "error": "CSV UI automation failed", "timestamp": "2025-05-29T23:49:47.382711", "subject": "ICICI: MP Abandonment Date"}, {"row_number": "74", "activity": {"row_number": "74", "subject": "ICICI: MP Activity", "activity_date": "2025-01-15 08:30:10", "activity_type": "Update", "content_html": "<p>Automated update: MP Activity changed from 'Inactive' to 'Active'</p>", "plain_text": "Automated update: MP Activity changed from 'Inactive' to 'Active'", "author_name": "<PERSON>", "author_email": "<EMAIL>", "flow_type": "Adoption", "touchpoint_reason": "", "external_attendees": "", "company": "ICICI", "original_activity_type": "automated_attribute_change", "original_meeting_type": "No Meeting Type", "source": "ICICI_MIGRATION"}, "error": "CSV UI automation failed", "timestamp": "2025-05-29T23:49:50.901138", "subject": "ICICI: MP Activity"}, {"row_number": "75", "activity": {"row_number": "75", "subject": "ICICI: MP Activity", "activity_date": "2025-01-15 07:30:13", "activity_type": "Update", "content_html": "<p>Automated update: MP Activity changed from 'Active' to 'Abandoned'</p>", "plain_text": "Automated update: MP Activity changed from 'Active' to 'Abandoned'", "author_name": "<PERSON>", "author_email": "<EMAIL>", "flow_type": "Adoption", "touchpoint_reason": "", "external_attendees": "", "company": "ICICI", "original_activity_type": "automated_attribute_change", "original_meeting_type": "No Meeting Type", "source": "ICICI_MIGRATION"}, "error": "CSV UI automation failed", "timestamp": "2025-05-29T23:49:53.918725", "subject": "ICICI: MP Activity"}, {"row_number": "76", "activity": {"row_number": "76", "subject": "ICICI: Mend Platform Access", "activity_date": "2025-01-14 07:30:10", "activity_type": "Update", "content_html": "<p>Automated update: Mend Platform Access changed from 'Access Provided' to 'Access Provided'</p>", "plain_text": "Automated update: Mend Platform Access changed from 'Access Provided' to 'Access Provided'", "author_name": "<PERSON>", "author_email": "<EMAIL>", "flow_type": "Onboarding", "touchpoint_reason": "", "external_attendees": "", "company": "ICICI", "original_activity_type": "automated_attribute_change", "original_meeting_type": "No Meeting Type", "source": "ICICI_MIGRATION"}, "error": "CSV UI automation failed", "timestamp": "2025-05-29T23:49:56.924546", "subject": "ICICI: Mend Platform Access"}, {"row_number": "77", "activity": {"row_number": "77", "subject": "ICICI: MP Abandonment Date", "activity_date": "2025-01-13 10:30:12", "activity_type": "Update", "content_html": "<p>Automated update: MP Abandonment Date changed from '' to ''</p>", "plain_text": "Automated update: MP Abandonment Date changed from '' to ''", "author_name": "<PERSON>", "author_email": "<EMAIL>", "flow_type": "Onboarding", "touchpoint_reason": "", "external_attendees": "", "company": "ICICI", "original_activity_type": "automated_attribute_change", "original_meeting_type": "No Meeting Type", "source": "ICICI_MIGRATION"}, "error": "CSV UI automation failed", "timestamp": "2025-05-30T00:04:13.557826", "subject": "ICICI: MP Abandonment Date"}, {"row_number": "78", "activity": {"row_number": "78", "subject": "ICICI: MP Activity", "activity_date": "2025-01-13 09:30:13", "activity_type": "Update", "content_html": "<p>Automated update: MP Activity changed from 'Inactive' to 'Active'</p>", "plain_text": "Automated update: MP Activity changed from 'Inactive' to 'Active'", "author_name": "<PERSON>", "author_email": "<EMAIL>", "flow_type": "Adoption", "touchpoint_reason": "", "external_attendees": "", "company": "ICICI", "original_activity_type": "automated_attribute_change", "original_meeting_type": "No Meeting Type", "source": "ICICI_MIGRATION"}, "error": "CSV UI automation failed", "timestamp": "2025-05-30T00:04:16.575470", "subject": "ICICI: MP Activity"}, {"row_number": "79", "activity": {"row_number": "79", "subject": "ICICI: MP Activity Frequency", "activity_date": "2025-01-11 08:30:12", "activity_type": "Update", "content_html": "<p>Automated update: MP Activity Frequency changed from 'First Experience' to 'Light Use'</p>", "plain_text": "Automated update: MP Activity Frequency changed from 'First Experience' to 'Light Use'", "author_name": "<PERSON>", "author_email": "<EMAIL>", "flow_type": "Onboarding", "touchpoint_reason": "", "external_attendees": "", "company": "ICICI", "original_activity_type": "automated_attribute_change", "original_meeting_type": "No Meeting Type", "source": "ICICI_MIGRATION"}, "error": "CSV UI automation failed", "timestamp": "2025-05-30T00:04:19.581119", "subject": "ICICI: MP Activity Frequency"}, {"row_number": "80", "activity": {"row_number": "80", "subject": "ICICI: MP Activity", "activity_date": "2025-01-11 08:30:12", "activity_type": "Update", "content_html": "<p>Automated update: MP Activity changed from 'Active' to 'Abandoned'</p>", "plain_text": "Automated update: MP Activity changed from 'Active' to 'Abandoned'", "author_name": "<PERSON>", "author_email": "<EMAIL>", "flow_type": "Adoption", "touchpoint_reason": "", "external_attendees": "", "company": "ICICI", "original_activity_type": "automated_attribute_change", "original_meeting_type": "No Meeting Type", "source": "ICICI_MIGRATION"}, "error": "CSV UI automation failed", "timestamp": "2025-05-30T00:21:40.137138", "subject": "ICICI: MP Activity"}, {"row_number": "81", "activity": {"row_number": "81", "subject": "ICICI: January Newsletter:  Keep Your Code Secure, Exciting Updates from Mend.io!", "activity_date": "2025-01-10 14:40:00", "activity_type": "Update", "content_html": "<p>One-time campaign \"January Newsletter:  Keep Your Code Secure, Exciting Updates from Mend.io!\" was sent to 3 users. </p>", "plain_text": "One-time campaign \"January Newsletter:  Keep Your Code Secure, Exciting Updates from Mend.io!\" was sent to 3 users.", "author_name": "<PERSON>", "author_email": "<EMAIL>", "flow_type": "Adoption", "touchpoint_reason": "", "external_attendees": "", "company": "ICICI", "original_activity_type": "campaign_touch", "original_meeting_type": "No Meeting Type", "source": "ICICI_MIGRATION"}, "error": "CSV UI automation failed", "timestamp": "2025-05-30T00:54:15.884646", "subject": "ICICI: January Newsletter:  Keep Your Code Secure, Exciting Updates from Mend.io!"}, {"row_number": "82", "activity": {"row_number": "82", "subject": "ICICI: Mend Platform Access", "activity_date": "2025-01-08 08:30:10", "activity_type": "Update", "content_html": "<p>Automated update: Mend Platform Access changed from 'Access Provided' to 'Access Provided'</p>", "plain_text": "Automated update: Mend Platform Access changed from 'Access Provided' to 'Access Provided'", "author_name": "<PERSON>", "author_email": "<EMAIL>", "flow_type": "Onboarding", "touchpoint_reason": "", "external_attendees": "", "company": "ICICI", "original_activity_type": "automated_attribute_change", "original_meeting_type": "No Meeting Type", "source": "ICICI_MIGRATION"}, "error": "CSV UI automation failed", "timestamp": "2025-05-30T00:54:18.899089", "subject": "ICICI: Mend Platform Access"}, {"row_number": "83", "activity": {"row_number": "83", "subject": "ICICI: MP Abandonment Date", "activity_date": "2025-01-07 09:30:13", "activity_type": "Update", "content_html": "<p>Automated update: MP Abandonment Date changed from '' to ''</p>", "plain_text": "Automated update: MP Abandonment Date changed from '' to ''", "author_name": "<PERSON>", "author_email": "<EMAIL>", "flow_type": "Onboarding", "touchpoint_reason": "", "external_attendees": "", "company": "ICICI", "original_activity_type": "automated_attribute_change", "original_meeting_type": "No Meeting Type", "source": "ICICI_MIGRATION"}, "error": "CSV UI automation failed", "timestamp": "2025-05-30T00:54:22.065485", "subject": "ICICI: MP Abandonment Date"}, {"row_number": "84", "activity": {"row_number": "84", "subject": "ICICI: MP Activity", "activity_date": "2025-01-07 08:30:12", "activity_type": "Update", "content_html": "<p>Automated update: MP Activity changed from 'Inactive' to 'Active'</p>", "plain_text": "Automated update: MP Activity changed from 'Inactive' to 'Active'", "author_name": "<PERSON>", "author_email": "<EMAIL>", "flow_type": "Adoption", "touchpoint_reason": "", "external_attendees": "", "company": "ICICI", "original_activity_type": "automated_attribute_change", "original_meeting_type": "No Meeting Type", "source": "ICICI_MIGRATION"}, "error": "CSV UI automation failed", "timestamp": "2025-05-30T00:54:25.256814", "subject": "ICICI: MP Activity"}, {"row_number": "85", "activity": {"row_number": "85", "subject": "ICICI: MP Activity", "activity_date": "2025-01-04 07:30:13", "activity_type": "Update", "content_html": "<p>Automated update: MP Activity changed from 'Active' to 'Abandoned'</p>", "plain_text": "Automated update: MP Activity changed from 'Active' to 'Abandoned'", "author_name": "<PERSON>", "author_email": "<EMAIL>", "flow_type": "Adoption", "touchpoint_reason": "", "external_attendees": "", "company": "ICICI", "original_activity_type": "automated_attribute_change", "original_meeting_type": "No Meeting Type", "source": "ICICI_MIGRATION"}, "error": "CSV UI automation failed", "timestamp": "2025-05-30T00:54:28.281732", "subject": "ICICI: MP Activity"}, {"row_number": "86", "activity": {"row_number": "86", "subject": "ICICI: Mend Platform Access", "activity_date": "2025-01-04 02:30:10", "activity_type": "Update", "content_html": "<p>Automated update: Mend Platform Access changed from 'Access Provided' to 'Access Provided'</p>", "plain_text": "Automated update: Mend Platform Access changed from 'Access Provided' to 'Access Provided'", "author_name": "<PERSON>", "author_email": "<EMAIL>", "flow_type": "Onboarding", "touchpoint_reason": "", "external_attendees": "", "company": "ICICI", "original_activity_type": "automated_attribute_change", "original_meeting_type": "No Meeting Type", "source": "ICICI_MIGRATION"}, "error": "CSV UI automation failed", "timestamp": "2025-05-30T01:08:44.870986", "subject": "ICICI: Mend Platform Access"}, {"row_number": "87", "activity": {"row_number": "87", "subject": "ICICI: MP Activity Frequency", "activity_date": "2025-01-03 08:30:14", "activity_type": "Update", "content_html": "<p>Automated update: MP Activity Frequency changed from 'Light Use' to 'First Use'</p>", "plain_text": "Automated update: MP Activity Frequency changed from 'Light Use' to 'First Use'", "author_name": "<PERSON>", "author_email": "<EMAIL>", "flow_type": "Adoption", "touchpoint_reason": "", "external_attendees": "", "company": "ICICI", "original_activity_type": "automated_attribute_change", "original_meeting_type": "No Meeting Type", "source": "ICICI_MIGRATION"}, "error": "CSV UI automation failed", "timestamp": "2025-05-30T01:08:47.897672", "subject": "ICICI: MP Activity Frequency"}, {"row_number": "88", "activity": {"row_number": "88", "subject": "ICICI: MP Abandonment Date", "activity_date": "2025-01-03 08:30:14", "activity_type": "Update", "content_html": "<p>Automated update: MP Abandonment Date changed from '' to ''</p>", "plain_text": "Automated update: MP Abandonment Date changed from '' to ''", "author_name": "<PERSON>", "author_email": "<EMAIL>", "flow_type": "Onboarding", "touchpoint_reason": "", "external_attendees": "", "company": "ICICI", "original_activity_type": "automated_attribute_change", "original_meeting_type": "No Meeting Type", "source": "ICICI_MIGRATION"}, "error": "CSV UI automation failed", "timestamp": "2025-05-30T01:08:51.358682", "subject": "ICICI: MP Abandonment Date"}, {"row_number": "89", "activity": {"row_number": "89", "subject": "ICICI: MP Activity", "activity_date": "2025-01-03 07:30:11", "activity_type": "Update", "content_html": "<p>Automated update: MP Activity changed from 'Never Used' to 'Active'</p>", "plain_text": "Automated update: MP Activity changed from 'Never Used' to 'Active'", "author_name": "<PERSON>", "author_email": "<EMAIL>", "flow_type": "Adoption", "touchpoint_reason": "", "external_attendees": "", "company": "ICICI", "original_activity_type": "automated_attribute_change", "original_meeting_type": "No Meeting Type", "source": "ICICI_MIGRATION"}, "error": "CSV UI automation failed", "timestamp": "2025-05-30T01:08:54.362798", "subject": "ICICI: MP Activity"}, {"row_number": "90", "activity": {"row_number": "90", "subject": "ICICI: <PERSON><PERSON><PERSON> to Delighted", "activity_date": "2025-01-01 05:30:13", "activity_type": "Update", "content_html": "<p>Webhook 'Zapier to Delighted' was success</p>", "plain_text": "Webhook 'Zapier to Delighted' was success", "author_name": "<PERSON>", "author_email": "<EMAIL>", "flow_type": "Adoption", "touchpoint_reason": "", "external_attendees": "", "company": "ICICI", "original_activity_type": "webhook", "original_meeting_type": "No Meeting Type", "source": "ICICI_MIGRATION"}, "error": "CSV UI automation failed", "timestamp": "2025-05-30T01:08:57.371673", "subject": "ICICI: <PERSON><PERSON><PERSON> to Delighted"}, {"row_number": "91", "activity": {"row_number": "91", "subject": "ICICI: <PERSON><PERSON><PERSON> to Delighted", "activity_date": "2025-01-01 05:30:13", "activity_type": "Update", "content_html": "<p>Webhook 'Zapier to Delighted' was success</p>", "plain_text": "Webhook 'Zapier to Delighted' was success", "author_name": "<PERSON>", "author_email": "<EMAIL>", "flow_type": "Adoption", "touchpoint_reason": "", "external_attendees": "", "company": "ICICI", "original_activity_type": "webhook", "original_meeting_type": "No Meeting Type", "source": "ICICI_MIGRATION"}, "error": "CSV UI automation failed", "timestamp": "2025-05-30T01:12:24.619597", "subject": "ICICI: <PERSON><PERSON><PERSON> to Delighted"}, {"row_number": "92", "activity": {"row_number": "92", "subject": "ICICI: NPS Send Date", "activity_date": "2025-01-01 05:30:10", "activity_type": "Update", "content_html": "<p>Automated update: NPS Send Date changed from '2024-12-31' to '2025-06-30T05:30:09.644Z'</p>", "plain_text": "Automated update: NPS Send Date changed from '2024-12-31' to '2025-06-30T05:30:09.644Z'", "author_name": "<PERSON>", "author_email": "<EMAIL>", "flow_type": "Adoption", "touchpoint_reason": "", "external_attendees": "", "company": "ICICI", "original_activity_type": "automated_attribute_change", "original_meeting_type": "No Meeting Type", "source": "ICICI_MIGRATION"}, "error": "CSV UI automation failed", "timestamp": "2025-05-30T01:12:27.704170", "subject": "ICICI: NPS Send Date"}, {"row_number": "93", "activity": {"row_number": "93", "subject": "ICICI: NPS Last Sent", "activity_date": "2025-01-01 05:30:09", "activity_type": "Update", "content_html": "<p>Automated update: NPS Last Sent changed from '2024-07-04' to '2025-01-01T05:30:09.644Z'</p>", "plain_text": "Automated update: NPS Last Sent changed from '2024-07-04' to '2025-01-01T05:30:09.644Z'", "author_name": "<PERSON>", "author_email": "<EMAIL>", "flow_type": "Adoption", "touchpoint_reason": "", "external_attendees": "", "company": "ICICI", "original_activity_type": "automated_attribute_change", "original_meeting_type": "No Meeting Type", "source": "ICICI_MIGRATION"}, "error": "CSV UI automation failed", "timestamp": "2025-05-30T01:26:48.190913", "subject": "ICICI: NPS Last Sent"}, {"row_number": "94", "activity": {"row_number": "94", "subject": "ICICI: NPS Send Date", "activity_date": "2025-01-01 05:30:09", "activity_type": "Update", "content_html": "<p>Automated update: NPS Send Date changed from '2024-12-31' to '2025-06-30T05:30:09.633Z'</p>", "plain_text": "Automated update: NPS Send Date changed from '2024-12-31' to '2025-06-30T05:30:09.633Z'", "author_name": "<PERSON>", "author_email": "<EMAIL>", "flow_type": "Adoption", "touchpoint_reason": "", "external_attendees": "", "company": "ICICI", "original_activity_type": "automated_attribute_change", "original_meeting_type": "No Meeting Type", "source": "ICICI_MIGRATION"}, "error": "CSV UI automation failed", "timestamp": "2025-05-30T01:26:51.214677", "subject": "ICICI: NPS Send Date"}, {"row_number": "95", "activity": {"row_number": "95", "subject": "ICICI: NPS Last Sent", "activity_date": "2025-01-01 05:30:09", "activity_type": "Update", "content_html": "<p>Automated update: NPS Last Sent changed from '2024-07-04' to '2025-01-01T05:30:09.633Z'</p>", "plain_text": "Automated update: NPS Last Sent changed from '2024-07-04' to '2025-01-01T05:30:09.633Z'", "author_name": "<PERSON>", "author_email": "<EMAIL>", "flow_type": "Adoption", "touchpoint_reason": "", "external_attendees": "", "company": "ICICI", "original_activity_type": "automated_attribute_change", "original_meeting_type": "No Meeting Type", "source": "ICICI_MIGRATION"}, "error": "CSV UI automation failed", "timestamp": "2025-05-30T01:26:54.386600", "subject": "ICICI: NPS Last Sent"}, {"row_number": "96", "activity": {"row_number": "96", "subject": "ICICI: Mend Platform Access", "activity_date": "2025-01-01 02:30:10", "activity_type": "Update", "content_html": "<p>Automated update: Mend Platform Access changed from 'Access Provided' to 'Access Provided'</p>", "plain_text": "Automated update: Mend Platform Access changed from 'Access Provided' to 'Access Provided'", "author_name": "<PERSON>", "author_email": "<EMAIL>", "flow_type": "Onboarding", "touchpoint_reason": "", "external_attendees": "", "company": "ICICI", "original_activity_type": "automated_attribute_change", "original_meeting_type": "No Meeting Type", "source": "ICICI_MIGRATION"}, "error": "CSV UI automation failed", "timestamp": "2025-05-30T01:26:57.432795", "subject": "ICICI: Mend Platform Access"}, {"row_number": "97", "activity": {"row_number": "97", "subject": "ICICI: Mend Platform Access", "activity_date": "2024-12-30 02:30:11", "activity_type": "Update", "content_html": "<p>Automated update: Mend Platform Access changed from 'Access Provided' to 'Access Provided'</p>", "plain_text": "Automated update: Mend Platform Access changed from 'Access Provided' to 'Access Provided'", "author_name": "<PERSON>", "author_email": "<EMAIL>", "flow_type": "Onboarding", "touchpoint_reason": "", "external_attendees": "", "company": "ICICI", "original_activity_type": "automated_attribute_change", "original_meeting_type": "No Meeting Type", "source": "ICICI_MIGRATION"}, "error": "CSV UI automation failed", "timestamp": "2025-05-30T01:27:00.445705", "subject": "ICICI: Mend Platform Access"}, {"row_number": "98", "activity": {"row_number": "98", "subject": "ICICI: Risk Status", "activity_date": "2024-12-29 05:30:13", "activity_type": "Update", "content_html": "<p>Automated update: Risk Status changed from '' to 'Watch'</p>", "plain_text": "Automated update: Risk Status changed from '' to 'Watch'", "author_name": "<PERSON>", "author_email": "<EMAIL>", "flow_type": "Risk", "touchpoint_reason": "", "external_attendees": "", "company": "ICICI", "original_activity_type": "automated_attribute_change", "original_meeting_type": "No Meeting Type", "source": "ICICI_MIGRATION"}, "error": "CSV UI automation failed", "timestamp": "2025-05-30T01:27:03.453251", "subject": "ICICI: Risk Status"}, {"row_number": "99", "activity": {"row_number": "99", "subject": "ICICI: Risk Next Step(s)", "activity_date": "2024-12-29 05:30:13", "activity_type": "Update", "content_html": "<p>Automated update: Risk Next Step(s) changed from '[Auto] - CSM to engage customer and Investigate risk' to '[Auto] - CSM to engage customer and Investigate risk '</p>", "plain_text": "Automated update: Risk Next Step(s) changed from '[Auto] - CSM to engage customer and Investigate risk' to '[Auto] - CSM to engage customer and Investigate risk '", "author_name": "<PERSON>", "author_email": "<EMAIL>", "flow_type": "Risk", "touchpoint_reason": "", "external_attendees": "", "company": "ICICI", "original_activity_type": "automated_attribute_change", "original_meeting_type": "No Meeting Type", "source": "ICICI_MIGRATION"}, "error": "CSV UI automation failed", "timestamp": "2025-05-30T01:27:06.469176", "subject": "ICICI: Risk Next Step(s)"}, {"row_number": "100", "activity": {"row_number": "100", "subject": "ICICI: Primary Risk Reason", "activity_date": "2024-12-29 05:30:13", "activity_type": "Update", "content_html": "<p>Automated update: Primary Risk Reason changed from '[Auto] - No reach out to customer in over 31 days' to '[Auto] - No reach out to customer in over 31 days'</p>", "plain_text": "Automated update: Primary Risk Reason changed from '[Auto] - No reach out to customer in over 31 days' to '[Auto] - No reach out to customer in over 31 days'", "author_name": "<PERSON>", "author_email": "<EMAIL>", "flow_type": "Risk", "touchpoint_reason": "", "external_attendees": "", "company": "ICICI", "original_activity_type": "automated_attribute_change", "original_meeting_type": "No Meeting Type", "source": "ICICI_MIGRATION"}, "error": "CSV UI automation failed", "timestamp": "2025-05-30T01:27:09.482741", "subject": "ICICI: Primary Risk Reason"}, {"row_number": "101", "activity": {"row_number": "101", "subject": "ICICI: Mend Platform Access", "activity_date": "2024-12-22 02:30:12", "activity_type": "Update", "content_html": "<p>Automated update: Mend Platform Access changed from 'Access Provided' to 'Access Provided'</p>", "plain_text": "Automated update: Mend Platform Access changed from 'Access Provided' to 'Access Provided'", "author_name": "<PERSON>", "author_email": "<EMAIL>", "flow_type": "Onboarding", "touchpoint_reason": "", "external_attendees": "", "company": "ICICI", "original_activity_type": "automated_attribute_change", "original_meeting_type": "No Meeting Type", "source": "ICICI_MIGRATION"}, "error": "CSV UI automation failed", "timestamp": "2025-05-30T01:55:51.116706", "subject": "ICICI: Mend Platform Access"}, {"row_number": "102", "activity": {"row_number": "102", "subject": "ICICI: <div><br>Upcoming NPS Survey&nbsp;</div>", "activity_date": "2024-12-17 05:30:11", "activity_type": "Update", "content_html": "<p><div>An NPS Survey Send Date is approaching in 14 days for a Key Contact at <span class=\"mention-wrapper\" data-reactroot=\"\"><span class=\"field\">ICICI Bank</span><br></span></div></p>", "plain_text": "An NPS Survey Send Date is approaching in 14 days for a Key Contact at ICICI Bank", "author_name": "<PERSON>", "author_email": "<EMAIL>", "flow_type": "Adoption", "touchpoint_reason": "", "external_attendees": "", "company": "ICICI", "original_activity_type": "account_alert", "original_meeting_type": "No Meeting Type", "source": "ICICI_MIGRATION"}, "error": "CSV UI automation failed", "timestamp": "2025-05-30T01:55:54.121192", "subject": "ICICI: <div><br>Upcoming NPS Survey&nbsp;</div>"}, {"row_number": "103", "activity": {"row_number": "103", "subject": "ICICI: Mend Platform Access", "activity_date": "2024-12-17 02:30:11", "activity_type": "Update", "content_html": "<p>Automated update: Mend Platform Access changed from 'Access Provided' to 'Access Provided'</p>", "plain_text": "Automated update: Mend Platform Access changed from 'Access Provided' to 'Access Provided'", "author_name": "<PERSON>", "author_email": "<EMAIL>", "flow_type": "Onboarding", "touchpoint_reason": "", "external_attendees": "", "company": "ICICI", "original_activity_type": "automated_attribute_change", "original_meeting_type": "No Meeting Type", "source": "ICICI_MIGRATION"}, "error": "CSV UI automation failed", "timestamp": "2025-05-30T01:55:57.254290", "subject": "ICICI: Mend Platform Access"}, {"row_number": "104", "activity": {"row_number": "104", "subject": "ICICI: Mend Platform Access", "activity_date": "2024-12-14 02:30:14", "activity_type": "Update", "content_html": "<p>Automated update: Mend Platform Access changed from 'Access Provided' to 'Access Provided'</p>", "plain_text": "Automated update: Mend Platform Access changed from 'Access Provided' to 'Access Provided'", "author_name": "<PERSON>", "author_email": "<EMAIL>", "flow_type": "Onboarding", "touchpoint_reason": "", "external_attendees": "", "company": "ICICI", "original_activity_type": "automated_attribute_change", "original_meeting_type": "No Meeting Type", "source": "ICICI_MIGRATION"}, "error": "CSV UI automation failed", "timestamp": "2025-05-30T01:56:00.308829", "subject": "ICICI: Mend Platform Access"}, {"row_number": "105", "activity": {"row_number": "105", "subject": "ICICI: Risk KPI", "activity_date": "2024-12-12 21:36:49", "activity_type": "Update", "content_html": "<p>Automated update: Risk KPI changed from '' to '0'</p>", "plain_text": "Automated update: Risk KPI changed from '' to '0'", "author_name": "<PERSON>", "author_email": "<EMAIL>", "flow_type": "Risk", "touchpoint_reason": "", "external_attendees": "", "company": "ICICI", "original_activity_type": "automated_attribute_change", "original_meeting_type": "No Meeting Type", "source": "ICICI_MIGRATION"}, "error": "CSV UI automation failed", "timestamp": "2025-05-30T02:10:21.111794", "subject": "ICICI: Risk KPI"}, {"row_number": "106", "activity": {"row_number": "106", "subject": "ICICI: <PERSON> <PERSON> <PERSON><PERSON><PERSON>", "activity_date": "2024-12-12 18:00:00", "activity_type": "Update", "content_html": "<p>One-time campaign \"Springs - HeroDev\" was sent to 3 users. </p>", "plain_text": "One-time campaign \"Springs - HeroDev\" was sent to 3 users.", "author_name": "<PERSON>", "author_email": "<EMAIL>", "flow_type": "Adoption", "touchpoint_reason": "", "external_attendees": "", "company": "ICICI", "original_activity_type": "campaign_touch", "original_meeting_type": "No Meeting Type", "source": "ICICI_MIGRATION"}, "error": "CSV UI automation failed", "timestamp": "2025-05-30T02:10:24.117937", "subject": "ICICI: <PERSON> <PERSON> <PERSON><PERSON><PERSON>"}, {"row_number": "107", "activity": {"row_number": "107", "subject": "ICICI: Mend Platform Access", "activity_date": "2024-12-10 02:30:14", "activity_type": "Update", "content_html": "<p>Automated update: Mend Platform Access changed from 'Access Provided' to 'Access Provided'</p>", "plain_text": "Automated update: Mend Platform Access changed from 'Access Provided' to 'Access Provided'", "author_name": "<PERSON>", "author_email": "<EMAIL>", "flow_type": "Onboarding", "touchpoint_reason": "", "external_attendees": "", "company": "ICICI", "original_activity_type": "automated_attribute_change", "original_meeting_type": "No Meeting Type", "source": "ICICI_MIGRATION"}, "error": "CSV UI automation failed", "timestamp": "2025-05-30T02:10:27.964991", "subject": "ICICI: Mend Platform Access"}, {"row_number": "108", "activity": {"row_number": "108", "subject": "ICICI: December Newsletter: Sharper Risk Insights & Updates", "activity_date": "2024-12-09 14:40:00", "activity_type": "Update", "content_html": "<p>One-time campaign \"December Newsletter: Sharper Risk Insights & Updates\" was sent to 3 users. </p>", "plain_text": "One-time campaign \"December Newsletter: Sharper Risk Insights & Updates\" was sent to 3 users.", "author_name": "<PERSON>", "author_email": "<EMAIL>", "flow_type": "Adoption", "touchpoint_reason": "", "external_attendees": "", "company": "ICICI", "original_activity_type": "campaign_touch", "original_meeting_type": "No Meeting Type", "source": "ICICI_MIGRATION"}, "error": "CSV UI automation failed", "timestamp": "2025-05-30T02:10:31.007285", "subject": "ICICI: December Newsletter: Sharper Risk Insights & Updates"}, {"row_number": "109", "activity": {"row_number": "109", "subject": "ICICI: Mend Platform Access", "activity_date": "2024-12-08 02:30:11", "activity_type": "Update", "content_html": "<p>Automated update: Mend Platform Access changed from 'Access Provided' to 'Access Provided'</p>", "plain_text": "Automated update: Mend Platform Access changed from 'Access Provided' to 'Access Provided'", "author_name": "<PERSON>", "author_email": "<EMAIL>", "flow_type": "Onboarding", "touchpoint_reason": "", "external_attendees": "", "company": "ICICI", "original_activity_type": "automated_attribute_change", "original_meeting_type": "No Meeting Type", "source": "ICICI_MIGRATION"}, "error": "CSV UI automation failed", "timestamp": "2025-05-30T02:13:19.274697", "subject": "ICICI: Mend Platform Access"}, {"row_number": "110", "activity": {"row_number": "110", "subject": "ICICI: Solana: MSC Critical Security Event", "activity_date": "2024-12-05 18:20:00", "activity_type": "Update", "content_html": "<p>One-time campaign \"Solana: MSC Critical Security Event\" was sent to 3 users. </p>", "plain_text": "One-time campaign \"Solana: MSC Critical Security Event\" was sent to 3 users.", "author_name": "<PERSON>", "author_email": "<EMAIL>", "flow_type": "Adoption", "touchpoint_reason": "", "external_attendees": "", "company": "ICICI", "original_activity_type": "campaign_touch", "original_meeting_type": "No Meeting Type", "source": "ICICI_MIGRATION"}, "error": "CSV UI automation failed", "timestamp": "2025-05-30T02:13:22.293187", "subject": "ICICI: Solana: MSC Critical Security Event"}, {"row_number": "111", "activity": {"row_number": "111", "subject": "ICICI: Mend Platform Access", "activity_date": "2024-12-04 02:30:11", "activity_type": "Update", "content_html": "<p>Automated update: Mend Platform Access changed from 'Access Provided' to 'Access Provided'</p>", "plain_text": "Automated update: Mend Platform Access changed from 'Access Provided' to 'Access Provided'", "author_name": "<PERSON>", "author_email": "<EMAIL>", "flow_type": "Onboarding", "touchpoint_reason": "", "external_attendees": "", "company": "ICICI", "original_activity_type": "automated_attribute_change", "original_meeting_type": "No Meeting Type", "source": "ICICI_MIGRATION"}, "error": "CSV UI automation failed", "timestamp": "2025-05-30T02:13:35.306637", "subject": "ICICI: Mend Platform Access"}, {"row_number": "112", "activity": {"row_number": "112", "subject": "ICICI: Mend Platform Access", "activity_date": "2024-12-01 02:30:12", "activity_type": "Update", "content_html": "<p>Automated update: Mend Platform Access changed from 'Access Provided' to 'Access Provided'</p>", "plain_text": "Automated update: Mend Platform Access changed from 'Access Provided' to 'Access Provided'", "author_name": "<PERSON>", "author_email": "<EMAIL>", "flow_type": "Onboarding", "touchpoint_reason": "", "external_attendees": "", "company": "ICICI", "original_activity_type": "automated_attribute_change", "original_meeting_type": "No Meeting Type", "source": "ICICI_MIGRATION"}, "error": "CSV UI automation failed", "timestamp": "2025-05-30T02:27:51.985975", "subject": "ICICI: Mend Platform Access"}, {"row_number": "113", "activity": {"row_number": "113", "subject": "ICICI: Mend Platform Access", "activity_date": "2024-11-29 02:30:14", "activity_type": "Update", "content_html": "<p>Automated update: Mend Platform Access changed from 'Access Provided' to 'Access Provided'</p>", "plain_text": "Automated update: Mend Platform Access changed from 'Access Provided' to 'Access Provided'", "author_name": "<PERSON>", "author_email": "<EMAIL>", "flow_type": "Onboarding", "touchpoint_reason": "", "external_attendees": "", "company": "ICICI", "original_activity_type": "automated_attribute_change", "original_meeting_type": "No Meeting Type", "source": "ICICI_MIGRATION"}, "error": "CSV UI automation failed", "timestamp": "2025-05-30T02:27:54.998073", "subject": "ICICI: Mend Platform Access"}, {"row_number": "114", "activity": {"row_number": "114", "subject": "ICICI: SAST Planned Downtime_copy", "activity_date": "2024-11-25 21:20:00", "activity_type": "Update", "content_html": "<p>One-time campaign \"SAST Planned Downtime_copy\" was sent to 3 users. </p>", "plain_text": "One-time campaign \"SAST Planned Downtime_copy\" was sent to 3 users.", "author_name": "<PERSON>", "author_email": "<EMAIL>", "flow_type": "Adoption", "touchpoint_reason": "", "external_attendees": "", "company": "ICICI", "original_activity_type": "campaign_touch", "original_meeting_type": "No Meeting Type", "source": "ICICI_MIGRATION"}, "error": "CSV UI automation failed", "timestamp": "2025-05-30T02:27:58.003802", "subject": "ICICI: SAST Planned Downtime_copy"}, {"row_number": "115", "activity": {"row_number": "115", "subject": "ICICI: Mend Platform Access", "activity_date": "2024-11-23 02:30:13", "activity_type": "Update", "content_html": "<p>Automated update: Mend Platform Access changed from 'Access Provided' to 'Access Provided'</p>", "plain_text": "Automated update: Mend Platform Access changed from 'Access Provided' to 'Access Provided'", "author_name": "<PERSON>", "author_email": "<EMAIL>", "flow_type": "Onboarding", "touchpoint_reason": "", "external_attendees": "", "company": "ICICI", "original_activity_type": "automated_attribute_change", "original_meeting_type": "No Meeting Type", "source": "ICICI_MIGRATION"}, "error": "CSV UI automation failed", "timestamp": "2025-05-30T02:28:01.011324", "subject": "ICICI: Mend Platform Access"}, {"row_number": "116", "activity": {"row_number": "116", "subject": "ICICI: Mend Platform Access", "activity_date": "2024-11-19 02:30:15", "activity_type": "Update", "content_html": "<p>Automated update: Mend Platform Access changed from 'Access Provided' to 'Access Provided'</p>", "plain_text": "Automated update: Mend Platform Access changed from 'Access Provided' to 'Access Provided'", "author_name": "<PERSON>", "author_email": "<EMAIL>", "flow_type": "Onboarding", "touchpoint_reason": "", "external_attendees": "", "company": "ICICI", "original_activity_type": "automated_attribute_change", "original_meeting_type": "No Meeting Type", "source": "ICICI_MIGRATION"}, "error": "CSV UI automation failed", "timestamp": "2025-05-30T02:45:47.479223", "subject": "ICICI: Mend Platform Access"}, {"row_number": "117", "activity": {"row_number": "117", "subject": "ICICI: Mend Platform Access", "activity_date": "2024-11-17 02:30:12", "activity_type": "Update", "content_html": "<p>Automated update: Mend Platform Access changed from 'Access Provided' to 'Access Provided'</p>", "plain_text": "Automated update: Mend Platform Access changed from 'Access Provided' to 'Access Provided'", "author_name": "<PERSON>", "author_email": "<EMAIL>", "flow_type": "Onboarding", "touchpoint_reason": "", "external_attendees": "", "company": "ICICI", "original_activity_type": "automated_attribute_change", "original_meeting_type": "No Meeting Type", "source": "ICICI_MIGRATION"}, "error": "CSV UI automation failed", "timestamp": "2025-05-30T02:45:50.483977", "subject": "ICICI: Mend Platform Access"}, {"row_number": "118", "activity": {"row_number": "118", "subject": "ICICI: SAST Planned Downtime", "activity_date": "2024-11-11 22:20:00", "activity_type": "Update", "content_html": "<p>One-time campaign \"SAST Planned Downtime\" was sent to 3 users. </p>", "plain_text": "One-time campaign \"SAST Planned Downtime\" was sent to 3 users.", "author_name": "<PERSON>", "author_email": "<EMAIL>", "flow_type": "Adoption", "touchpoint_reason": "", "external_attendees": "", "company": "ICICI", "original_activity_type": "campaign_touch", "original_meeting_type": "No Meeting Type", "source": "ICICI_MIGRATION"}, "error": "CSV UI automation failed", "timestamp": "2025-05-30T03:03:00.264537", "subject": "ICICI: SAST Planned Downtime"}, {"row_number": "119", "activity": {"row_number": "119", "subject": "ICICI: Mend Platform Access", "activity_date": "2024-11-11 02:30:10", "activity_type": "Update", "content_html": "<p>Automated update: Mend Platform Access changed from 'Access Provided' to 'Access Provided'</p>", "plain_text": "Automated update: Mend Platform Access changed from 'Access Provided' to 'Access Provided'", "author_name": "<PERSON>", "author_email": "<EMAIL>", "flow_type": "Onboarding", "touchpoint_reason": "", "external_attendees": "", "company": "ICICI", "original_activity_type": "automated_attribute_change", "original_meeting_type": "No Meeting Type", "source": "ICICI_MIGRATION"}, "error": "CSV UI automation failed", "timestamp": "2025-05-30T03:14:20.100591", "subject": "ICICI: Mend Platform Access"}, {"row_number": "120", "activity": {"row_number": "120", "subject": "ICICI: <PERSON><PERSON><PERSON> to Delighted", "activity_date": "2024-11-10 05:30:26", "activity_type": "Update", "content_html": "<p>Webhook 'Zapier to Delighted' was success</p>", "plain_text": "Webhook 'Zapier to Delighted' was success", "author_name": "<PERSON>", "author_email": "<EMAIL>", "flow_type": "Adoption", "touchpoint_reason": "", "external_attendees": "", "company": "ICICI", "original_activity_type": "webhook", "original_meeting_type": "No Meeting Type", "source": "ICICI_MIGRATION"}, "error": "CSV UI automation failed", "timestamp": "2025-05-30T03:14:23.114673", "subject": "ICICI: <PERSON><PERSON><PERSON> to Delighted"}, {"row_number": "121", "activity": {"row_number": "121", "subject": "ICICI: NPS Send Date", "activity_date": "2024-11-10 05:30:10", "activity_type": "Update", "content_html": "<p>Automated update: NPS Send Date changed from '2024-11-09' to '2025-05-09T05:30:10.379Z'</p>", "plain_text": "Automated update: NPS Send Date changed from '2024-11-09' to '2025-05-09T05:30:10.379Z'", "author_name": "<PERSON>", "author_email": "<EMAIL>", "flow_type": "Adoption", "touchpoint_reason": "", "external_attendees": "", "company": "ICICI", "original_activity_type": "automated_attribute_change", "original_meeting_type": "No Meeting Type", "source": "ICICI_MIGRATION"}, "error": "CSV UI automation failed", "timestamp": "2025-05-30T03:14:37.227518", "subject": "ICICI: NPS Send Date"}, {"row_number": "122", "activity": {"row_number": "122", "subject": "ICICI: NPS Last Sent", "activity_date": "2024-11-10 05:30:10", "activity_type": "Update", "content_html": "<p>Automated update: NPS Last Sent changed from '' to '2024-11-10T05:30:10.379Z'</p>", "plain_text": "Automated update: NPS Last Sent changed from '' to '2024-11-10T05:30:10.379Z'", "author_name": "<PERSON>", "author_email": "<EMAIL>", "flow_type": "Adoption", "touchpoint_reason": "", "external_attendees": "", "company": "ICICI", "original_activity_type": "automated_attribute_change", "original_meeting_type": "No Meeting Type", "source": "ICICI_MIGRATION"}, "error": "CSV UI automation failed", "timestamp": "2025-05-30T03:14:40.233910", "subject": "ICICI: NPS Last Sent"}, {"row_number": "123", "activity": {"row_number": "123", "subject": "ICICI: Mend Platform Access", "activity_date": "2024-11-08 02:30:14", "activity_type": "Update", "content_html": "<p>Automated update: Mend Platform Access changed from 'Access Provided' to 'Access Provided'</p>", "plain_text": "Automated update: Mend Platform Access changed from 'Access Provided' to 'Access Provided'", "author_name": "<PERSON>", "author_email": "<EMAIL>", "flow_type": "Onboarding", "touchpoint_reason": "", "external_attendees": "", "company": "ICICI", "original_activity_type": "automated_attribute_change", "original_meeting_type": "No Meeting Type", "source": "ICICI_MIGRATION"}, "error": "CSV UI automation failed", "timestamp": "2025-05-30T03:14:43.249855", "subject": "ICICI: Mend Platform Access"}, {"row_number": "124", "activity": {"row_number": "124", "subject": "ICICI: November newsletter 2024", "activity_date": "2024-11-07 15:40:04", "activity_type": "Update", "content_html": "<p>One-time campaign \"November newsletter 2024\" was sent to 3 users. </p>", "plain_text": "One-time campaign \"November newsletter 2024\" was sent to 3 users.", "author_name": "<PERSON>", "author_email": "<EMAIL>", "flow_type": "Adoption", "touchpoint_reason": "", "external_attendees": "", "company": "ICICI", "original_activity_type": "campaign_touch", "original_meeting_type": "No Meeting Type", "source": "ICICI_MIGRATION"}, "error": "CSV UI automation failed", "timestamp": "2025-05-30T03:14:46.259416", "subject": "ICICI: November newsletter 2024"}, {"row_number": "125", "activity": {"row_number": "125", "subject": "ICICI: Mend Platform Access", "activity_date": "2024-10-31 02:30:11", "activity_type": "Update", "content_html": "<p>Automated update: Mend Platform Access changed from 'Access Provided' to 'Access Provided'</p>", "plain_text": "Automated update: Mend Platform Access changed from 'Access Provided' to 'Access Provided'", "author_name": "<PERSON>", "author_email": "<EMAIL>", "flow_type": "Onboarding", "touchpoint_reason": "", "external_attendees": "", "company": "ICICI", "original_activity_type": "automated_attribute_change", "original_meeting_type": "No Meeting Type", "source": "ICICI_MIGRATION"}, "error": "CSV UI automation failed", "timestamp": "2025-05-30T03:28:56.449566", "subject": "ICICI: Mend Platform Access"}, {"row_number": "126", "activity": {"row_number": "126", "subject": "ICICI: <PERSON> <PERSON><PERSON><PERSON> (7d)", "activity_date": "2024-10-30 00:17:52", "activity_type": "Update", "content_html": "<p>Automated update: MP Vs. Legacy (7d) changed from '' to 'Legacy Only'</p>", "plain_text": "Automated update: MP Vs. Legacy (7d) changed from '' to 'Legacy Only'", "author_name": "<PERSON>", "author_email": "<EMAIL>", "flow_type": "Onboarding", "touchpoint_reason": "", "external_attendees": "", "company": "ICICI", "original_activity_type": "automated_attribute_change", "original_meeting_type": "No Meeting Type", "source": "ICICI_MIGRATION"}, "error": "CSV UI automation failed", "timestamp": "2025-05-30T03:28:59.460811", "subject": "ICICI: <PERSON> <PERSON><PERSON><PERSON> (7d)"}, {"row_number": "127", "activity": {"row_number": "127", "subject": "ICICI: Mend Platform Access", "activity_date": "2024-10-29 02:30:11", "activity_type": "Update", "content_html": "<p>Automated update: Mend Platform Access changed from 'Access Provided' to 'Access Provided'</p>", "plain_text": "Automated update: Mend Platform Access changed from 'Access Provided' to 'Access Provided'", "author_name": "<PERSON>", "author_email": "<EMAIL>", "flow_type": "Onboarding", "touchpoint_reason": "", "external_attendees": "", "company": "ICICI", "original_activity_type": "automated_attribute_change", "original_meeting_type": "No Meeting Type", "source": "ICICI_MIGRATION"}, "error": "CSV UI automation failed", "timestamp": "2025-05-30T03:29:02.483548", "subject": "ICICI: Mend Platform Access"}, {"row_number": "128", "activity": {"row_number": "128", "subject": "ICICI: <div><br>Upcoming NPS Survey&nbsp;</div>", "activity_date": "2024-10-26 06:30:13", "activity_type": "Update", "content_html": "<p><div>An NPS Survey Send Date is approaching in 14 days for a Display Contact at <span class=\"mention-wrapper\" data-reactroot=\"\"><span class=\"field\">ICICI Bank</span><br></span></div></p>", "plain_text": "An NPS Survey Send Date is approaching in 14 days for a Display Contact at ICICI Bank", "author_name": "<PERSON>", "author_email": "<EMAIL>", "flow_type": "Adoption", "touchpoint_reason": "", "external_attendees": "", "company": "ICICI", "original_activity_type": "account_alert", "original_meeting_type": "No Meeting Type", "source": "ICICI_MIGRATION"}, "error": "CSV UI automation failed", "timestamp": "2025-05-30T03:29:05.489703", "subject": "ICICI: <div><br>Upcoming NPS Survey&nbsp;</div>"}, {"row_number": "129", "activity": {"row_number": "129", "subject": "ICICI: October newsletter 2024", "activity_date": "2024-10-08 16:20:00", "activity_type": "Update", "content_html": "<p>One-time campaign \"October newsletter 2024\" was sent to 3 users. </p>", "plain_text": "One-time campaign \"October newsletter 2024\" was sent to 3 users.", "author_name": "<PERSON>", "author_email": "<EMAIL>", "flow_type": "Adoption", "touchpoint_reason": "", "external_attendees": "", "company": "ICICI", "original_activity_type": "campaign_touch", "original_meeting_type": "No Meeting Type", "source": "ICICI_MIGRATION"}, "error": "CSV UI automation failed", "timestamp": "2025-05-30T03:29:08.508382", "subject": "ICICI: October newsletter 2024"}, {"row_number": "130", "activity": {"row_number": "130", "subject": "ICICI: Risk Status", "activity_date": "2024-10-07 10:30:12", "activity_type": "Update", "content_html": "<p>Automated update: Risk Status changed from '' to 'Watch'</p>", "plain_text": "Automated update: Risk Status changed from '' to 'Watch'", "author_name": "<PERSON>", "author_email": "<EMAIL>", "flow_type": "Risk", "touchpoint_reason": "", "external_attendees": "", "company": "ICICI", "original_activity_type": "automated_attribute_change", "original_meeting_type": "No Meeting Type", "source": "ICICI_MIGRATION"}, "error": "CSV UI automation failed", "timestamp": "2025-05-30T03:29:11.524373", "subject": "ICICI: Risk Status"}, {"row_number": "131", "activity": {"row_number": "131", "subject": "ICICI: Primary Risk Reason", "activity_date": "2024-10-07 10:30:12", "activity_type": "Update", "content_html": "<p>Automated update: Primary Risk Reason changed from '[Auto] - No reach out to customer in over 31 days' to '[Auto] - No reach out to customer in over 31 days'</p>", "plain_text": "Automated update: Primary Risk Reason changed from '[Auto] - No reach out to customer in over 31 days' to '[Auto] - No reach out to customer in over 31 days'", "author_name": "<PERSON>", "author_email": "<EMAIL>", "flow_type": "Risk", "touchpoint_reason": "", "external_attendees": "", "company": "ICICI", "original_activity_type": "automated_attribute_change", "original_meeting_type": "No Meeting Type", "source": "ICICI_MIGRATION"}, "error": "CSV UI automation failed", "timestamp": "2025-05-30T03:59:49.153753", "subject": "ICICI: Primary Risk Reason"}, {"row_number": "132", "activity": {"row_number": "132", "subject": "ICICI: Risk Next Step(s)", "activity_date": "2024-10-07 10:30:12", "activity_type": "Update", "content_html": "<p>Automated update: Risk Next Step(s) changed from '[Auto] - CSM to engage customer and Investigate risk' to '[Auto] - CSM to engage customer and Investigate risk '</p>", "plain_text": "Automated update: Risk Next Step(s) changed from '[Auto] - CSM to engage customer and Investigate risk' to '[Auto] - CSM to engage customer and Investigate risk '", "author_name": "<PERSON>", "author_email": "<EMAIL>", "flow_type": "Risk", "touchpoint_reason": "", "external_attendees": "", "company": "ICICI", "original_activity_type": "automated_attribute_change", "original_meeting_type": "No Meeting Type", "source": "ICICI_MIGRATION"}, "error": "CSV UI automation failed", "timestamp": "2025-05-30T03:59:52.169248", "subject": "ICICI: Risk Next Step(s)"}, {"row_number": "133", "activity": {"row_number": "133", "subject": "ICICI: Risk Status", "activity_date": "2024-10-05 06:30:10", "activity_type": "Update", "content_html": "<p>Automated update: Risk Status changed from '' to 'Watch'</p>", "plain_text": "Automated update: Risk Status changed from '' to 'Watch'", "author_name": "<PERSON>", "author_email": "<EMAIL>", "flow_type": "Risk", "touchpoint_reason": "", "external_attendees": "", "company": "ICICI", "original_activity_type": "automated_attribute_change", "original_meeting_type": "No Meeting Type", "source": "ICICI_MIGRATION"}, "error": "CSV UI automation failed", "timestamp": "2025-05-30T04:15:21.939104", "subject": "ICICI: Risk Status"}, {"row_number": "134", "activity": {"row_number": "134", "subject": "ICICI: Primary Risk Reason", "activity_date": "2024-10-05 06:30:10", "activity_type": "Update", "content_html": "<p>Automated update: Primary Risk Reason changed from 'OS Aug28th # Offlince scan customer- \"Offline scan option is mandate for ICICI Bank team and without this it would be too difficult and will be a blocker for future as well.\"' to '[Auto] - No reach out to customer in over 31 days'</p>", "plain_text": "Automated update: Primary Risk Reason changed from 'OS Aug28th # Offlince scan customer- \"Offline scan option is mandate for ICICI Bank team and without this it would be too difficult and will be a blocker for future as well.\"' to '[Auto] - No reach out to customer in over 31 days'", "author_name": "<PERSON>", "author_email": "<EMAIL>", "flow_type": "Risk", "touchpoint_reason": "", "external_attendees": "", "company": "ICICI", "original_activity_type": "automated_attribute_change", "original_meeting_type": "No Meeting Type", "source": "ICICI_MIGRATION"}, "error": "CSV UI automation failed", "timestamp": "2025-05-30T04:15:24.947960", "subject": "ICICI: Primary Risk Reason"}, {"row_number": "135", "activity": {"row_number": "135", "subject": "ICICI: Risk Next Step(s)", "activity_date": "2024-10-05 06:30:10", "activity_type": "Update", "content_html": "<p>Automated update: Risk Next Step(s) changed from 'OS Aug28th# Oksana to check with <PERSON> what are our approach for Offline scan and if this will be added to the CLI and MP' to '[Auto] - CSM to engage customer and Investigate risk '</p>", "plain_text": "Automated update: Risk Next Step(s) changed from 'OS Aug28th# Oksana to check with <PERSON> what are our approach for Offline scan and if this will be added to the CLI and MP' to '[Auto] - CSM to engage customer and Investigate risk '", "author_name": "<PERSON>", "author_email": "<EMAIL>", "flow_type": "Risk", "touchpoint_reason": "", "external_attendees": "", "company": "ICICI", "original_activity_type": "automated_attribute_change", "original_meeting_type": "No Meeting Type", "source": "ICICI_MIGRATION"}, "error": "CSV UI automation failed", "timestamp": "2025-05-30T04:15:27.961343", "subject": "ICICI: Risk Next Step(s)"}, {"row_number": "136", "activity": {"row_number": "136", "subject": "ICICI: Support: IP Address Change", "activity_date": "2024-09-12 20:40:00", "activity_type": "Update", "content_html": "<p>One-time campaign \"Support: IP Address Change\" was sent to 3 users. </p>", "plain_text": "One-time campaign \"Support: IP Address Change\" was sent to 3 users.", "author_name": "<PERSON>", "author_email": "<EMAIL>", "flow_type": "Adoption", "touchpoint_reason": "", "external_attendees": "", "company": "ICICI", "original_activity_type": "campaign_touch", "original_meeting_type": "No Meeting Type", "source": "ICICI_MIGRATION"}, "error": "CSV UI automation failed", "timestamp": "2025-05-30T04:15:31.568573", "subject": "ICICI: Support: IP Address Change"}, {"row_number": "137", "activity": {"row_number": "137", "subject": "ICICI: Business Model Launch", "activity_date": "2024-09-09 10:30:15", "activity_type": "Update", "content_html": "<p>Automated update: Business Model Launch changed from 'Bundle Enabled' to 'Bundle Enabled'</p>", "plain_text": "Automated update: Business Model Launch changed from 'Bundle Enabled' to 'Bundle Enabled'", "author_name": "<PERSON>", "author_email": "<EMAIL>", "flow_type": "Onboarding", "touchpoint_reason": "", "external_attendees": "", "company": "ICICI", "original_activity_type": "automated_attribute_change", "original_meeting_type": "No Meeting Type", "source": "ICICI_MIGRATION"}, "error": "CSV UI automation failed", "timestamp": "2025-05-30T04:29:55.540624", "subject": "ICICI: Business Model Launch"}, {"row_number": "138", "activity": {"row_number": "138", "subject": "ICICI: New Business Model_Webinar Follow up", "activity_date": "2024-09-06 16:20:03", "activity_type": "Update", "content_html": "<p>One-time campaign \"New Business Model_Webinar Follow up\" was sent to 3 users. </p>", "plain_text": "One-time campaign \"New Business Model_Webinar Follow up\" was sent to 3 users.", "author_name": "<PERSON>", "author_email": "<EMAIL>", "flow_type": "Adoption", "touchpoint_reason": "", "external_attendees": "", "company": "ICICI", "original_activity_type": "campaign_touch", "original_meeting_type": "No Meeting Type", "source": "ICICI_MIGRATION"}, "error": "CSV UI automation failed", "timestamp": "2025-05-30T04:29:58.599947", "subject": "ICICI: New Business Model_Webinar Follow up"}, {"row_number": "139", "activity": {"row_number": "139", "subject": "ICICI: MP Activity Frequency", "activity_date": "2024-09-05 08:30:13", "activity_type": "Update", "content_html": "<p>Automated update: MP Activity Frequency changed from 'First Experience' to 'Light Use'</p>", "plain_text": "Automated update: MP Activity Frequency changed from 'First Experience' to 'Light Use'", "author_name": "<PERSON>", "author_email": "<EMAIL>", "flow_type": "Onboarding", "touchpoint_reason": "", "external_attendees": "", "company": "ICICI", "original_activity_type": "automated_attribute_change", "original_meeting_type": "No Meeting Type", "source": "ICICI_MIGRATION"}, "error": "CSV UI automation failed", "timestamp": "2025-05-30T04:30:02.179338", "subject": "ICICI: MP Activity Frequency"}, {"row_number": "140", "activity": {"row_number": "140", "subject": "ICICI: <PERSON><PERSON>", "activity_date": "2024-08-31 05:59:59", "activity_type": "Update", "content_html": "<p>ICICI Bank activity: alert</p>", "plain_text": "ICICI Bank activity: alert", "author_name": "<PERSON>", "author_email": "<EMAIL>", "flow_type": "Standard", "touchpoint_reason": "", "external_attendees": "", "company": "ICICI", "original_activity_type": "alert", "original_meeting_type": "No Meeting Type", "source": "ICICI_MIGRATION"}, "error": "CSV UI automation failed", "timestamp": "2025-05-30T04:30:05.183326", "subject": "ICICI: <PERSON><PERSON>"}, {"row_number": "141", "activity": {"row_number": "141", "subject": "ICICI: New Business Model", "activity_date": "2024-08-28 17:20:02", "activity_type": "Update", "content_html": "<p>One-time campaign \"New Business Model\" was sent to 3 users. </p>", "plain_text": "One-time campaign \"New Business Model\" was sent to 3 users.", "author_name": "<PERSON>", "author_email": "<EMAIL>", "flow_type": "Adoption", "touchpoint_reason": "", "external_attendees": "", "company": "ICICI", "original_activity_type": "campaign_touch", "original_meeting_type": "No Meeting Type", "source": "ICICI_MIGRATION"}, "error": "CSV UI automation failed", "timestamp": "2025-05-30T04:44:29.035012", "subject": "ICICI: New Business Model"}, {"row_number": "142", "activity": {"row_number": "142", "subject": "ICICI: Slack to #risk", "activity_date": "2024-08-22 16:30:12", "activity_type": "Update", "content_html": "<p>Webhook 'Slack to #risk' was success</p>", "plain_text": "Webhook 'Slack to #risk' was success", "author_name": "<PERSON>", "author_email": "<EMAIL>", "flow_type": "Adoption", "touchpoint_reason": "", "external_attendees": "", "company": "ICICI", "original_activity_type": "webhook", "original_meeting_type": "No Meeting Type", "source": "ICICI_MIGRATION"}, "error": "CSV UI automation failed", "timestamp": "2025-05-30T04:44:32.533214", "subject": "ICICI: Slack to #risk"}, {"row_number": "143", "activity": {"row_number": "143", "subject": "ICICI: Risk Next Step(s)", "activity_date": "2024-08-22 15:30:15", "activity_type": "Update", "content_html": "<p>Automated update: Risk Next Step(s) changed from '# Check with technical teams what is the workaround or other suggestions for offline scan customers. For this reason they are not able to transit to CLI from the UA.' to '[Auto] - Follow up with Management to put a plan in place to mitigate risk'</p>", "plain_text": "Automated update: Risk Next Step(s) changed from '# Check with technical teams what is the workaround or other suggestions for offline scan customers. For this reason they are not able to transit to CLI from the UA.' to '[Auto] - Follow up with Management to put a plan in place to mitigate risk'", "author_name": "<PERSON>", "author_email": "<EMAIL>", "flow_type": "Risk", "touchpoint_reason": "", "external_attendees": "", "company": "ICICI", "original_activity_type": "automated_attribute_change", "original_meeting_type": "No Meeting Type", "source": "ICICI_MIGRATION"}, "error": "CSV UI automation failed", "timestamp": "2025-05-30T04:44:35.600975", "subject": "ICICI: Risk Next Step(s)"}, {"row_number": "144", "activity": {"row_number": "144", "subject": "ICICI: Primary Risk Reason", "activity_date": "2024-08-22 15:30:15", "activity_type": "Update", "content_html": "<p>Automated update: Primary Risk Reason changed from 'Blocker to the MP migration as they are doing offline scans.' to '[Auto] - Blocker to the Mend Platform Identified'</p>", "plain_text": "Automated update: Primary Risk Reason changed from 'Blocker to the MP migration as they are doing offline scans.' to '[Auto] - Blocker to the Mend Platform Identified'", "author_name": "<PERSON>", "author_email": "<EMAIL>", "flow_type": "Risk", "touchpoint_reason": "", "external_attendees": "", "company": "ICICI", "original_activity_type": "automated_attribute_change", "original_meeting_type": "No Meeting Type", "source": "ICICI_MIGRATION"}, "error": "CSV UI automation failed", "timestamp": "2025-05-30T04:44:38.606460", "subject": "ICICI: Primary Risk Reason"}, {"row_number": "145", "activity": {"row_number": "145", "subject": "ICICI: Risk Status", "activity_date": "2024-08-22 15:30:15", "activity_type": "Update", "content_html": "<p>Automated update: Risk Status changed from 'Resolved' to 'Investigation'</p>", "plain_text": "Automated update: Risk Status changed from 'Resolved' to 'Investigation'", "author_name": "<PERSON>", "author_email": "<EMAIL>", "flow_type": "Risk", "touchpoint_reason": "", "external_attendees": "", "company": "ICICI", "original_activity_type": "automated_attribute_change", "original_meeting_type": "No Meeting Type", "source": "ICICI_MIGRATION"}, "error": "CSV UI automation failed", "timestamp": "2025-05-30T04:44:41.621677", "subject": "ICICI: Risk Status"}, {"row_number": "146", "activity": {"row_number": "146", "subject": "ICICI: <PERSON><PERSON>", "activity_date": "2024-08-22 05:59:59", "activity_type": "Update", "content_html": "<p>ICICI Bank activity: alert</p>", "plain_text": "ICICI Bank activity: alert", "author_name": "<PERSON>", "author_email": "<EMAIL>", "flow_type": "Standard", "touchpoint_reason": "", "external_attendees": "", "company": "ICICI", "original_activity_type": "alert", "original_meeting_type": "No Meeting Type", "source": "ICICI_MIGRATION"}, "error": "CSV UI automation failed", "timestamp": "2025-05-30T04:44:44.630715", "subject": "ICICI: <PERSON><PERSON>"}, {"row_number": "147", "activity": {"row_number": "147", "subject": "ICICI: MP Activity Frequency", "activity_date": "2024-08-15 15:43:52", "activity_type": "Update", "content_html": "<p>Automated update: MP Activity Frequency changed from '' to 'Never Used'</p>", "plain_text": "Automated update: MP Activity Frequency changed from '' to 'Never Used'", "author_name": "<PERSON>", "author_email": "<EMAIL>", "flow_type": "Onboarding", "touchpoint_reason": "", "external_attendees": "", "company": "ICICI", "original_activity_type": "automated_attribute_change", "original_meeting_type": "No Meeting Type", "source": "ICICI_MIGRATION"}, "error": "CSV UI automation failed", "timestamp": "2025-05-30T04:58:57.822633", "subject": "ICICI: MP Activity Frequency"}, {"row_number": "148", "activity": {"row_number": "148", "subject": "ICICI: MP Activity Frequency", "activity_date": "2024-08-13 17:17:04", "activity_type": "Update", "content_html": "<p>Automated update: MP Activity Frequency changed from 'Never Used' to ''</p>", "plain_text": "Automated update: MP Activity Frequency changed from 'Never Used' to ''", "author_name": "<PERSON>", "author_email": "<EMAIL>", "flow_type": "Onboarding", "touchpoint_reason": "", "external_attendees": "", "company": "ICICI", "original_activity_type": "automated_attribute_change", "original_meeting_type": "No Meeting Type", "source": "ICICI_MIGRATION"}, "error": "CSV UI automation failed", "timestamp": "2025-05-30T04:59:00.835509", "subject": "ICICI: MP Activity Frequency"}, {"row_number": "149", "activity": {"row_number": "149", "subject": "ICICI: NPS Send Date", "activity_date": "2024-08-11 21:30:16", "activity_type": "Update", "content_html": "<p>Automated update: NPS Send Date changed from '' to '2024-11-09T20:30:15.985Z'</p>", "plain_text": "Automated update: NPS Send Date changed from '' to '2024-11-09T20:30:15.985Z'", "author_name": "<PERSON>", "author_email": "<EMAIL>", "flow_type": "Nps", "touchpoint_reason": "", "external_attendees": "", "company": "ICICI", "original_activity_type": "automated_attribute_change", "original_meeting_type": "No Meeting Type", "source": "ICICI_MIGRATION"}, "error": "CSV UI automation failed", "timestamp": "2025-05-30T04:59:04.321295", "subject": "ICICI: NPS Send Date"}, {"row_number": "150", "activity": {"row_number": "150", "subject": "ICICI: MP Activity Frequency", "activity_date": "2024-08-09 02:53:30", "activity_type": "Update", "content_html": "<p>Automated update: MP Activity Frequency changed from '' to 'Never Used'</p>", "plain_text": "Automated update: MP Activity Frequency changed from '' to 'Never Used'", "author_name": "<PERSON>", "author_email": "<EMAIL>", "flow_type": "Onboarding", "touchpoint_reason": "", "external_attendees": "", "company": "ICICI", "original_activity_type": "automated_attribute_change", "original_meeting_type": "No Meeting Type", "source": "ICICI_MIGRATION"}, "error": "CSV UI automation failed", "timestamp": "2025-05-30T04:59:07.422093", "subject": "ICICI: MP Activity Frequency"}, {"row_number": "151", "activity": {"row_number": "151", "subject": "ICICI: MP Activity", "activity_date": "2024-07-26 20:10:44", "activity_type": "Update", "content_html": "<p>Automated update: MP Activity changed from '' to 'Not Engaged'</p>", "plain_text": "Automated update: MP Activity changed from '' to 'Not Engaged'", "author_name": "<PERSON>", "author_email": "<EMAIL>", "flow_type": "Standard", "touchpoint_reason": "", "external_attendees": "", "company": "ICICI", "original_activity_type": "automated_attribute_change", "original_meeting_type": "No Meeting Type", "source": "ICICI_MIGRATION"}, "error": "CSV UI automation failed", "timestamp": "2025-05-30T05:13:39.242460", "subject": "ICICI: MP Activity"}, {"row_number": "152", "activity": {"row_number": "152", "subject": "ICICI: MP Activity Status", "activity_date": "2024-07-26 19:38:31", "activity_type": "Update", "content_html": "<p>Automated update: MP Activity Status changed from 'Not Engaged' to ''</p>", "plain_text": "Automated update: MP Activity Status changed from 'Not Engaged' to ''", "author_name": "<PERSON>", "author_email": "<EMAIL>", "flow_type": "Onboarding", "touchpoint_reason": "", "external_attendees": "", "company": "ICICI", "original_activity_type": "automated_attribute_change", "original_meeting_type": "No Meeting Type", "source": "ICICI_MIGRATION"}, "error": "CSV UI automation failed", "timestamp": "2025-05-30T05:13:42.281743", "subject": "ICICI: MP Activity Status"}, {"row_number": "153", "activity": {"row_number": "153", "subject": "ICICI: <PERSON><PERSON>", "activity_date": "2024-07-24 05:59:59", "activity_type": "Update", "content_html": "<p>ICICI Bank activity: alert</p>", "plain_text": "ICICI Bank activity: alert", "author_name": "<PERSON>", "author_email": "<EMAIL>", "flow_type": "Standard", "touchpoint_reason": "", "external_attendees": "", "company": "ICICI", "original_activity_type": "alert", "original_meeting_type": "No Meeting Type", "source": "ICICI_MIGRATION"}, "error": "CSV UI automation failed", "timestamp": "2025-05-30T05:13:45.292907", "subject": "ICICI: <PERSON><PERSON>"}, {"row_number": "154", "activity": {"row_number": "154", "subject": "ICICI: MP Activity Status", "activity_date": "2024-07-20 00:12:53", "activity_type": "Update", "content_html": "<p>Automated update: MP Activity Status changed from '' to 'Not Engaged'</p>", "plain_text": "Automated update: MP Activity Status changed from '' to 'Not Engaged'", "author_name": "<PERSON>", "author_email": "<EMAIL>", "flow_type": "Onboarding", "touchpoint_reason": "", "external_attendees": "", "company": "ICICI", "original_activity_type": "automated_attribute_change", "original_meeting_type": "No Meeting Type", "source": "ICICI_MIGRATION"}, "error": "CSV UI automation failed", "timestamp": "2025-05-30T05:13:48.308002", "subject": "ICICI: MP Activity Status"}, {"row_number": "155", "activity": {"row_number": "155", "subject": "ICICI: Product Roadmap H2 2024", "activity_date": "2024-07-16 15:20:20", "activity_type": "Update", "content_html": "<p>One-time campaign \"Product Roadmap H2 2024\" was sent to 2 users. </p>", "plain_text": "One-time campaign \"Product Roadmap H2 2024\" was sent to 2 users.", "author_name": "<PERSON>", "author_email": "<EMAIL>", "flow_type": "Adoption", "touchpoint_reason": "", "external_attendees": "", "company": "ICICI", "original_activity_type": "campaign_touch", "original_meeting_type": "No Meeting Type", "source": "ICICI_MIGRATION"}, "error": "CSV UI automation failed", "timestamp": "2025-05-30T05:13:51.321491", "subject": "ICICI: Product Roadmap H2 2024"}, {"row_number": "156", "activity": {"row_number": "156", "subject": "ICICI: <PERSON><PERSON><PERSON> to Delighted", "activity_date": "2024-07-04 06:30:16", "activity_type": "Update", "content_html": "<p>Webhook 'Zapier to Delighted' was success</p>", "plain_text": "Webhook 'Zapier to Delighted' was success", "author_name": "<PERSON>", "author_email": "<EMAIL>", "flow_type": "Adoption", "touchpoint_reason": "", "external_attendees": "", "company": "ICICI", "original_activity_type": "webhook", "original_meeting_type": "No Meeting Type", "source": "ICICI_MIGRATION"}, "error": "CSV UI automation failed", "timestamp": "2025-05-30T05:16:22.660652", "subject": "ICICI: <PERSON><PERSON><PERSON> to Delighted"}, {"row_number": "157", "activity": {"row_number": "157", "subject": "ICICI: NPS Last Sent", "activity_date": "2024-07-04 06:30:15", "activity_type": "Update", "content_html": "<p>Automated update: NPS Last Sent changed from '' to '2024-07-04T05:30:15.075Z'</p>", "plain_text": "Automated update: NPS Last Sent changed from '' to '2024-07-04T05:30:15.075Z'", "author_name": "<PERSON>", "author_email": "<EMAIL>", "flow_type": "Adoption", "touchpoint_reason": "", "external_attendees": "", "company": "ICICI", "original_activity_type": "automated_attribute_change", "original_meeting_type": "No Meeting Type", "source": "ICICI_MIGRATION"}, "error": "CSV UI automation failed", "timestamp": "2025-05-30T05:16:25.666173", "subject": "ICICI: NPS Last Sent"}, {"row_number": "158", "activity": {"row_number": "158", "subject": "ICICI: NPS Send Date", "activity_date": "2024-07-04 06:30:15", "activity_type": "Update", "content_html": "<p>Automated update: NPS Send Date changed from '2024-07-03' to '2024-12-31T05:30:15.075Z'</p>", "plain_text": "Automated update: NPS Send Date changed from '2024-07-03' to '2024-12-31T05:30:15.075Z'", "author_name": "<PERSON>", "author_email": "<EMAIL>", "flow_type": "Adoption", "touchpoint_reason": "", "external_attendees": "", "company": "ICICI", "original_activity_type": "automated_attribute_change", "original_meeting_type": "No Meeting Type", "source": "ICICI_MIGRATION"}, "error": "CSV UI automation failed", "timestamp": "2025-05-30T05:16:28.671389", "subject": "ICICI: NPS Send Date"}, {"row_number": "159", "activity": {"row_number": "159", "subject": "ICICI: NPS Last Sent", "activity_date": "2024-07-04 06:30:15", "activity_type": "Update", "content_html": "<p>Automated update: NPS Last Sent changed from '' to '2024-07-04T05:30:14.945Z'</p>", "plain_text": "Automated update: NPS Last Sent changed from '' to '2024-07-04T05:30:14.945Z'", "author_name": "<PERSON>", "author_email": "<EMAIL>", "flow_type": "Adoption", "touchpoint_reason": "", "external_attendees": "", "company": "ICICI", "original_activity_type": "automated_attribute_change", "original_meeting_type": "No Meeting Type", "source": "ICICI_MIGRATION"}, "error": "CSV UI automation failed", "timestamp": "2025-05-30T05:31:51.440785", "subject": "ICICI: NPS Last Sent"}, {"row_number": "160", "activity": {"row_number": "160", "subject": "ICICI: NPS Send Date", "activity_date": "2024-07-04 06:30:15", "activity_type": "Update", "content_html": "<p>Automated update: NPS Send Date changed from '2024-07-03' to '2024-12-31T05:30:14.945Z'</p>", "plain_text": "Automated update: NPS Send Date changed from '2024-07-03' to '2024-12-31T05:30:14.945Z'", "author_name": "<PERSON>", "author_email": "<EMAIL>", "flow_type": "Adoption", "touchpoint_reason": "", "external_attendees": "", "company": "ICICI", "original_activity_type": "automated_attribute_change", "original_meeting_type": "No Meeting Type", "source": "ICICI_MIGRATION"}, "error": "CSV UI automation failed", "timestamp": "2025-05-30T05:49:24.232861", "subject": "ICICI: NPS Send Date"}, {"row_number": "161", "activity": {"row_number": "161", "subject": "ICICI: <PERSON><PERSON><PERSON> to Delighted", "activity_date": "2024-07-04 06:30:15", "activity_type": "Update", "content_html": "<p>Webhook 'Zapier to Delighted' was success</p>", "plain_text": "Webhook 'Zapier to Delighted' was success", "author_name": "<PERSON>", "author_email": "<EMAIL>", "flow_type": "Adoption", "touchpoint_reason": "", "external_attendees": "", "company": "ICICI", "original_activity_type": "webhook", "original_meeting_type": "No Meeting Type", "source": "ICICI_MIGRATION"}, "error": "CSV UI automation failed", "timestamp": "2025-05-30T05:59:14.588984", "subject": "ICICI: <PERSON><PERSON><PERSON> to Delighted"}, {"row_number": "162", "activity": {"row_number": "162", "subject": "ICICI: Vulnerability Insights with MITRE Data", "activity_date": "2024-06-28 18:40:06", "activity_type": "Update", "content_html": "<p>One-time campaign \"Vulnerability Insights with MITRE Data \" was sent to 2 users. </p>", "plain_text": "One-time campaign \"Vulnerability Insights with MITRE Data \" was sent to 2 users.", "author_name": "<PERSON>", "author_email": "<EMAIL>", "flow_type": "Adoption", "touchpoint_reason": "", "external_attendees": "", "company": "ICICI", "original_activity_type": "campaign_touch", "original_meeting_type": "No Meeting Type", "source": "ICICI_MIGRATION"}, "error": "CSV UI automation failed", "timestamp": "2025-05-30T05:59:17.670107", "subject": "ICICI: Vulnerability Insights with MITRE Data"}, {"row_number": "163", "activity": {"row_number": "163", "subject": "ICICI: <PERSON><PERSON>", "activity_date": "2024-06-28 05:59:59", "activity_type": "Update", "content_html": "<p>ICICI Bank activity: alert</p>", "plain_text": "ICICI Bank activity: alert", "author_name": "<PERSON>", "author_email": "<EMAIL>", "flow_type": "Standard", "touchpoint_reason": "", "external_attendees": "", "company": "ICICI", "original_activity_type": "alert", "original_meeting_type": "No Meeting Type", "source": "ICICI_MIGRATION"}, "error": "CSV UI automation failed", "timestamp": "2025-05-30T05:59:20.799013", "subject": "ICICI: <PERSON><PERSON>"}, {"row_number": "164", "activity": {"row_number": "164", "subject": "ICICI: Mend Platform Access", "activity_date": "2024-06-27 03:30:24", "activity_type": "Update", "content_html": "<p>Automated update: Mend Platform Access changed from '' to 'Access Provided'</p>", "plain_text": "Automated update: Mend Platform Access changed from '' to 'Access Provided'", "author_name": "<PERSON>", "author_email": "<EMAIL>", "flow_type": "Onboarding", "touchpoint_reason": "", "external_attendees": "", "company": "ICICI", "original_activity_type": "automated_attribute_change", "original_meeting_type": "No Meeting Type", "source": "ICICI_MIGRATION"}, "error": "CSV UI automation failed", "timestamp": "2025-05-30T05:59:23.808590", "subject": "ICICI: Mend Platform Access"}, {"row_number": "165", "activity": {"row_number": "165", "subject": "ICICI: CSM Managed", "activity_date": "2024-06-26 21:35:21", "activity_type": "Update", "content_html": "<p>Automated update: CSM Managed changed from '' to 'CSM Direct'</p>", "plain_text": "Automated update: CSM Managed changed from '' to 'CSM Direct'", "author_name": "<PERSON>", "author_email": "<EMAIL>", "flow_type": "Risk", "touchpoint_reason": "", "external_attendees": "", "company": "ICICI", "original_activity_type": "automated_attribute_change", "original_meeting_type": "No Meeting Type", "source": "ICICI_MIGRATION"}, "error": "CSV UI automation failed", "timestamp": "2025-05-30T06:13:40.859893", "subject": "ICICI: CSM Managed"}, {"row_number": "166", "activity": {"row_number": "166", "subject": "ICICI: <div><br>Upcoming NPS Survey&nbsp;</div>", "activity_date": "2024-06-19 06:30:08", "activity_type": "Update", "content_html": "<p><div>An NPS Survey Send Date is approaching in 14 days for a Key Contact at <span class=\"mention-wrapper\" data-reactroot=\"\"><span class=\"field\">ICICI Bank</span><br></span></div></p>", "plain_text": "An NPS Survey Send Date is approaching in 14 days for a Key Contact at ICICI Bank", "author_name": "<PERSON>", "author_email": "<EMAIL>", "flow_type": "Adoption", "touchpoint_reason": "", "external_attendees": "", "company": "ICICI", "original_activity_type": "account_alert", "original_meeting_type": "No Meeting Type", "source": "ICICI_MIGRATION"}, "error": "CSV UI automation failed", "timestamp": "2025-05-30T06:13:43.875110", "subject": "ICICI: <div><br>Upcoming NPS Survey&nbsp;</div>"}, {"row_number": "167", "activity": {"row_number": "167", "subject": "ICICI: <PERSON><PERSON>", "activity_date": "2024-05-23 05:59:59", "activity_type": "Update", "content_html": "<p>ICICI Bank activity: alert</p>", "plain_text": "ICICI Bank activity: alert", "author_name": "<PERSON>", "author_email": "<EMAIL>", "flow_type": "Standard", "touchpoint_reason": "", "external_attendees": "", "company": "ICICI", "original_activity_type": "alert", "original_meeting_type": "No Meeting Type", "source": "ICICI_MIGRATION"}, "error": "CSV UI automation failed", "timestamp": "2025-05-30T06:13:46.941653", "subject": "ICICI: <PERSON><PERSON>"}, {"row_number": "168", "activity": {"row_number": "168", "subject": "ICICI: <PERSON><PERSON>", "activity_date": "2024-05-22 05:59:59", "activity_type": "Update", "content_html": "<p>ICICI Bank activity: alert</p>", "plain_text": "ICICI Bank activity: alert", "author_name": "<PERSON>", "author_email": "<EMAIL>", "flow_type": "Standard", "touchpoint_reason": "", "external_attendees": "", "company": "ICICI", "original_activity_type": "alert", "original_meeting_type": "No Meeting Type", "source": "ICICI_MIGRATION"}, "error": "CSV UI automation failed", "timestamp": "2025-05-30T06:13:49.979960", "subject": "ICICI: <PERSON><PERSON>"}, {"row_number": "169", "activity": {"row_number": "169", "subject": "ICICI: <PERSON><PERSON>", "activity_date": "2024-05-21 05:59:59", "activity_type": "Update", "content_html": "<p>ICICI Bank activity: alert</p>", "plain_text": "ICICI Bank activity: alert", "author_name": "<PERSON>", "author_email": "<EMAIL>", "flow_type": "Standard", "touchpoint_reason": "", "external_attendees": "", "company": "ICICI", "original_activity_type": "alert", "original_meeting_type": "No Meeting Type", "source": "ICICI_MIGRATION"}, "error": "CSV UI automation failed", "timestamp": "2025-05-30T06:13:52.985794", "subject": "ICICI: <PERSON><PERSON>"}, {"row_number": "170", "activity": {"row_number": "170", "subject": "ICICI: <PERSON><PERSON>", "activity_date": "2024-05-15 05:59:59", "activity_type": "Update", "content_html": "<p>ICICI Bank activity: alert</p>", "plain_text": "ICICI Bank activity: alert", "author_name": "<PERSON>", "author_email": "<EMAIL>", "flow_type": "Standard", "touchpoint_reason": "", "external_attendees": "", "company": "ICICI", "original_activity_type": "alert", "original_meeting_type": "No Meeting Type", "source": "ICICI_MIGRATION"}, "error": "CSV UI automation failed", "timestamp": "2025-05-30T06:17:22.746688", "subject": "ICICI: <PERSON><PERSON>"}, {"row_number": "171", "activity": {"row_number": "171", "subject": "ICICI: <PERSON> Follow Up Email", "activity_date": "2024-04-25 14:57:49", "activity_type": "Meeting", "content_html": "<p>Note activity for ICICI Bank</p>", "plain_text": "Note activity for ICICI Bank", "author_name": "<PERSON>", "author_email": "<EMAIL>", "flow_type": "Adoption", "touchpoint_reason": "RENEWAL", "external_attendees": "", "company": "ICICI", "original_activity_type": "note", "original_meeting_type": "Meeting", "source": "ICICI_MIGRATION"}, "error": "CSV UI automation failed", "timestamp": "2025-05-30T06:31:53.431032", "subject": "ICICI: <PERSON> Follow Up Email"}, {"row_number": "172", "activity": {"row_number": "172", "subject": "ICICI: <PERSON> Follow Up Email", "activity_date": "2024-04-25 14:57:49", "activity_type": "Update", "content_html": "<p>ICICI Bank activity: task</p>", "plain_text": "ICICI Bank activity: task", "author_name": "<PERSON>", "author_email": "<EMAIL>", "flow_type": "Adoption", "touchpoint_reason": "", "external_attendees": "", "company": "ICICI", "original_activity_type": "task", "original_meeting_type": "No Meeting Type", "source": "ICICI_MIGRATION"}, "error": "CSV UI automation failed", "timestamp": "2025-05-30T06:31:56.436801", "subject": "ICICI: <PERSON> Follow Up Email"}, {"row_number": "173", "activity": {"row_number": "173", "subject": "ICICI: <PERSON> Follow Up Email", "activity_date": "2024-04-18 15:30:20", "activity_type": "Update", "content_html": "<p>ICICI Bank activity: task</p>", "plain_text": "ICICI Bank activity: task", "author_name": "<PERSON>", "author_email": "<EMAIL>", "flow_type": "Adoption", "touchpoint_reason": "", "external_attendees": "", "company": "ICICI", "original_activity_type": "task", "original_meeting_type": "No Meeting Type", "source": "ICICI_MIGRATION"}, "error": "CSV UI automation failed", "timestamp": "2025-05-30T06:31:59.569948", "subject": "ICICI: <PERSON> Follow Up Email"}, {"row_number": "174", "activity": {"row_number": "174", "subject": "ICICI: Slack to #risk", "activity_date": "2024-04-17 23:30:33", "activity_type": "Update", "content_html": "<p>Webhook 'Slack to #risk' was success</p>", "plain_text": "Webhook 'Slack to #risk' was success", "author_name": "<PERSON>", "author_email": "<EMAIL>", "flow_type": "Risk", "touchpoint_reason": "", "external_attendees": "", "company": "ICICI", "original_activity_type": "webhook", "original_meeting_type": "No Meeting Type", "source": "ICICI_MIGRATION"}, "error": "CSV UI automation failed", "timestamp": "2025-05-30T06:32:03.401513", "subject": "ICICI: Slack to #risk"}, {"row_number": "175", "activity": {"row_number": "175", "subject": "ICICI: <PERSON><PERSON>", "activity_date": "2024-04-15 05:59:59", "activity_type": "Update", "content_html": "<p>ICICI Bank activity: alert</p>", "plain_text": "ICICI Bank activity: alert", "author_name": "<PERSON>", "author_email": "<EMAIL>", "flow_type": "Standard", "touchpoint_reason": "", "external_attendees": "", "company": "ICICI", "original_activity_type": "alert", "original_meeting_type": "No Meeting Type", "source": "ICICI_MIGRATION"}, "error": "CSV UI automation failed", "timestamp": "2025-05-30T06:46:23.780127", "subject": "ICICI: <PERSON><PERSON>"}, {"row_number": "176", "activity": {"row_number": "176", "subject": "ICICI: <PERSON><PERSON><PERSON> - CSM Satisfaction Survey/Kelle Intro", "activity_date": "2024-04-11 15:20:00", "activity_type": "Update", "content_html": "<p>One-time campaign \"Oksana - CSM Satisfaction Survey/Kelle Intro\" was sent to 2 users. </p>", "plain_text": "One-time campaign \"Oksana - CSM Satisfaction Survey/Kelle Intro\" was sent to 2 users.", "author_name": "<PERSON>", "author_email": "<EMAIL>", "flow_type": "Adoption", "touchpoint_reason": "", "external_attendees": "", "company": "ICICI", "original_activity_type": "campaign_touch", "original_meeting_type": "No Meeting Type", "source": "ICICI_MIGRATION"}, "error": "CSV UI automation failed", "timestamp": "2025-05-30T06:46:26.790913", "subject": "ICICI: <PERSON><PERSON><PERSON> - CSM Satisfaction Survey/Kelle Intro"}, {"row_number": "177", "activity": {"row_number": "177", "subject": "ICICI: <div><span class=\"mention-wrapper\" data-reactroot=\"\"><span class=\"field\">ICICI Bank</span>'s renewal date has passed</span></div>", "activity_date": "2024-04-08 06:30:07", "activity_type": "Update", "content_html": "<p><div>Send a thank you email if they renewed</div></p>", "plain_text": "Send a thank you email if they renewed", "author_name": "<PERSON>", "author_email": "<EMAIL>", "flow_type": "Adoption", "touchpoint_reason": "", "external_attendees": "", "company": "ICICI", "original_activity_type": "account_alert", "original_meeting_type": "No Meeting Type", "source": "ICICI_MIGRATION"}, "error": "CSV UI automation failed", "timestamp": "2025-05-30T06:46:29.975966", "subject": "ICICI: <div><span class=\"mention-wrapper\" data-reactroot=\"\"><span class=\"field\">ICICI Bank</span>'s renewal date has passed</span></div>"}, {"row_number": "178", "activity": {"row_number": "178", "subject": "ICICI: Correction - CVE - 2024 - 3094_copy", "activity_date": "2024-04-03 16:20:09", "activity_type": "Update", "content_html": "<p>One-time campaign \"Correction - CVE - 2024 - 3094_copy\" was sent to 2 users. </p>", "plain_text": "One-time campaign \"Correction - CVE - 2024 - 3094_copy\" was sent to 2 users.", "author_name": "<PERSON>", "author_email": "<EMAIL>", "flow_type": "Adoption", "touchpoint_reason": "", "external_attendees": "", "company": "ICICI", "original_activity_type": "campaign_touch", "original_meeting_type": "No Meeting Type", "source": "ICICI_MIGRATION"}, "error": "CSV UI automation failed", "timestamp": "2025-05-30T06:46:32.988759", "subject": "ICICI: Correction - CVE - 2024 - 3094_copy"}, {"row_number": "179", "activity": {"row_number": "179", "subject": "ICICI: <PERSON><PERSON>", "activity_date": "2024-04-03 05:59:59", "activity_type": "Update", "content_html": "<p>ICICI Bank activity: alert</p>", "plain_text": "ICICI Bank activity: alert", "author_name": "<PERSON>", "author_email": "<EMAIL>", "flow_type": "Standard", "touchpoint_reason": "", "external_attendees": "", "company": "ICICI", "original_activity_type": "alert", "original_meeting_type": "No Meeting Type", "source": "ICICI_MIGRATION"}, "error": "CSV UI automation failed", "timestamp": "2025-05-30T07:00:53.391548", "subject": "ICICI: <PERSON><PERSON>"}, {"row_number": "180", "activity": {"row_number": "180", "subject": "ICICI: All Other Customers - CVE - 2024 - 3094", "activity_date": "2024-04-02 23:00:01", "activity_type": "Update", "content_html": "<p>One-time campaign \"All Other Customers - CVE - 2024 - 3094\" was sent to 2 users. </p>", "plain_text": "One-time campaign \"All Other Customers - CVE - 2024 - 3094\" was sent to 2 users.", "author_name": "<PERSON>", "author_email": "<EMAIL>", "flow_type": "Adoption", "touchpoint_reason": "", "external_attendees": "", "company": "ICICI", "original_activity_type": "campaign_touch", "original_meeting_type": "No Meeting Type", "source": "ICICI_MIGRATION"}, "error": "CSV UI automation failed", "timestamp": "2025-05-30T07:00:56.399340", "subject": "ICICI: All Other Customers - CVE - 2024 - 3094"}, {"row_number": "181", "activity": {"row_number": "181", "subject": "ICICI: <PERSON><PERSON>", "activity_date": "2024-03-30 04:59:59", "activity_type": "Update", "content_html": "<p>ICICI Bank activity: alert</p>", "plain_text": "ICICI Bank activity: alert", "author_name": "<PERSON>", "author_email": "<EMAIL>", "flow_type": "Standard", "touchpoint_reason": "", "external_attendees": "", "company": "ICICI", "original_activity_type": "alert", "original_meeting_type": "No Meeting Type", "source": "ICICI_MIGRATION"}, "error": "CSV UI automation failed", "timestamp": "2025-05-30T07:01:09.433055", "subject": "ICICI: <PERSON><PERSON>"}, {"row_number": "182", "activity": {"row_number": "182", "subject": "ICICI: License Utilization Status", "activity_date": "2024-03-28 02:30:14", "activity_type": "Update", "content_html": "<p>Automated update: License Utilization Status changed from 'Moderate' to 'Low'</p>", "plain_text": "Automated update: License Utilization Status changed from 'Moderate' to 'Low'", "author_name": "<PERSON>", "author_email": "<EMAIL>", "flow_type": "Intelligence", "touchpoint_reason": "", "external_attendees": "", "company": "ICICI", "original_activity_type": "automated_attribute_change", "original_meeting_type": "No Meeting Type", "source": "ICICI_MIGRATION"}, "error": "CSV UI automation failed", "timestamp": "2025-05-30T07:01:12.443508", "subject": "ICICI: License Utilization Status"}, {"row_number": "183", "activity": {"row_number": "183", "subject": "ICICI: License Utilization Status", "activity_date": "2024-03-27 02:30:16", "activity_type": "Update", "content_html": "<p>Automated update: License Utilization Status changed from 'Low' to 'Moderate'</p>", "plain_text": "Automated update: License Utilization Status changed from 'Low' to 'Moderate'", "author_name": "<PERSON>", "author_email": "<EMAIL>", "flow_type": "Renewal", "touchpoint_reason": "", "external_attendees": "", "company": "ICICI", "original_activity_type": "automated_attribute_change", "original_meeting_type": "No Meeting Type", "source": "ICICI_MIGRATION"}, "error": "CSV UI automation failed", "timestamp": "2025-05-30T07:15:25.743209", "subject": "ICICI: License Utilization Status"}, {"row_number": "184", "activity": {"row_number": "184", "subject": "ICICI: <PERSON><PERSON>", "activity_date": "2024-03-15 04:59:59", "activity_type": "Update", "content_html": "<p>ICICI Bank activity: alert</p>", "plain_text": "ICICI Bank activity: alert", "author_name": "<PERSON>", "author_email": "<EMAIL>", "flow_type": "Standard", "touchpoint_reason": "", "external_attendees": "", "company": "ICICI", "original_activity_type": "alert", "original_meeting_type": "No Meeting Type", "source": "ICICI_MIGRATION"}, "error": "CSV UI automation failed", "timestamp": "2025-05-30T07:15:28.751726", "subject": "ICICI: <PERSON><PERSON>"}, {"row_number": "185", "activity": {"row_number": "185", "subject": "ICICI: <PERSON><PERSON>", "activity_date": "2024-03-14 04:59:59", "activity_type": "Update", "content_html": "<p>ICICI Bank activity: alert</p>", "plain_text": "ICICI Bank activity: alert", "author_name": "<PERSON>", "author_email": "<EMAIL>", "flow_type": "Standard", "touchpoint_reason": "", "external_attendees": "", "company": "ICICI", "original_activity_type": "alert", "original_meeting_type": "No Meeting Type", "source": "ICICI_MIGRATION"}, "error": "CSV UI automation failed", "timestamp": "2025-05-30T07:15:31.912596", "subject": "ICICI: <PERSON><PERSON>"}, {"row_number": "186", "activity": {"row_number": "186", "subject": "ICICI: <PERSON><PERSON>", "activity_date": "2024-03-07 04:59:59", "activity_type": "Update", "content_html": "<p>ICICI Bank activity: alert</p>", "plain_text": "ICICI Bank activity: alert", "author_name": "<PERSON>", "author_email": "<EMAIL>", "flow_type": "Standard", "touchpoint_reason": "", "external_attendees": "", "company": "ICICI", "original_activity_type": "alert", "original_meeting_type": "No Meeting Type", "source": "ICICI_MIGRATION"}, "error": "CSV UI automation failed", "timestamp": "2025-05-30T07:15:34.963403", "subject": "ICICI: <PERSON><PERSON>"}, {"row_number": "187", "activity": {"row_number": "187", "subject": "ICICI: <PERSON><PERSON>", "activity_date": "2024-02-29 04:59:59", "activity_type": "Update", "content_html": "<p>ICICI Bank activity: alert</p>", "plain_text": "ICICI Bank activity: alert", "author_name": "<PERSON>", "author_email": "<EMAIL>", "flow_type": "Standard", "touchpoint_reason": "", "external_attendees": "", "company": "ICICI", "original_activity_type": "alert", "original_meeting_type": "No Meeting Type", "source": "ICICI_MIGRATION"}, "error": "CSV UI automation failed", "timestamp": "2025-05-30T07:18:23.391393", "subject": "ICICI: <PERSON><PERSON>"}, {"row_number": "188", "activity": {"row_number": "188", "subject": "ICICI: Executive Engaged", "activity_date": "2024-02-21 22:45:35", "activity_type": "Update", "content_html": "<p>Automated update: Executive Engaged changed from '' to 'No'</p>", "plain_text": "Automated update: Executive Engaged changed from '' to 'No'", "author_name": "<PERSON>", "author_email": "<EMAIL>", "flow_type": "Risk", "touchpoint_reason": "", "external_attendees": "", "company": "ICICI", "original_activity_type": "automated_attribute_change", "original_meeting_type": "No Meeting Type", "source": "ICICI_MIGRATION"}, "error": "CSV UI automation failed", "timestamp": "2025-05-30T07:18:26.399746", "subject": "ICICI: Executive Engaged"}, {"row_number": "189", "activity": {"row_number": "189", "subject": "ICICI: AI Survey - Gold/ Platinum Customers - Follow Up", "activity_date": "2024-01-30 17:20:06", "activity_type": "Update", "content_html": "<p>One-time campaign \"AI Survey - Gold/ Platinum Customers - Follow Up\" was sent to 2 users. </p>", "plain_text": "One-time campaign \"AI Survey - Gold/ Platinum Customers - Follow Up\" was sent to 2 users.", "author_name": "<PERSON>", "author_email": "<EMAIL>", "flow_type": "Adoption", "touchpoint_reason": "", "external_attendees": "", "company": "ICICI", "original_activity_type": "campaign_touch", "original_meeting_type": "No Meeting Type", "source": "ICICI_MIGRATION"}, "error": "CSV UI automation failed", "timestamp": "2025-05-30T07:18:29.414800", "subject": "ICICI: AI Survey - Gold/ Platinum Customers - Follow Up"}, {"row_number": "190", "activity": {"row_number": "190", "subject": "ICICI: <PERSON><PERSON>", "activity_date": "2024-01-30 04:59:59", "activity_type": "Update", "content_html": "<p>ICICI Bank activity: alert</p>", "plain_text": "ICICI Bank activity: alert", "author_name": "<PERSON>", "author_email": "<EMAIL>", "flow_type": "Standard", "touchpoint_reason": "", "external_attendees": "", "company": "ICICI", "original_activity_type": "alert", "original_meeting_type": "No Meeting Type", "source": "ICICI_MIGRATION"}, "error": "CSV UI automation failed", "timestamp": "2025-05-30T07:18:32.602906", "subject": "ICICI: <PERSON><PERSON>"}, {"row_number": "191", "activity": {"row_number": "191", "subject": "ICICI: <PERSON><PERSON>", "activity_date": "2024-01-29 04:59:59", "activity_type": "Update", "content_html": "<p>ICICI Bank activity: alert</p>", "plain_text": "ICICI Bank activity: alert", "author_name": "<PERSON>", "author_email": "<EMAIL>", "flow_type": "Standard", "touchpoint_reason": "", "external_attendees": "", "company": "ICICI", "original_activity_type": "alert", "original_meeting_type": "No Meeting Type", "source": "ICICI_MIGRATION"}, "error": "CSV UI automation failed", "timestamp": "2025-05-30T07:33:03.125350", "subject": "ICICI: <PERSON><PERSON>"}, {"row_number": "192", "activity": {"row_number": "192", "subject": "ICICI: AI Survey - Int. Gold & Platinum Customers Oops_copy", "activity_date": "2024-01-19 16:40:00", "activity_type": "Update", "content_html": "<p>One-time campaign \"AI Survey - Int. Gold & Platinum Customers Oops_copy\" was sent to 2 users. </p>", "plain_text": "One-time campaign \"AI Survey - Int. Gold & Platinum Customers Oops_copy\" was sent to 2 users.", "author_name": "<PERSON>", "author_email": "<EMAIL>", "flow_type": "Adoption", "touchpoint_reason": "", "external_attendees": "", "company": "ICICI", "original_activity_type": "campaign_touch", "original_meeting_type": "No Meeting Type", "source": "ICICI_MIGRATION"}, "error": "CSV UI automation failed", "timestamp": "2025-05-30T07:37:10.610962", "subject": "ICICI: AI Survey - Int. Gold & Platinum Customers Oops_copy"}, {"row_number": "193", "activity": {"row_number": "193", "subject": "ICICI: AI Survey - Gold & Platinum Customers", "activity_date": "2024-01-18 15:20:01", "activity_type": "Update", "content_html": "<p>One-time campaign \"AI Survey - Gold & Platinum Customers\" was sent to 2 users. </p>", "plain_text": "One-time campaign \"AI Survey - Gold & Platinum Customers\" was sent to 2 users.", "author_name": "<PERSON>", "author_email": "<EMAIL>", "flow_type": "Adoption", "touchpoint_reason": "", "external_attendees": "", "company": "ICICI", "original_activity_type": "campaign_touch", "original_meeting_type": "No Meeting Type", "source": "ICICI_MIGRATION"}, "error": "CSV UI automation failed", "timestamp": "2025-05-30T07:37:13.628804", "subject": "ICICI: AI Survey - Gold & Platinum Customers"}, {"row_number": "194", "activity": {"row_number": "194", "subject": "ICICI: <PERSON><PERSON>", "activity_date": "2024-01-18 04:59:59", "activity_type": "Update", "content_html": "<p>ICICI Bank activity: alert</p>", "plain_text": "ICICI Bank activity: alert", "author_name": "<PERSON>", "author_email": "<EMAIL>", "flow_type": "Standard", "touchpoint_reason": "", "external_attendees": "", "company": "ICICI", "original_activity_type": "alert", "original_meeting_type": "No Meeting Type", "source": "ICICI_MIGRATION"}, "error": "CSV UI automation failed", "timestamp": "2025-05-30T07:37:16.671956", "subject": "ICICI: <PERSON><PERSON>"}, {"row_number": "195", "activity": {"row_number": "195", "subject": "ICICI: <PERSON><PERSON>", "activity_date": "2024-01-17 04:59:59", "activity_type": "Update", "content_html": "<p>ICICI Bank activity: alert</p>", "plain_text": "ICICI Bank activity: alert", "author_name": "<PERSON>", "author_email": "<EMAIL>", "flow_type": "Standard", "touchpoint_reason": "", "external_attendees": "", "company": "ICICI", "original_activity_type": "alert", "original_meeting_type": "No Meeting Type", "source": "ICICI_MIGRATION"}, "error": "CSV UI automation failed", "timestamp": "2025-05-30T07:37:19.680108", "subject": "ICICI: <PERSON><PERSON>"}, {"row_number": "196", "activity": {"row_number": "196", "subject": "ICICI: <PERSON><PERSON>", "activity_date": "2024-01-14 04:59:59", "activity_type": "Update", "content_html": "<p>ICICI Bank activity: alert</p>", "plain_text": "ICICI Bank activity: alert", "author_name": "<PERSON>", "author_email": "<EMAIL>", "flow_type": "Standard", "touchpoint_reason": "", "external_attendees": "", "company": "ICICI", "original_activity_type": "alert", "original_meeting_type": "No Meeting Type", "source": "ICICI_MIGRATION"}, "error": "CSV UI automation failed", "timestamp": "2025-05-30T07:37:22.684638", "subject": "ICICI: <PERSON><PERSON>"}, {"row_number": "197", "activity": {"row_number": "197", "subject": "ICICI: <PERSON><PERSON>", "activity_date": "2024-01-12 04:59:59", "activity_type": "Update", "content_html": "<p>ICICI Bank activity: alert</p>", "plain_text": "ICICI Bank activity: alert", "author_name": "<PERSON>", "author_email": "<EMAIL>", "flow_type": "Standard", "touchpoint_reason": "", "external_attendees": "", "company": "ICICI", "original_activity_type": "alert", "original_meeting_type": "No Meeting Type", "source": "ICICI_MIGRATION"}, "error": "CSV UI automation failed", "timestamp": "2025-05-30T07:37:25.694153", "subject": "ICICI: <PERSON><PERSON>"}, {"row_number": "198", "activity": {"row_number": "198", "subject": "ICICI: Slack to #risk", "activity_date": "2024-01-11 13:30:09", "activity_type": "Update", "content_html": "<p>Webhook 'Slack to #risk' was success</p>", "plain_text": "Webhook 'Slack to #risk' was success", "author_name": "<PERSON>", "author_email": "<EMAIL>", "flow_type": "Adoption", "touchpoint_reason": "", "external_attendees": "", "company": "ICICI", "original_activity_type": "webhook", "original_meeting_type": "No Meeting Type", "source": "ICICI_MIGRATION"}, "error": "CSV UI automation failed", "timestamp": "2025-05-30T07:37:28.700742", "subject": "ICICI: Slack to #risk"}, {"row_number": "199", "activity": {"row_number": "199", "subject": "ICICI: Risk Update", "activity_date": "2024-01-11 12:30:07", "activity_type": "Update", "content_html": "<p>Automated update: Risk Update changed from 'Jan 11th OS- NEXT STEPS: <PERSON><PERSON> will start to work on the renewal with potential upsell. MAIN ISSUE: the customer is managed by a partner - Meteonic- and we do not have the full picture of customer satisfaction.' to '[Auto] - Sales Renewal in Upside'</p>", "plain_text": "Automated update: Risk Update changed from 'Jan 11th OS- NEXT STEPS: <PERSON><PERSON> will start to work on the renewal with potential upsell. MAIN ISSUE: the customer is managed by a partner - Meteonic- and we do not have the full picture of customer satisfaction.' to '[Auto] - Sales Renewal in Upside'", "author_name": "<PERSON>", "author_email": "<EMAIL>", "flow_type": "Risk", "touchpoint_reason": "", "external_attendees": "", "company": "ICICI", "original_activity_type": "automated_attribute_change", "original_meeting_type": "No Meeting Type", "source": "ICICI_MIGRATION"}, "error": "CSV UI automation failed", "timestamp": "2025-05-30T07:37:31.708005", "subject": "ICICI: Risk Update"}, {"row_number": "200", "activity": {"row_number": "200", "subject": "ICICI: Risk Status", "activity_date": "2024-01-11 12:30:07", "activity_type": "Update", "content_html": "<p>Automated update: Risk Status changed from '' to 'Watch'</p>", "plain_text": "Automated update: Risk Status changed from '' to 'Watch'", "author_name": "<PERSON>", "author_email": "<EMAIL>", "flow_type": "Risk", "touchpoint_reason": "", "external_attendees": "", "company": "ICICI", "original_activity_type": "automated_attribute_change", "original_meeting_type": "No Meeting Type", "source": "ICICI_MIGRATION"}, "error": "CSV UI automation failed", "timestamp": "2025-05-30T07:37:34.715557", "subject": "ICICI: Risk Status"}, {"row_number": "201", "activity": {"row_number": "201", "subject": "ICICI: <PERSON><PERSON>", "activity_date": "2024-01-11 04:59:59", "activity_type": "Update", "content_html": "<p>ICICI Bank activity: alert</p>", "plain_text": "ICICI Bank activity: alert", "author_name": "<PERSON>", "author_email": "<EMAIL>", "flow_type": "Standard", "touchpoint_reason": "", "external_attendees": "", "company": "ICICI", "original_activity_type": "alert", "original_meeting_type": "No Meeting Type", "source": "ICICI_MIGRATION"}, "error": "CSV UI automation failed", "timestamp": "2025-05-30T07:37:47.730390", "subject": "ICICI: <PERSON><PERSON>"}, {"row_number": "202", "activity": {"row_number": "202", "subject": "ICICI: <PERSON><PERSON>", "activity_date": "2024-01-08 04:59:59", "activity_type": "Update", "content_html": "<p>ICICI Bank activity: alert</p>", "plain_text": "ICICI Bank activity: alert", "author_name": "<PERSON>", "author_email": "<EMAIL>", "flow_type": "Standard", "touchpoint_reason": "", "external_attendees": "", "company": "ICICI", "original_activity_type": "alert", "original_meeting_type": "No Meeting Type", "source": "ICICI_MIGRATION"}, "error": "CSV UI automation failed", "timestamp": "2025-05-30T07:37:50.735697", "subject": "ICICI: <PERSON><PERSON>"}, {"row_number": "203", "activity": {"row_number": "203", "subject": "ICICI: NPS Send Date", "activity_date": "2024-01-05 05:30:19", "activity_type": "Update", "content_html": "<p>Automated update: NPS Send Date changed from '2024-01-04' to '2024-07-03T05:30:19.224Z'</p>", "plain_text": "Automated update: NPS Send Date changed from '2024-01-04' to '2024-07-03T05:30:19.224Z'", "author_name": "<PERSON>", "author_email": "<EMAIL>", "flow_type": "Nps", "touchpoint_reason": "", "external_attendees": "", "company": "ICICI", "original_activity_type": "automated_attribute_change", "original_meeting_type": "No Meeting Type", "source": "ICICI_MIGRATION"}, "error": "CSV UI automation failed", "timestamp": "2025-05-30T07:37:53.741636", "subject": "ICICI: NPS Send Date"}, {"row_number": "204", "activity": {"row_number": "204", "subject": "ICICI: NPS Informed", "activity_date": "2024-01-05 05:30:19", "activity_type": "Update", "content_html": "<p>Automated update: NPS Informed changed from 'Yes' to 'updated'</p>", "plain_text": "Automated update: NPS Informed changed from 'Yes' to 'updated'", "author_name": "<PERSON>", "author_email": "<EMAIL>", "flow_type": "Nps", "touchpoint_reason": "", "external_attendees": "", "company": "ICICI", "original_activity_type": "automated_attribute_change", "original_meeting_type": "No Meeting Type", "source": "ICICI_MIGRATION"}, "error": "CSV UI automation failed", "timestamp": "2025-05-30T07:37:56.749564", "subject": "ICICI: NPS Informed"}, {"row_number": "205", "activity": {"row_number": "205", "subject": "ICICI: NPS Informed", "activity_date": "2024-01-02 05:30:12", "activity_type": "Update", "content_html": "<p>Automated update: NPS Informed changed from '' to 'Yes'</p>", "plain_text": "Automated update: NPS Informed changed from '' to 'Yes'", "author_name": "<PERSON>", "author_email": "<EMAIL>", "flow_type": "Nps", "touchpoint_reason": "", "external_attendees": "", "company": "ICICI", "original_activity_type": "automated_attribute_change", "original_meeting_type": "No Meeting Type", "source": "ICICI_MIGRATION"}, "error": "CSV UI automation failed", "timestamp": "2025-05-30T07:37:59.756785", "subject": "ICICI: NPS Informed"}, {"row_number": "206", "activity": {"row_number": "206", "subject": "ICICI: Touch Status 2", "activity_date": "2023-12-23 21:30:44", "activity_type": "Update", "content_html": "<p>Automated update: Touch Status 2 changed from '' to 'Overdue'</p>", "plain_text": "Automated update: Touch Status 2 changed from '' to 'Overdue'", "author_name": "<PERSON>", "author_email": "<EMAIL>", "flow_type": "Intelligence", "touchpoint_reason": "", "external_attendees": "", "company": "ICICI", "original_activity_type": "automated_attribute_change", "original_meeting_type": "No Meeting Type", "source": "ICICI_MIGRATION"}, "error": "CSV UI automation failed", "timestamp": "2025-05-30T07:38:02.760533", "subject": "ICICI: Touch Status 2"}, {"row_number": "207", "activity": {"row_number": "207", "subject": "ICICI: <PERSON><PERSON>", "activity_date": "2023-12-17 04:59:59", "activity_type": "Update", "content_html": "<p>ICICI Bank activity: alert</p>", "plain_text": "ICICI Bank activity: alert", "author_name": "<PERSON>", "author_email": "<EMAIL>", "flow_type": "Standard", "touchpoint_reason": "", "external_attendees": "", "company": "ICICI", "original_activity_type": "alert", "original_meeting_type": "No Meeting Type", "source": "ICICI_MIGRATION"}, "error": "CSV UI automation failed", "timestamp": "2025-05-30T07:38:05.768937", "subject": "ICICI: <PERSON><PERSON>"}, {"row_number": "208", "activity": {"row_number": "208", "subject": "ICICI: <PERSON><PERSON>", "activity_date": "2023-12-13 04:59:59", "activity_type": "Update", "content_html": "<p>ICICI Bank activity: alert</p>", "plain_text": "ICICI Bank activity: alert", "author_name": "<PERSON>", "author_email": "<EMAIL>", "flow_type": "Standard", "touchpoint_reason": "", "external_attendees": "", "company": "ICICI", "original_activity_type": "alert", "original_meeting_type": "No Meeting Type", "source": "ICICI_MIGRATION"}, "error": "CSV UI automation failed", "timestamp": "2025-05-30T07:38:08.774386", "subject": "ICICI: <PERSON><PERSON>"}, {"row_number": "209", "activity": {"row_number": "209", "subject": "ICICI: <PERSON><PERSON>", "activity_date": "2023-12-12 04:59:59", "activity_type": "Update", "content_html": "<p>ICICI Bank activity: alert</p>", "plain_text": "ICICI Bank activity: alert", "author_name": "<PERSON>", "author_email": "<EMAIL>", "flow_type": "Standard", "touchpoint_reason": "", "external_attendees": "", "company": "ICICI", "original_activity_type": "alert", "original_meeting_type": "No Meeting Type", "source": "ICICI_MIGRATION"}, "error": "CSV UI automation failed", "timestamp": "2025-05-30T07:38:11.782799", "subject": "ICICI: <PERSON><PERSON>"}, {"row_number": "210", "activity": {"row_number": "210", "subject": "ICICI: <PERSON><PERSON>", "activity_date": "2023-12-11 04:59:59", "activity_type": "Update", "content_html": "<p>ICICI Bank activity: alert</p>", "plain_text": "ICICI Bank activity: alert", "author_name": "<PERSON>", "author_email": "<EMAIL>", "flow_type": "Standard", "touchpoint_reason": "", "external_attendees": "", "company": "ICICI", "original_activity_type": "alert", "original_meeting_type": "No Meeting Type", "source": "ICICI_MIGRATION"}, "error": "CSV UI automation failed", "timestamp": "2025-05-30T07:38:14.789503", "subject": "ICICI: <PERSON><PERSON>"}, {"row_number": "211", "activity": {"row_number": "211", "subject": "ICICI: <PERSON><PERSON>", "activity_date": "2023-12-04 04:59:59", "activity_type": "Update", "content_html": "<p>ICICI Bank activity: alert</p>", "plain_text": "ICICI Bank activity: alert", "author_name": "<PERSON>", "author_email": "<EMAIL>", "flow_type": "Standard", "touchpoint_reason": "", "external_attendees": "", "company": "ICICI", "original_activity_type": "alert", "original_meeting_type": "No Meeting Type", "source": "ICICI_MIGRATION"}, "error": "CSV UI automation failed", "timestamp": "2025-05-30T07:38:27.803792", "subject": "ICICI: <PERSON><PERSON>"}, {"row_number": "212", "activity": {"row_number": "212", "subject": "ICICI: <PERSON><PERSON>", "activity_date": "2023-12-01 04:59:59", "activity_type": "Update", "content_html": "<p>ICICI Bank activity: alert</p>", "plain_text": "ICICI Bank activity: alert", "author_name": "<PERSON>", "author_email": "<EMAIL>", "flow_type": "Standard", "touchpoint_reason": "", "external_attendees": "", "company": "ICICI", "original_activity_type": "alert", "original_meeting_type": "No Meeting Type", "source": "ICICI_MIGRATION"}, "error": "CSV UI automation failed", "timestamp": "2025-05-30T07:38:30.808839", "subject": "ICICI: <PERSON><PERSON>"}, {"row_number": "213", "activity": {"row_number": "213", "subject": "ICICI: Slack to #risk", "activity_date": "2023-11-30 12:30:05", "activity_type": "Update", "content_html": "<p>Webhook 'Slack to #risk' was success</p>", "plain_text": "Webhook 'Slack to #risk' was success", "author_name": "<PERSON>", "author_email": "<EMAIL>", "flow_type": "Adoption", "touchpoint_reason": "", "external_attendees": "", "company": "ICICI", "original_activity_type": "webhook", "original_meeting_type": "No Meeting Type", "source": "ICICI_MIGRATION"}, "error": "CSV UI automation failed", "timestamp": "2025-05-30T07:38:33.813389", "subject": "ICICI: Slack to #risk"}, {"row_number": "214", "activity": {"row_number": "214", "subject": "ICICI: Risk Update", "activity_date": "2023-11-30 11:30:11", "activity_type": "Update", "content_html": "<p>Automated update: Risk Update changed from '' to '[Auto] - Sales Renewal in Upside'</p>", "plain_text": "Automated update: Risk Update changed from '' to '[Auto] - Sales Renewal in Upside'", "author_name": "<PERSON>", "author_email": "<EMAIL>", "flow_type": "Risk", "touchpoint_reason": "", "external_attendees": "", "company": "ICICI", "original_activity_type": "automated_attribute_change", "original_meeting_type": "No Meeting Type", "source": "ICICI_MIGRATION"}, "error": "CSV UI automation failed", "timestamp": "2025-05-30T07:38:36.824176", "subject": "ICICI: Risk Update"}, {"row_number": "215", "activity": {"row_number": "215", "subject": "ICICI: Risk Status", "activity_date": "2023-11-30 11:30:11", "activity_type": "Update", "content_html": "<p>Automated update: Risk Status changed from '' to 'Watch'</p>", "plain_text": "Automated update: Risk Status changed from '' to 'Watch'", "author_name": "<PERSON>", "author_email": "<EMAIL>", "flow_type": "Risk", "touchpoint_reason": "", "external_attendees": "", "company": "ICICI", "original_activity_type": "automated_attribute_change", "original_meeting_type": "No Meeting Type", "source": "ICICI_MIGRATION"}, "error": "CSV UI automation failed", "timestamp": "2025-05-30T07:38:39.828030", "subject": "ICICI: Risk Status"}, {"row_number": "216", "activity": {"row_number": "216", "subject": "ICICI: <PERSON><PERSON>", "activity_date": "2023-11-21 04:59:59", "activity_type": "Update", "content_html": "<p>ICICI Bank activity: alert</p>", "plain_text": "ICICI Bank activity: alert", "author_name": "<PERSON>", "author_email": "<EMAIL>", "flow_type": "Standard", "touchpoint_reason": "", "external_attendees": "", "company": "ICICI", "original_activity_type": "alert", "original_meeting_type": "No Meeting Type", "source": "ICICI_MIGRATION"}, "error": "CSV UI automation failed", "timestamp": "2025-05-30T07:38:42.832831", "subject": "ICICI: <PERSON><PERSON>"}, {"row_number": "217", "activity": {"row_number": "217", "subject": "ICICI: <PERSON><PERSON>", "activity_date": "2023-10-26 05:59:59", "activity_type": "Update", "content_html": "<p>ICICI Bank activity: alert</p>", "plain_text": "ICICI Bank activity: alert", "author_name": "<PERSON>", "author_email": "<EMAIL>", "flow_type": "Standard", "touchpoint_reason": "", "external_attendees": "", "company": "ICICI", "original_activity_type": "alert", "original_meeting_type": "No Meeting Type", "source": "ICICI_MIGRATION"}, "error": "CSV UI automation failed", "timestamp": "2025-05-30T07:38:45.837760", "subject": "ICICI: <PERSON><PERSON>"}, {"row_number": "218", "activity": {"row_number": "218", "subject": "ICICI: <PERSON><PERSON>", "activity_date": "2023-10-22 05:59:59", "activity_type": "Update", "content_html": "<p>ICICI Bank activity: alert</p>", "plain_text": "ICICI Bank activity: alert", "author_name": "<PERSON>", "author_email": "<EMAIL>", "flow_type": "Standard", "touchpoint_reason": "", "external_attendees": "", "company": "ICICI", "original_activity_type": "alert", "original_meeting_type": "No Meeting Type", "source": "ICICI_MIGRATION"}, "error": "CSV UI automation failed", "timestamp": "2025-05-30T07:38:48.848230", "subject": "ICICI: <PERSON><PERSON>"}, {"row_number": "219", "activity": {"row_number": "219", "subject": "ICICI: <PERSON><PERSON>", "activity_date": "2023-10-21 05:59:59", "activity_type": "Update", "content_html": "<p>ICICI Bank activity: alert</p>", "plain_text": "ICICI Bank activity: alert", "author_name": "<PERSON>", "author_email": "<EMAIL>", "flow_type": "Standard", "touchpoint_reason": "", "external_attendees": "", "company": "ICICI", "original_activity_type": "alert", "original_meeting_type": "No Meeting Type", "source": "ICICI_MIGRATION"}, "error": "CSV UI automation failed", "timestamp": "2025-05-30T07:38:51.854093", "subject": "ICICI: <PERSON><PERSON>"}, {"row_number": "220", "activity": {"row_number": "220", "subject": "ICICI: <PERSON><PERSON>", "activity_date": "2023-10-08 05:59:59", "activity_type": "Update", "content_html": "<p>ICICI Bank activity: alert</p>", "plain_text": "ICICI Bank activity: alert", "author_name": "<PERSON>", "author_email": "<EMAIL>", "flow_type": "Standard", "touchpoint_reason": "", "external_attendees": "", "company": "ICICI", "original_activity_type": "alert", "original_meeting_type": "No Meeting Type", "source": "ICICI_MIGRATION"}, "error": "CSV UI automation failed", "timestamp": "2025-05-30T07:38:54.862408", "subject": "ICICI: <PERSON><PERSON>"}, {"row_number": "221", "activity": {"row_number": "221", "subject": "ICICI: <PERSON><PERSON>", "activity_date": "2023-10-07 05:59:59", "activity_type": "Update", "content_html": "<p>ICICI Bank activity: alert</p>", "plain_text": "ICICI Bank activity: alert", "author_name": "<PERSON>", "author_email": "<EMAIL>", "flow_type": "Standard", "touchpoint_reason": "", "external_attendees": "", "company": "ICICI", "original_activity_type": "alert", "original_meeting_type": "No Meeting Type", "source": "ICICI_MIGRATION"}, "error": "CSV UI automation failed", "timestamp": "2025-05-30T07:39:07.885850", "subject": "ICICI: <PERSON><PERSON>"}, {"row_number": "222", "activity": {"row_number": "222", "subject": "ICICI: <PERSON><PERSON>", "activity_date": "2023-10-02 05:59:59", "activity_type": "Update", "content_html": "<p>ICICI Bank activity: alert</p>", "plain_text": "ICICI Bank activity: alert", "author_name": "<PERSON>", "author_email": "<EMAIL>", "flow_type": "Standard", "touchpoint_reason": "", "external_attendees": "", "company": "ICICI", "original_activity_type": "alert", "original_meeting_type": "No Meeting Type", "source": "ICICI_MIGRATION"}, "error": "CSV UI automation failed", "timestamp": "2025-05-30T07:39:10.890533", "subject": "ICICI: <PERSON><PERSON>"}, {"row_number": "223", "activity": {"row_number": "223", "subject": "ICICI: <PERSON><PERSON>", "activity_date": "2023-10-01 05:59:59", "activity_type": "Update", "content_html": "<p>ICICI Bank activity: alert</p>", "plain_text": "ICICI Bank activity: alert", "author_name": "<PERSON>", "author_email": "<EMAIL>", "flow_type": "Standard", "touchpoint_reason": "", "external_attendees": "", "company": "ICICI", "original_activity_type": "alert", "original_meeting_type": "No Meeting Type", "source": "ICICI_MIGRATION"}, "error": "CSV UI automation failed", "timestamp": "2025-05-30T07:39:13.896031", "subject": "ICICI: <PERSON><PERSON>"}, {"row_number": "224", "activity": {"row_number": "224", "subject": "ICICI: <PERSON><PERSON>", "activity_date": "2023-09-30 05:59:59", "activity_type": "Update", "content_html": "<p>ICICI Bank activity: alert</p>", "plain_text": "ICICI Bank activity: alert", "author_name": "<PERSON>", "author_email": "<EMAIL>", "flow_type": "Standard", "touchpoint_reason": "", "external_attendees": "", "company": "ICICI", "original_activity_type": "alert", "original_meeting_type": "No Meeting Type", "source": "ICICI_MIGRATION"}, "error": "CSV UI automation failed", "timestamp": "2025-05-30T07:39:16.904004", "subject": "ICICI: <PERSON><PERSON>"}, {"row_number": "225", "activity": {"row_number": "225", "subject": "ICICI: <PERSON><PERSON>", "activity_date": "2023-09-21 05:59:59", "activity_type": "Update", "content_html": "<p>ICICI Bank activity: alert</p>", "plain_text": "ICICI Bank activity: alert", "author_name": "<PERSON>", "author_email": "<EMAIL>", "flow_type": "Standard", "touchpoint_reason": "", "external_attendees": "", "company": "ICICI", "original_activity_type": "alert", "original_meeting_type": "No Meeting Type", "source": "ICICI_MIGRATION"}, "error": "CSV UI automation failed", "timestamp": "2025-05-30T07:39:19.913033", "subject": "ICICI: <PERSON><PERSON>"}, {"row_number": "226", "activity": {"row_number": "226", "subject": "ICICI: <PERSON><PERSON>", "activity_date": "2023-09-13 05:59:59", "activity_type": "Update", "content_html": "<p>ICICI Bank activity: alert</p>", "plain_text": "ICICI Bank activity: alert", "author_name": "<PERSON>", "author_email": "<EMAIL>", "flow_type": "Standard", "touchpoint_reason": "", "external_attendees": "", "company": "ICICI", "original_activity_type": "alert", "original_meeting_type": "No Meeting Type", "source": "ICICI_MIGRATION"}, "error": "CSV UI automation failed", "timestamp": "2025-05-30T07:39:22.921682", "subject": "ICICI: <PERSON><PERSON>"}, {"row_number": "227", "activity": {"row_number": "227", "subject": "ICICI: ICICI Bank <> Mend - Catch Up Meeting External", "activity_date": "2023-09-11 14:53:14", "activity_type": "Update", "content_html": "<p>Note activity for ICICI Bank</p>", "plain_text": "Note activity for ICICI Bank", "author_name": "<PERSON>", "author_email": "<EMAIL>", "flow_type": "Adoption", "touchpoint_reason": "CADENCE", "external_attendees": "", "company": "ICICI", "original_activity_type": "note", "original_meeting_type": "Update", "source": "ICICI_MIGRATION"}, "error": "CSV UI automation failed", "timestamp": "2025-05-30T07:39:25.927215", "subject": "ICICI: ICICI Bank <> Mend - Catch Up Meeting External"}, {"row_number": "228", "activity": {"row_number": "228", "subject": "ICICI: <PERSON><PERSON>", "activity_date": "2023-09-11 05:59:59", "activity_type": "Update", "content_html": "<p>ICICI Bank activity: alert</p>", "plain_text": "ICICI Bank activity: alert", "author_name": "<PERSON>", "author_email": "<EMAIL>", "flow_type": "Standard", "touchpoint_reason": "", "external_attendees": "", "company": "ICICI", "original_activity_type": "alert", "original_meeting_type": "No Meeting Type", "source": "ICICI_MIGRATION"}, "error": "CSV UI automation failed", "timestamp": "2025-05-30T07:39:28.935117", "subject": "ICICI: <PERSON><PERSON>"}, {"row_number": "229", "activity": {"row_number": "229", "subject": "ICICI: SCA CD Variance Stages", "activity_date": "2023-09-08 08:30:19", "activity_type": "Update", "content_html": "<p>Automated update: SCA CD Variance Stages changed from 'No Data' to 'High'</p>", "plain_text": "Automated update: SCA CD Variance Stages changed from 'No Data' to 'High'", "author_name": "<PERSON>", "author_email": "<EMAIL>", "flow_type": "Intelligence", "touchpoint_reason": "", "external_attendees": "", "company": "ICICI", "original_activity_type": "automated_attribute_change", "original_meeting_type": "No Meeting Type", "source": "ICICI_MIGRATION"}, "error": "CSV UI automation failed", "timestamp": "2025-05-30T07:39:31.945223", "subject": "ICICI: SCA CD Variance Stages"}, {"row_number": "230", "activity": {"row_number": "230", "subject": "ICICI: License Utilization Status", "activity_date": "2023-09-08 03:30:15", "activity_type": "Update", "content_html": "<p>Automated update: License Utilization Status changed from '' to 'Low'</p>", "plain_text": "Automated update: License Utilization Status changed from '' to 'Low'", "author_name": "<PERSON>", "author_email": "<EMAIL>", "flow_type": "Intelligence", "touchpoint_reason": "", "external_attendees": "", "company": "ICICI", "original_activity_type": "automated_attribute_change", "original_meeting_type": "No Meeting Type", "source": "ICICI_MIGRATION"}, "error": "CSV UI automation failed", "timestamp": "2025-05-30T07:39:34.949935", "subject": "ICICI: License Utilization Status"}, {"row_number": "231", "activity": {"row_number": "231", "subject": "ICICI: Mend Vulnerability found by WithSecure", "activity_date": "2023-09-05 13:49:08", "activity_type": "Update", "content_html": "<p>One-time campaign \"Mend Vulnerability found by WithSecure\" was sent to 2 users. \nFrom: robert.ni<PERSON>@Mend.io</p>", "plain_text": "One-time campaign \"Mend Vulnerability found by WithSecure\" was sent to 2 users. \nFrom: <EMAIL>", "author_name": "<PERSON>", "author_email": "<EMAIL>", "flow_type": "Intelligence", "touchpoint_reason": "", "external_attendees": "", "company": "ICICI", "original_activity_type": "campaign_touch", "original_meeting_type": "No Meeting Type", "source": "ICICI_MIGRATION"}, "error": "CSV UI automation failed", "timestamp": "2025-05-30T07:39:47.964932", "subject": "ICICI: Mend Vulnerability found by WithSecure"}, {"row_number": "232", "activity": {"row_number": "232", "subject": "ICICI: ICICI Bank <> Mend - Catch Up Meeting", "activity_date": "2023-09-01 08:28:01", "activity_type": "Meeting", "content_html": "<p>Note activity for ICICI Bank</p>", "plain_text": "Note activity for ICICI Bank", "author_name": "<PERSON>", "author_email": "<EMAIL>", "flow_type": "Adoption", "touchpoint_reason": "CADENCE", "external_attendees": "", "company": "ICICI", "original_activity_type": "note", "original_meeting_type": "Meeting", "source": "ICICI_MIGRATION"}, "error": "CSV UI automation failed", "timestamp": "2025-05-30T07:39:50.976244", "subject": "ICICI: ICICI Bank <> Mend - Catch Up Meeting"}, {"row_number": "233", "activity": {"row_number": "233", "subject": "ICICI: ICICI Bank<>Mend.io organization migration from LBA to VBA", "activity_date": "2023-08-10 13:54:35", "activity_type": "Meeting", "content_html": "<p>Note activity for ICICI Bank</p>", "plain_text": "Note activity for ICICI Bank", "author_name": "<PERSON>", "author_email": "<EMAIL>", "flow_type": "Adoption", "touchpoint_reason": "CADENCE", "external_attendees": "", "company": "ICICI", "original_activity_type": "note", "original_meeting_type": "Meeting", "source": "ICICI_MIGRATION"}, "error": "CSV UI automation failed", "timestamp": "2025-05-30T07:39:53.980673", "subject": "ICICI: ICICI Bank<>Mend.io organization migration from LBA to VBA"}, {"row_number": "234", "activity": {"row_number": "234", "subject": "ICICI: <PERSON><PERSON>", "activity_date": "2023-07-29 05:59:59", "activity_type": "Update", "content_html": "<p>ICICI Bank activity: alert</p>", "plain_text": "ICICI Bank activity: alert", "author_name": "<PERSON>", "author_email": "<EMAIL>", "flow_type": "Standard", "touchpoint_reason": "", "external_attendees": "", "company": "ICICI", "original_activity_type": "alert", "original_meeting_type": "No Meeting Type", "source": "ICICI_MIGRATION"}, "error": "CSV UI automation failed", "timestamp": "2025-05-30T07:39:56.984477", "subject": "ICICI: <PERSON><PERSON>"}, {"row_number": "235", "activity": {"row_number": "235", "subject": "ICICI: No need to touch base", "activity_date": "2023-07-27 14:07:29", "activity_type": "Meeting", "content_html": "<p>Note activity for ICICI Bank</p>", "plain_text": "Note activity for ICICI Bank", "author_name": "<PERSON>", "author_email": "<EMAIL>", "flow_type": "Adoption", "touchpoint_reason": "CADENCE", "external_attendees": "", "company": "ICICI", "original_activity_type": "note", "original_meeting_type": "Meeting", "source": "ICICI_MIGRATION"}, "error": "CSV UI automation failed", "timestamp": "2025-05-30T07:39:59.988052", "subject": "ICICI: No need to touch base"}, {"row_number": "236", "activity": {"row_number": "236", "subject": "ICICI: Unified Agent Hotfix now available-due to Java upgrade issue", "activity_date": "2023-07-26 20:48:41", "activity_type": "Update", "content_html": "<p>One-time campaign \"Unified Agent Hotfix now available-due to Java upgrade issue \" was sent to 2 users. \nFrom: linda.rod<PERSON>ue<PERSON>@Mend.io</p>", "plain_text": "One-time campaign \"Unified Agent Hotfix now available-due to Java upgrade issue \" was sent to 2 users. \nFrom: <EMAIL>", "author_name": "<PERSON>", "author_email": "<EMAIL>", "flow_type": "Support", "touchpoint_reason": "", "external_attendees": "", "company": "ICICI", "original_activity_type": "campaign_touch", "original_meeting_type": "No Meeting Type", "source": "ICICI_MIGRATION"}, "error": "CSV UI automation failed", "timestamp": "2025-05-30T07:40:02.998254", "subject": "ICICI: Unified Agent Hotfix now available-due to Java upgrade issue"}, {"row_number": "237", "activity": {"row_number": "237", "subject": "ICICI: Unified Agent - Java upgrade issue", "activity_date": "2023-07-25 19:42:33", "activity_type": "Update", "content_html": "<p>One-time campaign \"Unified Agent - Java upgrade issue\" was sent to 2 users. \nFrom: linda.<PERSON><PERSON><PERSON><PERSON>@Mend.io</p>", "plain_text": "One-time campaign \"Unified Agent - Java upgrade issue\" was sent to 2 users. \nFrom: linda.rod<PERSON>ue<PERSON>@Mend.io", "author_name": "<PERSON>", "author_email": "<EMAIL>", "flow_type": "Support", "touchpoint_reason": "", "external_attendees": "", "company": "ICICI", "original_activity_type": "campaign_touch", "original_meeting_type": "No Meeting Type", "source": "ICICI_MIGRATION"}, "error": "CSV UI automation failed", "timestamp": "2025-05-30T07:40:06.007297", "subject": "ICICI: Unified Agent - Java upgrade issue"}, {"row_number": "238", "activity": {"row_number": "238", "subject": "ICICI: LBA email change sent to the partner", "activity_date": "2023-07-24 13:43:18", "activity_type": "Meeting", "content_html": "<p>Note activity for ICICI Bank</p>", "plain_text": "Note activity for ICICI Bank", "author_name": "<PERSON>", "author_email": "<EMAIL>", "flow_type": "Adoption", "touchpoint_reason": "CADENCE", "external_attendees": "", "company": "ICICI", "original_activity_type": "note", "original_meeting_type": "Meeting", "source": "ICICI_MIGRATION"}, "error": "CSV UI automation failed", "timestamp": "2025-05-30T07:40:09.016840", "subject": "ICICI: LBA email change sent to the partner"}, {"row_number": "239", "activity": {"row_number": "239", "subject": "ICICI: ICICI Bank<>MEND", "activity_date": "2023-07-20 11:46:35", "activity_type": "Meeting", "content_html": "<p>Note activity for ICICI Bank</p>", "plain_text": "Note activity for ICICI Bank", "author_name": "<PERSON>", "author_email": "<EMAIL>", "flow_type": "Adoption", "touchpoint_reason": "CADENCE", "external_attendees": "", "company": "ICICI", "original_activity_type": "note", "original_meeting_type": "Meeting", "source": "ICICI_MIGRATION"}, "error": "CSV UI automation failed", "timestamp": "2025-05-30T07:40:12.024086", "subject": "ICICI: ICICI Bank<>MEND"}, {"row_number": "240", "activity": {"row_number": "240", "subject": "ICICI: <PERSON><PERSON>", "activity_date": "2023-07-20 05:59:59", "activity_type": "Update", "content_html": "<p>ICICI Bank activity: alert</p>", "plain_text": "ICICI Bank activity: alert", "author_name": "<PERSON>", "author_email": "<EMAIL>", "flow_type": "Standard", "touchpoint_reason": "", "external_attendees": "", "company": "ICICI", "original_activity_type": "alert", "original_meeting_type": "No Meeting Type", "source": "ICICI_MIGRATION"}, "error": "CSV UI automation failed", "timestamp": "2025-05-30T07:40:15.032507", "subject": "ICICI: <PERSON><PERSON>"}, {"row_number": "241", "activity": {"row_number": "241", "subject": "ICICI: <PERSON><PERSON>", "activity_date": "2023-07-18 05:59:59", "activity_type": "Update", "content_html": "<p>ICICI Bank activity: alert</p>", "plain_text": "ICICI Bank activity: alert", "author_name": "<PERSON>", "author_email": "<EMAIL>", "flow_type": "Standard", "touchpoint_reason": "", "external_attendees": "", "company": "ICICI", "original_activity_type": "alert", "original_meeting_type": "No Meeting Type", "source": "ICICI_MIGRATION"}, "error": "CSV UI automation failed", "timestamp": "2025-05-30T07:40:28.045135", "subject": "ICICI: <PERSON><PERSON>"}, {"row_number": "242", "activity": {"row_number": "242", "subject": "ICICI: Request for Developer Training for ICICI", "activity_date": "2023-07-13 14:38:39", "activity_type": "Meeting", "content_html": "<p>Note activity for ICICI Bank</p>", "plain_text": "Note activity for ICICI Bank", "author_name": "<PERSON>", "author_email": "<EMAIL>", "flow_type": "Adoption", "touchpoint_reason": "CADENCE", "external_attendees": "", "company": "ICICI", "original_activity_type": "note", "original_meeting_type": "Meeting", "source": "ICICI_MIGRATION"}, "error": "CSV UI automation failed", "timestamp": "2025-05-30T07:40:31.052574", "subject": "ICICI: Request for Developer Training for ICICI"}, {"row_number": "243", "activity": {"row_number": "243", "subject": "ICICI: Re: Queries on Licensing Policy related details - ICICI Bank", "activity_date": "2023-07-12 19:04:50", "activity_type": "Email", "content_html": "<p>Note activity for ICICI Bank</p>", "plain_text": "Note activity for ICICI Bank", "author_name": "<PERSON>", "author_email": "<EMAIL>", "flow_type": "Adoption", "touchpoint_reason": "CADENCE", "external_attendees": "", "company": "ICICI", "original_activity_type": "note", "original_meeting_type": "Email", "source": "ICICI_MIGRATION"}, "error": "CSV UI automation failed", "timestamp": "2025-05-30T07:40:34.057596", "subject": "ICICI: Re: Queries on Licensing Policy related details - ICICI Bank"}, {"row_number": "244", "activity": {"row_number": "244", "subject": "ICICI: Re: Mend integration with LDAP and SAML - ICICI", "activity_date": "2023-07-12 18:45:46", "activity_type": "Email", "content_html": "<p>Note activity for ICICI Bank</p>", "plain_text": "Note activity for ICICI Bank", "author_name": "<PERSON>", "author_email": "<EMAIL>", "flow_type": "Adoption", "touchpoint_reason": "CADENCE", "external_attendees": "", "company": "ICICI", "original_activity_type": "note", "original_meeting_type": "Email", "source": "ICICI_MIGRATION"}, "error": "CSV UI automation failed", "timestamp": "2025-05-30T07:40:37.065433", "subject": "ICICI: Re: Mend integration with LDAP and SAML - ICICI"}, {"row_number": "245", "activity": {"row_number": "245", "subject": "ICICI: <PERSON><PERSON>", "activity_date": "2023-07-09 05:59:59", "activity_type": "Update", "content_html": "<p>ICICI Bank activity: alert</p>", "plain_text": "ICICI Bank activity: alert", "author_name": "<PERSON>", "author_email": "<EMAIL>", "flow_type": "Standard", "touchpoint_reason": "", "external_attendees": "", "company": "ICICI", "original_activity_type": "alert", "original_meeting_type": "No Meeting Type", "source": "ICICI_MIGRATION"}, "error": "CSV UI automation failed", "timestamp": "2025-05-30T07:40:40.073818", "subject": "ICICI: <PERSON><PERSON>"}, {"row_number": "246", "activity": {"row_number": "246", "subject": "ICICI: NPS Send Date", "activity_date": "2023-07-08 06:30:34", "activity_type": "Update", "content_html": "<p>Automated update: NPS Send Date changed from '2023-07-07' to '2024-01-04T05:30:34.422Z'</p>", "plain_text": "Automated update: NPS Send Date changed from '2023-07-07' to '2024-01-04T05:30:34.422Z'", "author_name": "<PERSON>", "author_email": "<EMAIL>", "flow_type": "Nps", "touchpoint_reason": "", "external_attendees": "", "company": "ICICI", "original_activity_type": "automated_attribute_change", "original_meeting_type": "No Meeting Type", "source": "ICICI_MIGRATION"}, "error": "CSV UI automation failed", "timestamp": "2025-05-30T07:40:43.078766", "subject": "ICICI: NPS Send Date"}, {"row_number": "247", "activity": {"row_number": "247", "subject": "ICICI: NPS Informed", "activity_date": "2023-07-08 06:30:34", "activity_type": "Update", "content_html": "<p>Automated update: NPS Informed changed from 'Yes' to 'updated'</p>", "plain_text": "Automated update: NPS Informed changed from 'Yes' to 'updated'", "author_name": "<PERSON>", "author_email": "<EMAIL>", "flow_type": "Nps", "touchpoint_reason": "", "external_attendees": "", "company": "ICICI", "original_activity_type": "automated_attribute_change", "original_meeting_type": "No Meeting Type", "source": "ICICI_MIGRATION"}, "error": "CSV UI automation failed", "timestamp": "2025-05-30T07:40:46.086043", "subject": "ICICI: NPS Informed"}, {"row_number": "248", "activity": {"row_number": "248", "subject": "ICICI: <PERSON><PERSON>", "activity_date": "2023-07-08 05:59:59", "activity_type": "Update", "content_html": "<p>ICICI Bank activity: alert</p>", "plain_text": "ICICI Bank activity: alert", "author_name": "<PERSON>", "author_email": "<EMAIL>", "flow_type": "Standard", "touchpoint_reason": "", "external_attendees": "", "company": "ICICI", "original_activity_type": "alert", "original_meeting_type": "No Meeting Type", "source": "ICICI_MIGRATION"}, "error": "CSV UI automation failed", "timestamp": "2025-05-30T07:40:49.090374", "subject": "ICICI: <PERSON><PERSON>"}, {"row_number": "249", "activity": {"row_number": "249", "subject": "ICICI: <PERSON><PERSON>", "activity_date": "2023-07-06 05:59:59", "activity_type": "Update", "content_html": "<p>ICICI Bank activity: alert</p>", "plain_text": "ICICI Bank activity: alert", "author_name": "<PERSON>", "author_email": "<EMAIL>", "flow_type": "Standard", "touchpoint_reason": "", "external_attendees": "", "company": "ICICI", "original_activity_type": "alert", "original_meeting_type": "No Meeting Type", "source": "ICICI_MIGRATION"}, "error": "CSV UI automation failed", "timestamp": "2025-05-30T07:40:52.100542", "subject": "ICICI: <PERSON><PERSON>"}, {"row_number": "250", "activity": {"row_number": "250", "subject": "ICICI: No need to touch base- working with the partner", "activity_date": "2023-07-05 08:59:37", "activity_type": "Meeting", "content_html": "<p>Note activity for ICICI Bank</p>", "plain_text": "Note activity for ICICI Bank", "author_name": "<PERSON>", "author_email": "<EMAIL>", "flow_type": "Adoption", "touchpoint_reason": "CADENCE", "external_attendees": "", "company": "ICICI", "original_activity_type": "note", "original_meeting_type": "Meeting", "source": "ICICI_MIGRATION"}, "error": "CSV UI automation failed", "timestamp": "2025-05-30T07:40:55.108545", "subject": "ICICI: No need to touch base- working with the partner"}, {"row_number": "251", "activity": {"row_number": "251", "subject": "ICICI: Inform Key Contacts of upcoming NPS survey", "activity_date": "2023-06-21 12:47:01", "activity_type": "Meeting", "content_html": "<p>Note activity for ICICI Bank</p>", "plain_text": "Note activity for ICICI Bank", "author_name": "<PERSON>", "author_email": "<EMAIL>", "flow_type": "Nps", "touchpoint_reason": "CADENCE", "external_attendees": "", "company": "ICICI", "original_activity_type": "note", "original_meeting_type": "Meeting", "source": "ICICI_MIGRATION"}, "error": "CSV UI automation failed", "timestamp": "2025-05-30T07:41:08.113992", "subject": "ICICI: Inform Key Contacts of upcoming NPS survey"}, {"row_number": "252", "activity": {"row_number": "252", "subject": "ICICI: <PERSON> is working to arrange a meeting with the bank and partner", "activity_date": "2023-06-21 12:43:22", "activity_type": "Meeting", "content_html": "<p>Note activity for ICICI Bank</p>", "plain_text": "Note activity for ICICI Bank", "author_name": "<PERSON>", "author_email": "<EMAIL>", "flow_type": "Adoption", "touchpoint_reason": "CADENCE", "external_attendees": "", "company": "ICICI", "original_activity_type": "note", "original_meeting_type": "Meeting", "source": "ICICI_MIGRATION"}, "error": "CSV UI automation failed", "timestamp": "2025-05-30T07:41:11.120696", "subject": "ICICI: <PERSON> is working to arrange a meeting with the bank and partner"}, {"row_number": "253", "activity": {"row_number": "253", "subject": "ICICI: <PERSON><PERSON>", "activity_date": "2023-06-10 05:59:59", "activity_type": "Update", "content_html": "<p>ICICI Bank activity: alert</p>", "plain_text": "ICICI Bank activity: alert", "author_name": "<PERSON>", "author_email": "<EMAIL>", "flow_type": "Standard", "touchpoint_reason": "", "external_attendees": "", "company": "ICICI", "original_activity_type": "alert", "original_meeting_type": "No Meeting Type", "source": "ICICI_MIGRATION"}, "error": "CSV UI automation failed", "timestamp": "2025-05-30T07:41:14.129882", "subject": "ICICI: <PERSON><PERSON>"}, {"row_number": "254", "activity": {"row_number": "254", "subject": "ICICI: <PERSON><PERSON>", "activity_date": "2023-05-25 05:59:59", "activity_type": "Update", "content_html": "<p>ICICI Bank activity: alert</p>", "plain_text": "ICICI Bank activity: alert", "author_name": "<PERSON>", "author_email": "<EMAIL>", "flow_type": "Standard", "touchpoint_reason": "", "external_attendees": "", "company": "ICICI", "original_activity_type": "alert", "original_meeting_type": "No Meeting Type", "source": "ICICI_MIGRATION"}, "error": "CSV UI automation failed", "timestamp": "2025-05-30T07:41:17.135162", "subject": "ICICI: <PERSON><PERSON>"}, {"row_number": "255", "activity": {"row_number": "255", "subject": "ICICI: Discussion with ICICI Team - Mend - WhiteSource !!", "activity_date": "2023-05-24 12:09:37", "activity_type": "Meeting", "content_html": "<p>Note activity for ICICI Bank</p>", "plain_text": "Note activity for ICICI Bank", "author_name": "<PERSON>", "author_email": "<EMAIL>", "flow_type": "Adoption", "touchpoint_reason": "FEE", "external_attendees": "", "company": "ICICI", "original_activity_type": "note", "original_meeting_type": "Meeting", "source": "ICICI_MIGRATION"}, "error": "CSV UI automation failed", "timestamp": "2025-05-30T07:41:20.140451", "subject": "ICICI: Discussion with ICICI Team - Mend - WhiteSource !!"}, {"row_number": "256", "activity": {"row_number": "256", "subject": "ICICI: <PERSON><PERSON>", "activity_date": "2023-05-10 05:59:59", "activity_type": "Update", "content_html": "<p>ICICI Bank activity: alert</p>", "plain_text": "ICICI Bank activity: alert", "author_name": "<PERSON>", "author_email": "<EMAIL>", "flow_type": "Standard", "touchpoint_reason": "", "external_attendees": "", "company": "ICICI", "original_activity_type": "alert", "original_meeting_type": "No Meeting Type", "source": "ICICI_MIGRATION"}, "error": "CSV UI automation failed", "timestamp": "2025-05-30T07:41:23.150028", "subject": "ICICI: <PERSON><PERSON>"}, {"row_number": "257", "activity": {"row_number": "257", "subject": "ICICI: Update from Luis- partner manager", "activity_date": "2023-05-09 12:52:33", "activity_type": "Meeting", "content_html": "<p>Note activity for ICICI Bank</p>", "plain_text": "Note activity for ICICI Bank", "author_name": "<PERSON>", "author_email": "<EMAIL>", "flow_type": "Adoption", "touchpoint_reason": "CADENCE", "external_attendees": "", "company": "ICICI", "original_activity_type": "note", "original_meeting_type": "Meeting", "source": "ICICI_MIGRATION"}, "error": "CSV UI automation failed", "timestamp": "2025-05-30T07:41:26.155699", "subject": "ICICI: Update from Luis- partner manager"}, {"row_number": "258", "activity": {"row_number": "258", "subject": "ICICI: <PERSON><PERSON>", "activity_date": "2023-05-06 05:59:59", "activity_type": "Update", "content_html": "<p>ICICI Bank activity: alert</p>", "plain_text": "ICICI Bank activity: alert", "author_name": "<PERSON>", "author_email": "<EMAIL>", "flow_type": "Standard", "touchpoint_reason": "", "external_attendees": "", "company": "ICICI", "original_activity_type": "alert", "original_meeting_type": "No Meeting Type", "source": "ICICI_MIGRATION"}, "error": "CSV UI automation failed", "timestamp": "2025-05-30T07:41:29.160353", "subject": "ICICI: <PERSON><PERSON>"}, {"row_number": "259", "activity": {"row_number": "259", "subject": "ICICI: Repo Integration-Accelerate your remediation", "activity_date": "2023-04-19 15:40:33", "activity_type": "Update", "content_html": "<p>One-time campaign \"Repo Integration-Accelerate your remediation \" was sent to 1 user. \nFrom: <EMAIL></p>", "plain_text": "One-time campaign \"Repo Integration-Accelerate your remediation \" was sent to 1 user. \nFrom: <EMAIL>", "author_name": "<PERSON>", "author_email": "<EMAIL>", "flow_type": "Adoption", "touchpoint_reason": "", "external_attendees": "", "company": "ICICI", "original_activity_type": "campaign_touch", "original_meeting_type": "No Meeting Type", "source": "ICICI_MIGRATION"}, "error": "CSV UI automation failed", "timestamp": "2025-05-30T07:41:32.169081", "subject": "ICICI: Repo Integration-Accelerate your remediation"}, {"row_number": "260", "activity": {"row_number": "260", "subject": "ICICI: RSA 2023 conference", "activity_date": "2023-03-24 16:05:48", "activity_type": "Update", "content_html": "<p>One-time campaign \"RSA 2023 conference\" was sent to 1 user. \nFrom: <EMAIL></p>", "plain_text": "One-time campaign \"RSA 2023 conference\" was sent to 1 user. \nFrom: <EMAIL>", "author_name": "<PERSON>", "author_email": "<EMAIL>", "flow_type": "Adoption", "touchpoint_reason": "", "external_attendees": "", "company": "ICICI", "original_activity_type": "campaign_touch", "original_meeting_type": "No Meeting Type", "source": "ICICI_MIGRATION"}, "error": "CSV UI automation failed", "timestamp": "2025-05-30T07:41:35.174473", "subject": "ICICI: RSA 2023 conference"}, {"row_number": "261", "activity": {"row_number": "261", "subject": "ICICI: <PERSON><PERSON>", "activity_date": "2023-03-24 04:59:59", "activity_type": "Update", "content_html": "<p>ICICI Bank activity: alert</p>", "plain_text": "ICICI Bank activity: alert", "author_name": "<PERSON>", "author_email": "<EMAIL>", "flow_type": "Standard", "touchpoint_reason": "", "external_attendees": "", "company": "ICICI", "original_activity_type": "alert", "original_meeting_type": "No Meeting Type", "source": "ICICI_MIGRATION"}, "error": "CSV UI automation failed", "timestamp": "2025-05-30T07:41:48.180901", "subject": "ICICI: <PERSON><PERSON>"}, {"row_number": "262", "activity": {"row_number": "262", "subject": "ICICI: FEE ticket in place", "activity_date": "2023-03-15 08:46:49", "activity_type": "Meeting", "content_html": "<p>Note activity for ICICI Bank</p>", "plain_text": "Note activity for ICICI Bank", "author_name": "<PERSON>", "author_email": "<EMAIL>", "flow_type": "Adoption", "touchpoint_reason": "FEE", "external_attendees": "", "company": "ICICI", "original_activity_type": "note", "original_meeting_type": "Meeting", "source": "ICICI_MIGRATION"}, "error": "CSV UI automation failed", "timestamp": "2025-05-30T07:41:51.186984", "subject": "ICICI: FEE ticket in place"}, {"row_number": "263", "activity": {"row_number": "263", "subject": "ICICI: FEE ticket in place", "activity_date": "2023-03-15 08:46:49", "activity_type": "Update", "content_html": "<p>ICICI Bank activity: task</p>", "plain_text": "ICICI Bank activity: task", "author_name": "<PERSON>", "author_email": "<EMAIL>", "flow_type": "Adoption", "touchpoint_reason": "", "external_attendees": "", "company": "ICICI", "original_activity_type": "task", "original_meeting_type": "No Meeting Type", "source": "ICICI_MIGRATION"}, "error": "CSV UI automation failed", "timestamp": "2025-05-30T07:41:54.193153", "subject": "ICICI: FEE ticket in place"}, {"row_number": "264", "activity": {"row_number": "264", "subject": "ICICI: FEE ticket in place", "activity_date": "2023-03-01 17:40:12", "activity_type": "Update", "content_html": "<p>ICICI Bank activity: task</p>", "plain_text": "ICICI Bank activity: task", "author_name": "<PERSON>", "author_email": "<EMAIL>", "flow_type": "Adoption", "touchpoint_reason": "", "external_attendees": "", "company": "ICICI", "original_activity_type": "task", "original_meeting_type": "No Meeting Type", "source": "ICICI_MIGRATION"}, "error": "CSV UI automation failed", "timestamp": "2025-05-30T07:41:57.200349", "subject": "ICICI: FEE ticket in place"}, {"row_number": "265", "activity": {"row_number": "265", "subject": "ICICI: Discussion with ICICI Team - Mend - WhiteSource !!", "activity_date": "2023-03-01 17:39:40", "activity_type": "Meeting", "content_html": "<p>Note activity for ICICI Bank</p>", "plain_text": "Note activity for ICICI Bank", "author_name": "<PERSON>", "author_email": "<EMAIL>", "flow_type": "Adoption", "touchpoint_reason": "FEE", "external_attendees": "", "company": "ICICI", "original_activity_type": "note", "original_meeting_type": "Meeting", "source": "ICICI_MIGRATION"}, "error": "CSV UI automation failed", "timestamp": "2025-05-30T07:42:00.205886", "subject": "ICICI: Discussion with ICICI Team - Mend - WhiteSource !!"}, {"row_number": "266", "activity": {"row_number": "266", "subject": "ICICI: ICICI/Meteonic - Next Steps & Alignment", "activity_date": "2023-02-27 11:52:58", "activity_type": "Meeting", "content_html": "<p>Note activity for ICICI Bank</p>", "plain_text": "Note activity for ICICI Bank", "author_name": "<PERSON>", "author_email": "<EMAIL>", "flow_type": "Adoption", "touchpoint_reason": "CADENCE", "external_attendees": "", "company": "ICICI", "original_activity_type": "note", "original_meeting_type": "Meeting", "source": "ICICI_MIGRATION"}, "error": "CSV UI automation failed", "timestamp": "2025-05-30T07:42:03.215303", "subject": "ICICI: ICICI/Meteonic - Next Steps & Alignment"}, {"row_number": "267", "activity": {"row_number": "267", "subject": "ICICI: Sunny to set up a joint meeting?", "activity_date": "2023-02-26 11:59:51", "activity_type": "Meeting", "content_html": "<p>Note activity for ICICI Bank</p>", "plain_text": "Note activity for ICICI Bank", "author_name": "<PERSON>", "author_email": "<EMAIL>", "flow_type": "Adoption", "touchpoint_reason": "CADENCE", "external_attendees": "", "company": "ICICI", "original_activity_type": "note", "original_meeting_type": "Meeting", "source": "ICICI_MIGRATION"}, "error": "CSV UI automation failed", "timestamp": "2025-05-30T07:42:06.224544", "subject": "ICICI: Sunny to set up a joint meeting?"}, {"row_number": "268", "activity": {"row_number": "268", "subject": "ICICI: Sunny to set up a joint meeting?", "activity_date": "2023-02-26 11:59:49", "activity_type": "Update", "content_html": "<p>ICICI Bank activity: task</p>", "plain_text": "ICICI Bank activity: task", "author_name": "<PERSON>", "author_email": "<EMAIL>", "flow_type": "Adoption", "touchpoint_reason": "", "external_attendees": "", "company": "ICICI", "original_activity_type": "task", "original_meeting_type": "No Meeting Type", "source": "ICICI_MIGRATION"}, "error": "CSV UI automation failed", "timestamp": "2025-05-30T07:42:09.229707", "subject": "ICICI: Sunny to set up a joint meeting?"}, {"row_number": "269", "activity": {"row_number": "269", "subject": "ICICI: <PERSON><PERSON>", "activity_date": "2023-02-23 04:59:59", "activity_type": "Update", "content_html": "<p>ICICI Bank activity: alert</p>", "plain_text": "ICICI Bank activity: alert", "author_name": "<PERSON>", "author_email": "<EMAIL>", "flow_type": "Standard", "touchpoint_reason": "", "external_attendees": "", "company": "ICICI", "original_activity_type": "alert", "original_meeting_type": "No Meeting Type", "source": "ICICI_MIGRATION"}, "error": "CSV UI automation failed", "timestamp": "2025-05-30T07:42:12.239675", "subject": "ICICI: <PERSON><PERSON>"}, {"row_number": "270", "activity": {"row_number": "270", "subject": "ICICI: Sales Manager Region", "activity_date": "2023-02-21 18:31:43", "activity_type": "Update", "content_html": "<p>Automated update: Sales Manager Region changed from 'International' to 'International'</p>", "plain_text": "Automated update: Sales Manager Region changed from 'International' to 'International'", "author_name": "<PERSON>", "author_email": "<EMAIL>", "flow_type": "Intelligence", "touchpoint_reason": "", "external_attendees": "", "company": "ICICI", "original_activity_type": "automated_attribute_change", "original_meeting_type": "No Meeting Type", "source": "ICICI_MIGRATION"}, "error": "CSV UI automation failed", "timestamp": "2025-05-30T07:42:15.244085", "subject": "ICICI: Sales Manager Region"}, {"row_number": "271", "activity": {"row_number": "271", "subject": "ICICI: SCA CD Variance Stages", "activity_date": "2023-02-21 18:31:05", "activity_type": "Update", "content_html": "<p>Automated update: SCA CD Variance Stages changed from 'No Data' to 'No Data'</p>", "plain_text": "Automated update: SCA CD Variance Stages changed from 'No Data' to 'No Data'", "author_name": "<PERSON>", "author_email": "<EMAIL>", "flow_type": "Renewal", "touchpoint_reason": "", "external_attendees": "", "company": "ICICI", "original_activity_type": "automated_attribute_change", "original_meeting_type": "No Meeting Type", "source": "ICICI_MIGRATION"}, "error": "CSV UI automation failed", "timestamp": "2025-05-30T07:42:28.250449", "subject": "ICICI: SCA CD Variance Stages"}, {"row_number": "272", "activity": {"row_number": "272", "subject": "ICICI: <PERSON><PERSON>", "activity_date": "2023-02-16 04:59:59", "activity_type": "Update", "content_html": "<p>ICICI Bank activity: alert</p>", "plain_text": "ICICI Bank activity: alert", "author_name": "<PERSON>", "author_email": "<EMAIL>", "flow_type": "Standard", "touchpoint_reason": "", "external_attendees": "", "company": "ICICI", "original_activity_type": "alert", "original_meeting_type": "No Meeting Type", "source": "ICICI_MIGRATION"}, "error": "CSV UI automation failed", "timestamp": "2025-05-30T07:42:31.256556", "subject": "ICICI: <PERSON><PERSON>"}, {"row_number": "273", "activity": {"row_number": "273", "subject": "ICICI: Sunny to set up a joint meeting?", "activity_date": "2023-02-13 12:01:23", "activity_type": "Update", "content_html": "<p>ICICI Bank activity: task</p>", "plain_text": "ICICI Bank activity: task", "author_name": "<PERSON>", "author_email": "<EMAIL>", "flow_type": "Adoption", "touchpoint_reason": "", "external_attendees": "", "company": "ICICI", "original_activity_type": "task", "original_meeting_type": "No Meeting Type", "source": "ICICI_MIGRATION"}, "error": "CSV UI automation failed", "timestamp": "2025-05-30T07:42:34.270133", "subject": "ICICI: Sunny to set up a joint meeting?"}, {"row_number": "274", "activity": {"row_number": "274", "subject": "ICICI: ********- zoom discussions in place", "activity_date": "2023-02-13 12:00:19", "activity_type": "Meeting", "content_html": "<p>Note activity for ICICI Bank</p>", "plain_text": "Note activity for ICICI Bank", "author_name": "<PERSON>", "author_email": "<EMAIL>", "flow_type": "Adoption", "touchpoint_reason": "SUPPORT", "external_attendees": "", "company": "ICICI", "original_activity_type": "note", "original_meeting_type": "Meeting", "source": "ICICI_MIGRATION"}, "error": "CSV UI automation failed", "timestamp": "2025-05-30T07:42:37.282138", "subject": "ICICI: ********- zoom discussions in place"}, {"row_number": "275", "activity": {"row_number": "275", "subject": "ICICI: Mend/Meteonic sync", "activity_date": "2023-02-06 17:26:54", "activity_type": "Meeting", "content_html": "<p>Note activity for ICICI Bank</p>", "plain_text": "Note activity for ICICI Bank", "author_name": "<PERSON>", "author_email": "<EMAIL>", "flow_type": "Adoption", "touchpoint_reason": "CADENCE", "external_attendees": "", "company": "ICICI", "original_activity_type": "note", "original_meeting_type": "Meeting", "source": "ICICI_MIGRATION"}, "error": "CSV UI automation failed", "timestamp": "2025-05-30T07:42:40.291339", "subject": "ICICI: Mend/Meteonic sync"}, {"row_number": "276", "activity": {"row_number": "276", "subject": "ICICI: Case#********- call with Eng", "activity_date": "2023-02-06 14:21:03", "activity_type": "Meeting", "content_html": "<p>Note activity for ICICI Bank</p>", "plain_text": "Note activity for ICICI Bank", "author_name": "<PERSON>", "author_email": "<EMAIL>", "flow_type": "Adoption", "touchpoint_reason": "SUPPORT", "external_attendees": "", "company": "ICICI", "original_activity_type": "note", "original_meeting_type": "Meeting", "source": "ICICI_MIGRATION"}, "error": "CSV UI automation failed", "timestamp": "2025-05-30T07:42:43.300667", "subject": "ICICI: Case#********- call with Eng"}, {"row_number": "277", "activity": {"row_number": "277", "subject": "ICICI: Working to set a meting with a the bank and the BP", "activity_date": "2023-02-02 14:48:15", "activity_type": "Meeting", "content_html": "<p>Note activity for ICICI Bank</p>", "plain_text": "Note activity for ICICI Bank", "author_name": "<PERSON>", "author_email": "<EMAIL>", "flow_type": "Adoption", "touchpoint_reason": "FEE", "external_attendees": "", "company": "ICICI", "original_activity_type": "note", "original_meeting_type": "Meeting", "source": "ICICI_MIGRATION"}, "error": "CSV UI automation failed", "timestamp": "2025-05-30T07:42:46.305304", "subject": "ICICI: Working to set a meting with a the bank and the BP"}, {"row_number": "278", "activity": {"row_number": "278", "subject": "ICICI: Meteonic will do onsite in the bank on Jan25th", "activity_date": "2023-01-23 13:57:49", "activity_type": "Meeting", "content_html": "<p>Note activity for ICICI Bank</p>", "plain_text": "Note activity for ICICI Bank", "author_name": "<PERSON>", "author_email": "<EMAIL>", "flow_type": "Adoption", "touchpoint_reason": "FEE", "external_attendees": "", "company": "ICICI", "original_activity_type": "note", "original_meeting_type": "Meeting", "source": "ICICI_MIGRATION"}, "error": "CSV UI automation failed", "timestamp": "2025-05-30T07:42:49.312634", "subject": "ICICI: Meteonic will do onsite in the bank on Jan25th"}, {"row_number": "279", "activity": {"row_number": "279", "subject": "ICICI: Mend's Malicious Package Communications", "activity_date": "2023-01-19 18:52:23", "activity_type": "Update", "content_html": "<p>One-time campaign \"Mend's Malicious Package Communications \" was sent to 1 user. \nFrom: <PERSON>@mend.io</p>", "plain_text": "One-time campaign \"Mend's Malicious Package Communications \" was sent to 1 user. \nFrom: <PERSON>@mend.io", "author_name": "<PERSON>", "author_email": "<EMAIL>", "flow_type": "Intelligence", "touchpoint_reason": "", "external_attendees": "", "company": "ICICI", "original_activity_type": "campaign_touch", "original_meeting_type": "No Meeting Type", "source": "ICICI_MIGRATION"}, "error": "CSV UI automation failed", "timestamp": "2025-05-30T07:42:52.322104", "subject": "ICICI: Mend's Malicious Package Communications"}, {"row_number": "280", "activity": {"row_number": "280", "subject": "ICICI: No need to touch base", "activity_date": "2023-01-15 17:51:33", "activity_type": "Meeting", "content_html": "<p>Note activity for ICICI Bank</p>", "plain_text": "Note activity for ICICI Bank", "author_name": "<PERSON>", "author_email": "<EMAIL>", "flow_type": "Adoption", "touchpoint_reason": "CADENCE", "external_attendees": "", "company": "ICICI", "original_activity_type": "note", "original_meeting_type": "Meeting", "source": "ICICI_MIGRATION"}, "error": "CSV UI automation failed", "timestamp": "2025-05-30T07:42:55.330771", "subject": "ICICI: No need to touch base"}, {"row_number": "281", "activity": {"row_number": "281", "subject": "ICICI: NPS Send Date", "activity_date": "2023-01-08 05:30:36", "activity_type": "Update", "content_html": "<p>Automated update: NPS Send Date changed from '2023-01-07' to '2023-07-07T05:30:36.215Z'</p>", "plain_text": "Automated update: NPS Send Date changed from '2023-01-07' to '2023-07-07T05:30:36.215Z'", "author_name": "<PERSON>", "author_email": "<EMAIL>", "flow_type": "Nps", "touchpoint_reason": "", "external_attendees": "", "company": "ICICI", "original_activity_type": "automated_attribute_change", "original_meeting_type": "No Meeting Type", "source": "ICICI_MIGRATION"}, "error": "CSV UI automation failed", "timestamp": "2025-05-30T07:43:08.337296", "subject": "ICICI: NPS Send Date"}, {"row_number": "282", "activity": {"row_number": "282", "subject": "ICICI: NPS Informed", "activity_date": "2023-01-08 05:30:36", "activity_type": "Update", "content_html": "<p>Automated update: NPS Informed changed from 'Yes' to 'updated'</p>", "plain_text": "Automated update: NPS Informed changed from 'Yes' to 'updated'", "author_name": "<PERSON>", "author_email": "<EMAIL>", "flow_type": "Nps", "touchpoint_reason": "", "external_attendees": "", "company": "ICICI", "original_activity_type": "automated_attribute_change", "original_meeting_type": "No Meeting Type", "source": "ICICI_MIGRATION"}, "error": "CSV UI automation failed", "timestamp": "2025-05-30T07:43:11.346939", "subject": "ICICI: NPS Informed"}, {"row_number": "283", "activity": {"row_number": "283", "subject": "ICICI: NPS Informed", "activity_date": "2023-01-05 05:30:51", "activity_type": "Update", "content_html": "<p>Automated update: NPS Informed changed from '' to 'Yes'</p>", "plain_text": "Automated update: NPS Informed changed from '' to 'Yes'", "author_name": "<PERSON>", "author_email": "<EMAIL>", "flow_type": "Nps", "touchpoint_reason": "", "external_attendees": "", "company": "ICICI", "original_activity_type": "automated_attribute_change", "original_meeting_type": "No Meeting Type", "source": "ICICI_MIGRATION"}, "error": "CSV UI automation failed", "timestamp": "2025-05-30T07:43:14.354941", "subject": "ICICI: NPS Informed"}, {"row_number": "284", "activity": {"row_number": "284", "subject": "ICICI: Internal sync with AM", "activity_date": "2022-12-29 13:26:20", "activity_type": "Meeting", "content_html": "<p>Note activity for ICICI Bank</p>", "plain_text": "Note activity for ICICI Bank", "author_name": "<PERSON>", "author_email": "<EMAIL>", "flow_type": "Adoption", "touchpoint_reason": "Internal Note", "external_attendees": "", "company": "ICICI", "original_activity_type": "note", "original_meeting_type": "Meeting", "source": "ICICI_MIGRATION"}, "error": "CSV UI automation failed", "timestamp": "2025-05-30T07:43:17.363001", "subject": "ICICI: Internal sync with AM"}, {"row_number": "285", "activity": {"row_number": "285", "subject": "ICICI: Touch Status", "activity_date": "2022-12-20 22:10:34", "activity_type": "Update", "content_html": "<p>Automated update: Touch Status changed from '' to 'updated'</p>", "plain_text": "Automated update: Touch Status changed from '' to 'updated'", "author_name": "<PERSON>", "author_email": "<EMAIL>", "flow_type": "Intelligence", "touchpoint_reason": "", "external_attendees": "", "company": "ICICI", "original_activity_type": "automated_attribute_change", "original_meeting_type": "No Meeting Type", "source": "ICICI_MIGRATION"}, "error": "CSV UI automation failed", "timestamp": "2025-05-30T07:43:20.369283", "subject": "ICICI: Touch Status"}, {"row_number": "286", "activity": {"row_number": "286", "subject": "ICICI: NPS Informed", "activity_date": "2022-12-08 05:30:16", "activity_type": "Update", "content_html": "<p>Automated update: NPS Informed changed from '' to 'updated'</p>", "plain_text": "Automated update: NPS Informed changed from '' to 'updated'", "author_name": "<PERSON>", "author_email": "<EMAIL>", "flow_type": "Nps", "touchpoint_reason": "", "external_attendees": "", "company": "ICICI", "original_activity_type": "automated_attribute_change", "original_meeting_type": "No Meeting Type", "source": "ICICI_MIGRATION"}, "error": "CSV UI automation failed", "timestamp": "2025-05-30T07:43:23.377063", "subject": "ICICI: NPS Informed"}, {"row_number": "287", "activity": {"row_number": "287", "subject": "ICICI: Sales Manager Region", "activity_date": "2022-11-18 13:30:48", "activity_type": "Update", "content_html": "<p>Automated update: Sales Manager Region changed from 'International' to 'International'</p>", "plain_text": "Automated update: Sales Manager Region changed from 'International' to 'International'", "author_name": "<PERSON>", "author_email": "<EMAIL>", "flow_type": "Intelligence", "touchpoint_reason": "", "external_attendees": "", "company": "ICICI", "original_activity_type": "automated_attribute_change", "original_meeting_type": "No Meeting Type", "source": "ICICI_MIGRATION"}, "error": "CSV UI automation failed", "timestamp": "2025-05-30T07:43:26.380615", "subject": "ICICI: Sales Manager Region"}, {"row_number": "288", "activity": {"row_number": "288", "subject": "ICICI: Team Manager Region", "activity_date": "2022-11-18 13:30:44", "activity_type": "Update", "content_html": "<p>Automated update: Team Manager Region changed from 'International' to 'International'</p>", "plain_text": "Automated update: Team Manager Region changed from 'International' to 'International'", "author_name": "<PERSON>", "author_email": "<EMAIL>", "flow_type": "Renewal", "touchpoint_reason": "", "external_attendees": "", "company": "ICICI", "original_activity_type": "automated_attribute_change", "original_meeting_type": "No Meeting Type", "source": "ICICI_MIGRATION"}, "error": "CSV UI automation failed", "timestamp": "2025-05-30T07:43:29.386640", "subject": "ICICI: Team Manager Region"}, {"row_number": "289", "activity": {"row_number": "289", "subject": "ICICI: Team Manager", "activity_date": "2022-11-18 13:30:27", "activity_type": "Update", "content_html": "<p>Automated update: Team Manager changed from '{owner_value=<PERSON><PERSON>, owner_tid_value=<EMAIL>, owner_user_email=<EMAIL>}' to '<EMAIL>'</p>", "plain_text": "Automated update: Team Manager changed from '{owner_value=<PERSON><PERSON> Go<PERSON>, owner_tid_value=<EMAIL>, owner_user_email=<EMAIL>}' to '<EMAIL>'", "author_name": "<PERSON>", "author_email": "<EMAIL>", "flow_type": "Intelligence", "touchpoint_reason": "", "external_attendees": "", "company": "ICICI", "original_activity_type": "automated_attribute_change", "original_meeting_type": "No Meeting Type", "source": "ICICI_MIGRATION"}, "error": "CSV UI automation failed", "timestamp": "2025-05-30T07:43:32.394826", "subject": "ICICI: Team Manager"}, {"row_number": "290", "activity": {"row_number": "290", "subject": "ICICI: SCA CD Variance Stages", "activity_date": "2022-11-18 13:30:16", "activity_type": "Update", "content_html": "<p>Automated update: SCA CD Variance Stages changed from 'No Data' to 'No Data'</p>", "plain_text": "Automated update: SCA CD Variance Stages changed from 'No Data' to 'No Data'", "author_name": "<PERSON>", "author_email": "<EMAIL>", "flow_type": "Renewal", "touchpoint_reason": "", "external_attendees": "", "company": "ICICI", "original_activity_type": "automated_attribute_change", "original_meeting_type": "No Meeting Type", "source": "ICICI_MIGRATION"}, "error": "CSV UI automation failed", "timestamp": "2025-05-30T07:43:35.399387", "subject": "ICICI: SCA CD Variance Stages"}, {"row_number": "291", "activity": {"row_number": "291", "subject": "ICICI: Setup a call with customers and explains our Policies best practice", "activity_date": "2022-07-14 14:35:09", "activity_type": "Update", "content_html": "<p>Note activity for ICICI Bank</p>", "plain_text": "Note activity for ICICI Bank", "author_name": "<PERSON>", "author_email": "<EMAIL>", "flow_type": "Adoption", "touchpoint_reason": "", "external_attendees": "", "company": "ICICI", "original_activity_type": "note", "original_meeting_type": "Update", "source": "ICICI_MIGRATION"}, "error": "CSV UI automation failed", "timestamp": "2025-05-30T07:43:48.406795", "subject": "ICICI: Setup a call with customers and explains our Policies best practice"}, {"row_number": "292", "activity": {"row_number": "292", "subject": "ICICI: Touch Status", "activity_date": "2022-06-15 23:31:37", "activity_type": "Update", "content_html": "<p>Automated update: Touch Status changed from 'Good' to 'Good'</p>", "plain_text": "Automated update: Touch Status changed from 'Good' to 'Good'", "author_name": "<PERSON>", "author_email": "<EMAIL>", "flow_type": "Intelligence", "touchpoint_reason": "", "external_attendees": "", "company": "ICICI", "original_activity_type": "automated_attribute_change", "original_meeting_type": "No Meeting Type", "source": "ICICI_MIGRATION"}, "error": "CSV UI automation failed", "timestamp": "2025-05-30T07:43:51.416520", "subject": "ICICI: Touch Status"}, {"row_number": "293", "activity": {"row_number": "293", "subject": "ICICI: Setup a call with customers and explains our Policies best practice", "activity_date": "2022-06-09 06:24:59", "activity_type": "Email", "content_html": "<p>Note activity for ICICI Bank</p>", "plain_text": "Note activity for ICICI Bank", "author_name": "<PERSON>", "author_email": "<EMAIL>", "flow_type": "Adoption", "touchpoint_reason": "", "external_attendees": "", "company": "ICICI", "original_activity_type": "note", "original_meeting_type": "Email", "source": "ICICI_MIGRATION"}, "error": "CSV UI automation failed", "timestamp": "2025-05-30T07:43:54.421905", "subject": "ICICI: Setup a call with customers and explains our Policies best practice"}, {"row_number": "294", "activity": {"row_number": "294", "subject": "ICICI: Setup a call with customers and explains our Policies best practice", "activity_date": "2022-05-26 14:43:54", "activity_type": "Update", "content_html": "<p>Note activity for ICICI Bank</p>", "plain_text": "Note activity for ICICI Bank", "author_name": "<PERSON>", "author_email": "<EMAIL>", "flow_type": "Adoption", "touchpoint_reason": "", "external_attendees": "", "company": "ICICI", "original_activity_type": "note", "original_meeting_type": "Update", "source": "ICICI_MIGRATION"}, "error": "CSV UI automation failed", "timestamp": "2025-05-30T07:43:57.431471", "subject": "ICICI: Setup a call with customers and explains our Policies best practice"}, {"row_number": "295", "activity": {"row_number": "295", "subject": "ICICI: Outage in app.whitesourcesoftware.com", "activity_date": "2022-05-16 20:58:26", "activity_type": "Update", "content_html": "<p>One-time campaign \"Outage in app.whitesourcesoftware.com\" was sent to 1 user. \nFrom: <EMAIL></p>", "plain_text": "One-time campaign \"Outage in app.whitesourcesoftware.com\" was sent to 1 user. \nFrom: <EMAIL>", "author_name": "<PERSON>", "author_email": "<EMAIL>", "flow_type": "Support", "touchpoint_reason": "", "external_attendees": "", "company": "ICICI", "original_activity_type": "campaign_touch", "original_meeting_type": "No Meeting Type", "source": "ICICI_MIGRATION"}, "error": "CSV UI automation failed", "timestamp": "2025-05-30T07:44:00.436425", "subject": "ICICI: Outage in app.whitesourcesoftware.com"}, {"row_number": "296", "activity": {"row_number": "296", "subject": "ICICI: Touch Status", "activity_date": "2022-05-12 14:31:43", "activity_type": "Update", "content_html": "<p>Automated update: Touch Status changed from 'Violation' to 'Good'</p>", "plain_text": "Automated update: Touch Status changed from 'Violation' to 'Good'", "author_name": "<PERSON>", "author_email": "<EMAIL>", "flow_type": "Intelligence", "touchpoint_reason": "", "external_attendees": "", "company": "ICICI", "original_activity_type": "automated_attribute_change", "original_meeting_type": "No Meeting Type", "source": "ICICI_MIGRATION"}, "error": "CSV UI automation failed", "timestamp": "2025-05-30T07:44:03.441337", "subject": "ICICI: Touch Status"}, {"row_number": "297", "activity": {"row_number": "297", "subject": "ICICI: Check the usage and project number", "activity_date": "2022-05-12 14:22:16", "activity_type": "Meeting", "content_html": "<p>Note activity for ICICI Bank</p>", "plain_text": "Note activity for ICICI Bank", "author_name": "<PERSON>", "author_email": "<EMAIL>", "flow_type": "Adoption", "touchpoint_reason": "", "external_attendees": "", "company": "ICICI", "original_activity_type": "note", "original_meeting_type": "Meeting", "source": "ICICI_MIGRATION"}, "error": "CSV UI automation failed", "timestamp": "2025-05-30T07:44:06.446260", "subject": "ICICI: Check the usage and project number"}, {"row_number": "298", "activity": {"row_number": "298", "subject": "ICICI: CSAT - Spring4Shell, Platinum&Gold", "activity_date": "2022-05-03 14:00:20", "activity_type": "Update", "content_html": "<p>One-time campaign \"CSAT - Spring4Shell, Platinum&Gold\" was sent to 1 user. \nFrom: <EMAIL></p>", "plain_text": "One-time campaign \"CSAT - Spring4Shell, Platinum&Gold\" was sent to 1 user. \nFrom: <EMAIL>", "author_name": "<PERSON>", "author_email": "<EMAIL>", "flow_type": "Intelligence", "touchpoint_reason": "", "external_attendees": "", "company": "ICICI", "original_activity_type": "campaign_touch", "original_meeting_type": "No Meeting Type", "source": "ICICI_MIGRATION"}, "error": "CSV UI automation failed", "timestamp": "2025-05-30T07:44:09.451134", "subject": "ICICI: CSAT - Spring4Shell, Platinum&Gold"}, {"row_number": "299", "activity": {"row_number": "299", "subject": "ICICI: Touch Status", "activity_date": "2022-05-02 06:30:39", "activity_type": "Update", "content_html": "<p>Automated update: Touch Status changed from 'Good' to 'Violation'</p>", "plain_text": "Automated update: Touch Status changed from 'Good' to 'Violation'", "author_name": "<PERSON>", "author_email": "<EMAIL>", "flow_type": "Intelligence", "touchpoint_reason": "", "external_attendees": "", "company": "ICICI", "original_activity_type": "automated_attribute_change", "original_meeting_type": "No Meeting Type", "source": "ICICI_MIGRATION"}, "error": "CSV UI automation failed", "timestamp": "2025-05-30T07:44:12.461126", "subject": "ICICI: Touch Status"}, {"row_number": "300", "activity": {"row_number": "300", "subject": "ICICI: Team Manager Region", "activity_date": "2022-04-26 22:13:37", "activity_type": "Update", "content_html": "<p>Automated update: Team Manager Region changed from '' to 'International'</p>", "plain_text": "Automated update: Team Manager Region changed from '' to 'International'", "author_name": "<PERSON>", "author_email": "<EMAIL>", "flow_type": "Renewal", "touchpoint_reason": "", "external_attendees": "", "company": "ICICI", "original_activity_type": "automated_attribute_change", "original_meeting_type": "No Meeting Type", "source": "ICICI_MIGRATION"}, "error": "CSV UI automation failed", "timestamp": "2025-05-30T07:44:15.466871", "subject": "ICICI: Team Manager Region"}, {"row_number": "301", "activity": {"row_number": "301", "subject": "ICICI: Spring4Shell HIGH SEVERITY Vulnerability in Spring core - Gold & Platinum", "activity_date": "2022-03-31 15:40:56", "activity_type": "Update", "content_html": "<p>One-time campaign \"Spring4Shell HIGH SEVERITY Vulnerability in Spring core - Gold & Platinum\" was sent to 1 user. \nFrom: <EMAIL></p>", "plain_text": "One-time campaign \"Spring4Shell HIGH SEVERITY Vulnerability in Spring core - Gold & Platinum\" was sent to 1 user. \nFrom: <EMAIL>", "author_name": "<PERSON>", "author_email": "<EMAIL>", "flow_type": "Support", "touchpoint_reason": "", "external_attendees": "", "company": "ICICI", "original_activity_type": "campaign_touch", "original_meeting_type": "No Meeting Type", "source": "ICICI_MIGRATION"}, "error": "CSV UI automation failed", "timestamp": "2025-05-30T07:44:28.479625", "subject": "ICICI: Spring4Shell HIGH SEVERITY Vulnerability in Spring core - Gold & Platinum"}, {"row_number": "302", "activity": {"row_number": "302", "subject": "ICICI: <PERSON><PERSON>", "activity_date": "2022-03-18 04:59:59", "activity_type": "Update", "content_html": "<p>ICICI Bank activity: alert</p>", "plain_text": "ICICI Bank activity: alert", "author_name": "<PERSON>", "author_email": "<EMAIL>", "flow_type": "Standard", "touchpoint_reason": "", "external_attendees": "", "company": "ICICI", "original_activity_type": "alert", "original_meeting_type": "No Meeting Type", "source": "ICICI_MIGRATION"}, "error": "CSV UI automation failed", "timestamp": "2025-05-30T07:44:31.486859", "subject": "ICICI: <PERSON><PERSON>"}, {"row_number": "303", "activity": {"row_number": "303", "subject": "ICICI: Touch Status", "activity_date": "2022-03-17 15:30:47", "activity_type": "Update", "content_html": "<p>Automated update: Touch Status changed from 'Violation' to 'Good'</p>", "plain_text": "Automated update: Touch Status changed from 'Violation' to 'Good'", "author_name": "<PERSON>", "author_email": "<EMAIL>", "flow_type": "Intelligence", "touchpoint_reason": "", "external_attendees": "", "company": "ICICI", "original_activity_type": "automated_attribute_change", "original_meeting_type": "No Meeting Type", "source": "ICICI_MIGRATION"}, "error": "CSV UI automation failed", "timestamp": "2025-05-30T07:44:34.496051", "subject": "ICICI: Touch Status"}, {"row_number": "304", "activity": {"row_number": "304", "subject": "ICICI: VBA migration", "activity_date": "2022-03-17 15:22:26", "activity_type": "Meeting", "content_html": "<p>Note activity for ICICI Bank</p>", "plain_text": "Note activity for ICICI Bank", "author_name": "<PERSON>", "author_email": "<EMAIL>", "flow_type": "Adoption", "touchpoint_reason": "", "external_attendees": "", "company": "ICICI", "original_activity_type": "note", "original_meeting_type": "Meeting", "source": "ICICI_MIGRATION"}, "error": "CSV UI automation failed", "timestamp": "2025-05-30T07:44:37.502405", "subject": "ICICI: VBA migration"}, {"row_number": "305", "activity": {"row_number": "305", "subject": "ICICI: Plz check with the usage is low", "activity_date": "2022-03-17 13:46:13", "activity_type": "Update", "content_html": "<p>Note activity for ICICI Bank</p>", "plain_text": "Note activity for ICICI Bank", "author_name": "<PERSON>", "author_email": "<EMAIL>", "flow_type": "Adoption", "touchpoint_reason": "", "external_attendees": "", "company": "ICICI", "original_activity_type": "note", "original_meeting_type": "Update", "source": "ICICI_MIGRATION"}, "error": "CSV UI automation failed", "timestamp": "2025-05-30T07:44:40.509807", "subject": "ICICI: Plz check with the usage is low"}, {"row_number": "306", "activity": {"row_number": "306", "subject": "ICICI: March 2022 Newsletter- Dedicated", "activity_date": "2022-03-03 21:38:56", "activity_type": "Update", "content_html": "<p>One-time campaign \"March 2022 Newsletter- Dedicated \" was sent to 1 user. \nFrom: <EMAIL></p>", "plain_text": "One-time campaign \"March 2022 Newsletter- Dedicated \" was sent to 1 user. \nFrom: <EMAIL>", "author_name": "<PERSON>", "author_email": "<EMAIL>", "flow_type": "Adoption", "touchpoint_reason": "", "external_attendees": "", "company": "ICICI", "original_activity_type": "campaign_touch", "original_meeting_type": "No Meeting Type", "source": "ICICI_MIGRATION"}, "error": "CSV UI automation failed", "timestamp": "2025-05-30T07:44:43.516299", "subject": "ICICI: March 2022 Newsletter- Dedicated"}, {"row_number": "307", "activity": {"row_number": "307", "subject": "ICICI: <PERSON><PERSON>", "activity_date": "2022-02-23 04:59:59", "activity_type": "Update", "content_html": "<p>ICICI Bank activity: alert</p>", "plain_text": "ICICI Bank activity: alert", "author_name": "<PERSON>", "author_email": "<EMAIL>", "flow_type": "Standard", "touchpoint_reason": "", "external_attendees": "", "company": "ICICI", "original_activity_type": "alert", "original_meeting_type": "No Meeting Type", "source": "ICICI_MIGRATION"}, "error": "CSV UI automation failed", "timestamp": "2025-05-30T07:44:46.523780", "subject": "ICICI: <PERSON><PERSON>"}, {"row_number": "308", "activity": {"row_number": "308", "subject": "ICICI: BP customer- no touch base is needed", "activity_date": "2022-02-22 16:21:47", "activity_type": "Update", "content_html": "<p>Note activity for ICICI Bank</p>", "plain_text": "Note activity for ICICI Bank", "author_name": "<PERSON>", "author_email": "<EMAIL>", "flow_type": "Adoption", "touchpoint_reason": "", "external_attendees": "", "company": "ICICI", "original_activity_type": "note", "original_meeting_type": "Update", "source": "ICICI_MIGRATION"}, "error": "CSV UI automation failed", "timestamp": "2025-05-30T07:44:49.530207", "subject": "ICICI: BP customer- no touch base is needed"}, {"row_number": "309", "activity": {"row_number": "309", "subject": "ICICI: Touch Status", "activity_date": "2022-01-24 15:21:36", "activity_type": "Update", "content_html": "<p>Automated update: Touch Status changed from '' to 'Violation'</p>", "plain_text": "Automated update: Touch Status changed from '' to 'Violation'", "author_name": "<PERSON>", "author_email": "<EMAIL>", "flow_type": "Intelligence", "touchpoint_reason": "", "external_attendees": "", "company": "ICICI", "original_activity_type": "automated_attribute_change", "original_meeting_type": "No Meeting Type", "source": "ICICI_MIGRATION"}, "error": "CSV UI automation failed", "timestamp": "2025-05-30T07:44:52.537369", "subject": "ICICI: Touch Status"}, {"row_number": "310", "activity": {"row_number": "310", "subject": "ICICI: Customer Communication Regarding LBA to VBA Migration - SCA-ICICI Bank - for SCA-ICICI Bank", "activity_date": "2022-01-17 10:22:49", "activity_type": "Email", "content_html": "<p>Note activity for ICICI Bank</p>", "plain_text": "Note activity for ICICI Bank", "author_name": "<PERSON>", "author_email": "<EMAIL>", "flow_type": "Intelligence", "touchpoint_reason": "", "external_attendees": "", "company": "ICICI", "original_activity_type": "note", "original_meeting_type": "Email", "source": "ICICI_MIGRATION"}, "error": "CSV UI automation failed", "timestamp": "2025-05-30T07:44:55.542303", "subject": "ICICI: Customer Communication Regarding LBA to VBA Migration - SCA-ICICI Bank - for SCA-ICICI Bank"}, {"row_number": "311", "activity": {"row_number": "311", "subject": "ICICI: Team Manager", "activity_date": "2022-01-04 21:11:57", "activity_type": "Update", "content_html": "<p>Automated update: Team Manager changed from '{}' to '<EMAIL>'</p>", "plain_text": "Automated update: Team Manager changed from '{}' to '<EMAIL>'", "author_name": "<PERSON>", "author_email": "<EMAIL>", "flow_type": "Intelligence", "touchpoint_reason": "", "external_attendees": "", "company": "ICICI", "original_activity_type": "automated_attribute_change", "original_meeting_type": "No Meeting Type", "source": "ICICI_MIGRATION"}, "error": "CSV UI automation failed", "timestamp": "2025-05-30T07:45:08.551993", "subject": "ICICI: Team Manager"}, {"row_number": "312", "activity": {"row_number": "312", "subject": "ICICI: <PERSON><PERSON>", "activity_date": "2022-01-02 04:59:59", "activity_type": "Update", "content_html": "<p>ICICI Bank activity: alert</p>", "plain_text": "ICICI Bank activity: alert", "author_name": "<PERSON>", "author_email": "<EMAIL>", "flow_type": "Standard", "touchpoint_reason": "", "external_attendees": "", "company": "ICICI", "original_activity_type": "alert", "original_meeting_type": "No Meeting Type", "source": "ICICI_MIGRATION"}, "error": "CSV UI automation failed", "timestamp": "2025-05-30T07:45:11.556594", "subject": "ICICI: <PERSON><PERSON>"}, {"row_number": "313", "activity": {"row_number": "313", "subject": "ICICI: Log4j Vulnerability webinar Dedicated CSM", "activity_date": "2021-12-16 01:58:42", "activity_type": "Update", "content_html": "<p>One-time campaign \"Log4j Vulnerability webinar Dedicated CSM \" was sent to 1 user. \nFrom: <EMAIL></p>", "plain_text": "One-time campaign \"Log4j Vulnerability webinar Dedicated CSM \" was sent to 1 user. \nFrom: <EMAIL>", "author_name": "<PERSON>", "author_email": "<EMAIL>", "flow_type": "Adoption", "touchpoint_reason": "", "external_attendees": "", "company": "ICICI", "original_activity_type": "campaign_touch", "original_meeting_type": "No Meeting Type", "source": "ICICI_MIGRATION"}, "error": "CSV UI automation failed", "timestamp": "2025-05-30T07:45:14.562490", "subject": "ICICI: Log4j Vulnerability webinar Dedicated CSM"}, {"row_number": "314", "activity": {"row_number": "314", "subject": "ICICI: Vulnerability in Apache Log4j2 library Part 2  Dedicated CSM Accounts", "activity_date": "2021-12-15 01:02:22", "activity_type": "Update", "content_html": "<p>One-time campaign \"Vulnerability in Apache Log4j2 library Part 2  Dedicated CSM Accounts\" was sent to 1 user. \nFrom: <EMAIL></p>", "plain_text": "One-time campaign \"Vulnerability in Apache Log4j2 library Part 2  Dedicated CSM Accounts\" was sent to 1 user. \nFrom: <EMAIL>", "author_name": "<PERSON>", "author_email": "<EMAIL>", "flow_type": "Adoption", "touchpoint_reason": "", "external_attendees": "", "company": "ICICI", "original_activity_type": "campaign_touch", "original_meeting_type": "No Meeting Type", "source": "ICICI_MIGRATION"}, "error": "CSV UI automation failed", "timestamp": "2025-05-30T07:45:17.571783", "subject": "ICICI: Vulnerability in Apache Log4j2 library Part 2  Dedicated CSM Accounts"}, {"row_number": "315", "activity": {"row_number": "315", "subject": "ICICI: Severity Vulnerability (CVE-2021-44228) in the Log4j2 -Dedicated CSM", "activity_date": "2021-12-11 02:01:05", "activity_type": "Update", "content_html": "<p>One-time campaign \"Severity Vulnerability (CVE-2021-44228) in the Log4j2 -Dedicated CSM\" was sent to 1 user. \nFrom: <EMAIL></p>", "plain_text": "One-time campaign \"Severity Vulnerability (CVE-2021-44228) in the Log4j2 -Dedicated CSM\" was sent to 1 user. \nFrom: <EMAIL>", "author_name": "<PERSON>", "author_email": "<EMAIL>", "flow_type": "Support", "touchpoint_reason": "", "external_attendees": "", "company": "ICICI", "original_activity_type": "campaign_touch", "original_meeting_type": "No Meeting Type", "source": "ICICI_MIGRATION"}, "error": "CSV UI automation failed", "timestamp": "2025-05-30T07:45:20.578479", "subject": "ICICI: Severity Vulnerability (CVE-2021-44228) in the Log4j2 -Dedicated CSM"}, {"row_number": "316", "activity": {"row_number": "316", "subject": "ICICI: Customer Communication Regarding LBA to VBA Migration - SCA-ICICI Bank", "activity_date": "2021-12-08 14:43:13", "activity_type": "Update", "content_html": "<p>Note activity for ICICI Bank</p>", "plain_text": "Note activity for ICICI Bank", "author_name": "<PERSON>", "author_email": "<EMAIL>", "flow_type": "Adoption", "touchpoint_reason": "", "external_attendees": "", "company": "ICICI", "original_activity_type": "note", "original_meeting_type": "Update", "source": "ICICI_MIGRATION"}, "error": "CSV UI automation failed", "timestamp": "2025-05-30T07:45:23.584815", "subject": "ICICI: Customer Communication Regarding LBA to VBA Migration - SCA-ICICI Bank"}, {"row_number": "317", "activity": {"row_number": "317", "subject": "ICICI: Contact SCA-ICICI Bank about moving to VBA", "activity_date": "2021-12-06 11:39:41", "activity_type": "Update", "content_html": "<p>Note activity for ICICI Bank</p>", "plain_text": "Note activity for ICICI Bank", "author_name": "<PERSON>", "author_email": "<EMAIL>", "flow_type": "Intelligence", "touchpoint_reason": "", "external_attendees": "", "company": "ICICI", "original_activity_type": "note", "original_meeting_type": "Update", "source": "ICICI_MIGRATION"}, "error": "CSV UI automation failed", "timestamp": "2025-05-30T07:45:26.588789", "subject": "ICICI: Contact SCA-ICICI Bank about moving to VBA"}, {"row_number": "318", "activity": {"row_number": "318", "subject": "ICICI: <PERSON><PERSON>", "activity_date": "2021-12-03 04:59:59", "activity_type": "Update", "content_html": "<p>ICICI Bank activity: alert</p>", "plain_text": "ICICI Bank activity: alert", "author_name": "<PERSON>", "author_email": "<EMAIL>", "flow_type": "Standard", "touchpoint_reason": "", "external_attendees": "", "company": "ICICI", "original_activity_type": "alert", "original_meeting_type": "No Meeting Type", "source": "ICICI_MIGRATION"}, "error": "CSV UI automation failed", "timestamp": "2025-05-30T07:45:29.596626", "subject": "ICICI: <PERSON><PERSON>"}, {"row_number": "319", "activity": {"row_number": "319", "subject": "ICICI: SCA CD Variance Stages", "activity_date": "2021-11-19 16:37:43", "activity_type": "Update", "content_html": "<p>Automated update: SCA CD Variance Stages changed from '' to 'No Data'</p>", "plain_text": "Automated update: SCA CD Variance Stages changed from '' to 'No Data'", "author_name": "<PERSON>", "author_email": "<EMAIL>", "flow_type": "Renewal", "touchpoint_reason": "", "external_attendees": "", "company": "ICICI", "original_activity_type": "automated_attribute_change", "original_meeting_type": "No Meeting Type", "source": "ICICI_MIGRATION"}, "error": "CSV UI automation failed", "timestamp": "2025-05-30T07:45:32.604257", "subject": "ICICI: SCA CD Variance Stages"}, {"row_number": "320", "activity": {"row_number": "320", "subject": "ICICI: Partner account", "activity_date": "2021-11-18 14:37:18", "activity_type": "Update", "content_html": "<p>Note activity for ICICI Bank</p>", "plain_text": "Note activity for ICICI Bank", "author_name": "<PERSON>", "author_email": "<EMAIL>", "flow_type": "Adoption", "touchpoint_reason": "", "external_attendees": "", "company": "ICICI", "original_activity_type": "note", "original_meeting_type": "Update", "source": "ICICI_MIGRATION"}, "error": "CSV UI automation failed", "timestamp": "2025-05-30T07:45:35.611465", "subject": "ICICI: Partner account"}, {"row_number": "321", "activity": {"row_number": "321", "subject": "ICICI: Sales Manager Region", "activity_date": "2021-10-28 22:12:12", "activity_type": "Update", "content_html": "<p>Automated update: Sales Manager Region changed from '' to 'International'</p>", "plain_text": "Automated update: Sales Manager Region changed from '' to 'International'", "author_name": "<PERSON>", "author_email": "<EMAIL>", "flow_type": "Intelligence", "touchpoint_reason": "", "external_attendees": "", "company": "ICICI", "original_activity_type": "automated_attribute_change", "original_meeting_type": "No Meeting Type", "source": "ICICI_MIGRATION"}, "error": "CSV UI automation failed", "timestamp": "2025-05-30T07:45:48.623741", "subject": "ICICI: Sales Manager Region"}, {"row_number": "322", "activity": {"row_number": "322", "subject": "ICICI: <PERSON><PERSON>", "activity_date": "2021-10-23 05:59:59", "activity_type": "Update", "content_html": "<p>ICICI Bank activity: alert</p>", "plain_text": "ICICI Bank activity: alert", "author_name": "<PERSON>", "author_email": "<EMAIL>", "flow_type": "Standard", "touchpoint_reason": "", "external_attendees": "", "company": "ICICI", "original_activity_type": "alert", "original_meeting_type": "No Meeting Type", "source": "ICICI_MIGRATION"}, "error": "CSV UI automation failed", "timestamp": "2025-05-30T07:45:51.632989", "subject": "ICICI: <PERSON><PERSON>"}]
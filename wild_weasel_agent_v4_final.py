#!/usr/bin/env python3
"""
Wild Weasel UI Automation v4 Final - Enhanced for Gainsight Activity Logging
This script automates the process of logging email activities into Gainsight
by reading from a converted JSON file and properly filling form fields.

Author: Wild Weasel Agent
Version: 4.0 Final
Date: 2025-05-29
"""

import asyncio
import json
import time
import logging
from datetime import datetime
from playwright.async_api import async_playwright, TimeoutError as PlaywrightTimeoutError
from bs4 import BeautifulSoup
import re

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('/Users/<USER>/Desktop/wild_weasel_gainsight_migration/wild_weasel_automation.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

# Configuration
GAINSIGHT_LOGIN_URL = "https://demo-wigmore.gainsightcloud.com/"
GAINSIGHT_USERNAME = "your_username"  # Replace with actual username
GAINSIGHT_PASSWORD = "your_password"  # Replace with actual password
JSON_FILE_PATH = "/Users/<USER>/Desktop/wild_weasel_gainsight_migration/converted_gainsight_activities.json"
DELAY_BETWEEN_ACTIVITIES = 3  # seconds between each activity logging
MAX_RETRIES = 3

class GainsightAutomator:
    def __init__(self):
        self.page = None
        self.context = None
        self.browser = None
        self.activities = []
        
    async def load_activities(self):
        """Load activities from the converted JSON file"""
        try:
            with open(JSON_FILE_PATH, 'r', encoding='utf-8') as f:
                self.activities = json.load(f)
            logger.info(f"Loaded {len(self.activities)} activities from {JSON_FILE_PATH}")
            return True
        except Exception as e:
            logger.error(f"Failed to load activities: {e}")
            return False
    
    def clean_text_for_input(self, text):
        """Clean text for form input - remove excessive whitespace and HTML remnants"""
        if not text:
            return ""
        
        # Remove any remaining HTML tags
        clean_text = BeautifulSoup(text, "html.parser").get_text()
        
        # Normalize whitespace
        clean_text = re.sub(r'\s+', ' ', clean_text)
        
        # Remove any special characters that might cause issues
        clean_text = clean_text.strip()
        
        return clean_text
    
    def format_notes_content(self, activity):
        """Format the notes content in a structured way"""
        subject = activity.get('subject', '')
        author = activity.get('author', '')
        author_email = activity.get('authorEmail', '')
        activity_date = activity.get('activityDate', '')
        plain_text = activity.get('plainText', '')
        
        # Convert timestamp to readable date
        try:
            if activity_date:
                if isinstance(activity_date, (int, float)):
                    date_str = datetime.fromtimestamp(activity_date / 1000).strftime('%Y-%m-%d %H:%M:%S')
                else:
                    date_str = str(activity_date)
            else:
                date_str = "Unknown"
        except:
            date_str = "Unknown"
        
        # Format the notes content
        notes_content = f"""
Email Activity - Migrated from Totango

Subject: {subject}

Author: {author} ({author_email})
Date: {date_str}

Content:
{self.clean_text_for_input(plain_text)}

---
Migration Source: Wild Weasel v4.0 Final
Migration Date: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
        """.strip()
        
        return notes_content

    async def login_to_gainsight(self):
        """Login to Gainsight"""
        try:
            logger.info("Navigating to Gainsight login page...")
            await self.page.goto(GAINSIGHT_LOGIN_URL, wait_until="networkidle", timeout=60000)
            
            # Wait for login form elements
            await self.page.wait_for_selector('input[type="email"], input[name="username"], input[id="username"]', timeout=30000)
            
            # Try different possible selectors for username field
            username_selectors = [
                'input[type="email"]',
                'input[name="username"]', 
                'input[id="username"]',
                'input[placeholder*="email"]',
                'input[placeholder*="username"]'
            ]
            
            username_filled = False
            for selector in username_selectors:
                try:
                    await self.page.fill(selector, GAINSIGHT_USERNAME)
                    logger.info(f"Username filled using selector: {selector}")
                    username_filled = True
                    break
                except:
                    continue
            
            if not username_filled:
                raise Exception("Could not find username field")
            
            # Try different possible selectors for password field
            password_selectors = [
                'input[type="password"]',
                'input[name="password"]',
                'input[id="password"]'
            ]
            
            password_filled = False
            for selector in password_selectors:
                try:
                    await self.page.fill(selector, GAINSIGHT_PASSWORD)
                    logger.info(f"Password filled using selector: {selector}")
                    password_filled = True
                    break
                except:
                    continue
            
            if not password_filled:
                raise Exception("Could not find password field")
            
            # Click login button
            login_button_selectors = [
                'button[type="submit"]',
                'input[type="submit"]',
                'button:has-text("Sign In")',
                'button:has-text("Login")',
                'button:has-text("Log In")',
                '.login-button',
                '#login-button'
            ]
            
            login_clicked = False
            for selector in login_button_selectors:
                try:
                    await self.page.click(selector)
                    logger.info(f"Login button clicked using selector: {selector}")
                    login_clicked = True
                    break
                except:
                    continue
            
            if not login_clicked:
                raise Exception("Could not find login button")
            
            # Wait for login to complete
            await self.page.wait_for_load_state("networkidle", timeout=60000)
            
            # Check if we're on the dashboard or main page
            await asyncio.sleep(5)
            current_url = self.page.url
            logger.info(f"Login completed. Current URL: {current_url}")
            
            return True
            
        except Exception as e:
            logger.error(f"Login failed: {e}")
            await self.page.screenshot(path="login_error.png")
            return False

    async def navigate_to_company_c360(self, company_name="ICICI"):
        """Navigate to the company's C360 page"""
        try:
            logger.info(f"Navigating to {company_name} C360 page...")
            
            # Look for search functionality
            search_selectors = [
                'input[placeholder*="Search"]',
                'input[placeholder*="search"]',
                'input[type="search"]',
                '.search-input',
                '#search-input',
                '[data-testid="search"]'
            ]
            
            search_found = False
            for selector in search_selectors:
                try:
                    await self.page.wait_for_selector(selector, timeout=10000)
                    await self.page.fill(selector, company_name)
                    await self.page.press(selector, "Enter")
                    logger.info(f"Search performed using selector: {selector}")
                    search_found = True
                    break
                except:
                    continue
            
            if search_found:
                # Wait for search results and click on the company
                await asyncio.sleep(3)
                
                company_link_selectors = [
                    f'a:has-text("{company_name}")',
                    f'[title*="{company_name}"]',
                    f'[data-label*="{company_name}"]',
                    '.company-name',
                    '.account-name'
                ]
                
                for selector in company_link_selectors:
                    try:
                        await self.page.click(selector)
                        logger.info(f"Company link clicked using selector: {selector}")
                        break
                    except:
                        continue
            else:
                # Try direct navigation to C360 if search fails
                logger.warning("Search not found, trying alternative navigation methods...")
                
                # Look for Companies/Accounts menu
                menu_selectors = [
                    'text=Companies',
                    'text=Accounts', 
                    'text=Customers',
                    '[title="Companies"]',
                    '[title="Accounts"]'
                ]
                
                for selector in menu_selectors:
                    try:
                        await self.page.click(selector)
                        await asyncio.sleep(2)
                        
                        # Now search for the specific company
                        await self.page.fill('input[type="search"], .search-input', company_name)
                        await self.page.press('input[type="search"], .search-input', "Enter")
                        await asyncio.sleep(3)
                        
                        # Click on the company
                        await self.page.click(f'text="{company_name}"')
                        logger.info(f"Navigated to {company_name} via menu")
                        break
                    except:
                        continue
            
            # Wait for C360 page to load
            await self.page.wait_for_load_state("networkidle", timeout=30000)
            await asyncio.sleep(3)
            
            logger.info(f"Successfully navigated to {company_name} C360 page")
            return True
            
        except Exception as e:
            logger.error(f"Failed to navigate to C360: {e}")
            await self.page.screenshot(path="c360_navigation_error.png")
            return False

    async def log_email_activity(self, activity):
        """Log a single email activity"""
        try:
            activity_id = activity.get('id', 'Unknown')
            subject = activity.get('subject', '')
            logger.info(f"Logging activity {activity_id}: {subject[:50]}...")
            
            # Look for "Log Activity" or similar button
            log_activity_selectors = [
                'button:has-text("Log Activity")',
                'button:has-text("Add Activity")',
                'button:has-text("New Activity")',
                'button:has-text("Log")',
                '[data-testid="log-activity"]',
                '.log-activity-button',
                '#log-activity',
                'button[title*="Log"]',
                'button[title*="Activity"]'
            ]
            
            activity_button_clicked = False
            for selector in log_activity_selectors:
                try:
                    await self.page.wait_for_selector(selector, timeout=10000)
                    await self.page.click(selector)
                    logger.info(f"Log Activity button clicked using selector: {selector}")
                    activity_button_clicked = True
                    break
                except:
                    continue
            
            if not activity_button_clicked:
                # Try scrolling and looking again
                await self.page.evaluate("window.scrollTo(0, document.body.scrollHeight)")
                await asyncio.sleep(2)
                
                for selector in log_activity_selectors:
                    try:
                        await self.page.click(selector)
                        logger.info(f"Log Activity button clicked after scroll using selector: {selector}")
                        activity_button_clicked = True
                        break
                    except:
                        continue
            
            if not activity_button_clicked:
                raise Exception("Could not find Log Activity button")
            
            # Wait for the activity form to appear
            await asyncio.sleep(3)
            
            # Select Email activity type
            email_type_selectors = [
                'button:has-text("Email")',
                'option:has-text("Email")',
                '[data-value="Email"]',
                '[value="EMAIL"]',
                'text=Email',
                '.activity-type-email'
            ]
            
            email_selected = False
            for selector in email_type_selectors:
                try:
                    await self.page.click(selector)
                    logger.info(f"Email activity type selected using selector: {selector}")
                    email_selected = True
                    break
                except:
                    continue
            
            if not email_selected:
                logger.warning("Could not select Email type, proceeding with default")
            
            await asyncio.sleep(2)
            
            # Fill in the subject field
            subject_text = self.clean_text_for_input(subject)
            subject_selectors = [
                'input[placeholder*="Subject"]',
                'input[name*="subject"]',
                'input[id*="subject"]',
                'input[label*="Subject"]',
                '.subject-input',
                '#subject',
                'input[type="text"]:near(label:has-text("Subject"))'
            ]
            
            subject_filled = False
            for selector in subject_selectors:
                try:
                    await self.page.fill(selector, subject_text)
                    logger.info(f"Subject filled using selector: {selector}")
                    subject_filled = True
                    break
                except:
                    continue
            
            if not subject_filled:
                logger.warning("Could not fill subject field")
            
            # Fill in the notes/content field
            notes_content = self.format_notes_content(activity)
            notes_selectors = [
                'textarea[placeholder*="Notes"]',
                'textarea[placeholder*="notes"]',
                'textarea[name*="notes"]',
                'textarea[name*="content"]',
                'textarea[id*="notes"]',
                'textarea[id*="content"]',
                '.notes-textarea',
                '.content-textarea',
                '#notes',
                '#content',
                'textarea:near(label:has-text("Notes"))',
                'textarea:near(label:has-text("Content"))',
                'div[contenteditable="true"]'  # For rich text editors
            ]
            
            notes_filled = False
            for selector in notes_selectors:
                try:
                    if 'contenteditable' in selector:
                        await self.page.click(selector)
                        await self.page.evaluate(f"document.querySelector('{selector}').innerHTML = `{notes_content.replace('`', '\\`')}`")
                    else:
                        await self.page.fill(selector, notes_content)
                    logger.info(f"Notes filled using selector: {selector}")
                    notes_filled = True
                    break
                except Exception as e:
                    logger.debug(f"Failed to fill notes with selector {selector}: {e}")
                    continue
            
            if not notes_filled:
                logger.warning("Could not fill notes field")
            
            await asyncio.sleep(2)
            
            # Save/Submit the activity
            save_selectors = [
                'button:has-text("Save")',
                'button:has-text("Submit")',
                'button:has-text("Log Activity")',
                'button:has-text("Create")',
                'button[type="submit"]',
                '.save-button',
                '.submit-button',
                '#save',
                '#submit'
            ]
            
            saved = False
            for selector in save_selectors:
                try:
                    await self.page.click(selector)
                    logger.info(f"Activity saved using selector: {selector}")
                    saved = True
                    break
                except:
                    continue
            
            if not saved:
                logger.warning("Could not find save button")
                return False
            
            # Wait for save to complete
            await asyncio.sleep(3)
            
            logger.info(f"Successfully logged activity {activity_id}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to log activity {activity.get('id', 'Unknown')}: {e}")
            await self.page.screenshot(path=f"activity_error_{activity.get('id', 'unknown')}.png")
            return False

    async def run_automation(self):
        """Main automation runner"""
        try:
            # Load activities
            if not await self.load_activities():
                return False
            
            # Start browser
            playwright = await async_playwright().start()
            self.browser = await playwright.chromium.launch(
                headless=False,  # Set to True for headless operation
                slow_mo=1000     # Slow down for visibility
            )
            
            self.context = await self.browser.new_context(
                user_agent="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36"
            )
            
            self.page = await self.context.new_page()
            
            # Login to Gainsight
            if not await self.login_to_gainsight():
                return False
            
            # Navigate to company C360
            if not await self.navigate_to_company_c360():
                return False
            
            # Process each activity
            success_count = 0
            total_count = len(self.activities)
            
            for i, activity in enumerate(self.activities, 1):
                logger.info(f"Processing activity {i}/{total_count}")
                
                retry_count = 0
                while retry_count < MAX_RETRIES:
                    try:
                        if await self.log_email_activity(activity):
                            success_count += 1
                            break
                        else:
                            retry_count += 1
                            if retry_count < MAX_RETRIES:
                                logger.warning(f"Retrying activity {i} (attempt {retry_count + 1})")
                                await asyncio.sleep(2)
                    except Exception as e:
                        retry_count += 1
                        logger.error(f"Error on activity {i}, attempt {retry_count}: {e}")
                        if retry_count < MAX_RETRIES:
                            await asyncio.sleep(2)
                
                # Delay between activities
                if i < total_count:
                    logger.info(f"Waiting {DELAY_BETWEEN_ACTIVITIES} seconds before next activity...")
                    await asyncio.sleep(DELAY_BETWEEN_ACTIVITIES)
            
            logger.info(f"Automation completed. Successfully processed {success_count}/{total_count} activities")
            return True
            
        except Exception as e:
            logger.error(f"Automation failed: {e}")
            return False
        
        finally:
            if self.browser:
                await self.browser.close()

async def main():
    """Main entry point"""
    automator = GainsightAutomator()
    
    logger.info("=" * 60)
    logger.info("Wild Weasel UI Automation v4.0 Final Starting...")
    logger.info("=" * 60)
    
    success = await automator.run_automation()
    
    if success:
        logger.info("=" * 60)
        logger.info("Wild Weasel Mission Completed Successfully!")
        logger.info("All email activities have been migrated to Gainsight")
        logger.info("=" * 60)
    else:
        logger.error("=" * 60)
        logger.error("Wild Weasel Mission Failed!")
        logger.error("Please check the logs and try again")
        logger.error("=" * 60)
    
    return success

if __name__ == "__main__":
    # Ensure we have the required packages
    try:
        import playwright
        import bs4
    except ImportError as e:
        print(f"Missing required package: {e}")
        print("Please install with: pip install playwright beautifulsoup4")
        print("Then run: playwright install")
        exit(1)
    
    # Run the automation
    asyncio.run(main())

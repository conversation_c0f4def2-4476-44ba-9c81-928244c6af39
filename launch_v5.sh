#!/bin/bash

# 🐺 Wild Weasel v5.0 API Enhanced Launcher
# Smart launcher script that guides through the migration process

echo "🐺 Wild Weasel v5.0 API Enhanced Migration Launcher"
echo "=================================================="
echo ""

# Function to check if file exists
check_file() {
    if [ ! -f "$1" ]; then
        echo "❌ Error: $1 not found"
        return 1
    fi
    return 0
}

# Function to check Python dependencies
check_dependencies() {
    echo "🔍 Checking dependencies..."
    
    # Check if Python 3 is available
    if ! command -v python3 &> /dev/null; then
        echo "❌ Python 3 is not installed"
        return 1
    fi
    
    # Check key packages
    python3 -c "import playwright, aiofiles, aiohttp" 2>/dev/null
    if [ $? -ne 0 ]; then
        echo "⚠️  Some dependencies missing. Run setup first."
        return 1
    fi
    
    echo "✅ Dependencies check passed"
    return 0
}

# Function to check data file
check_data() {
    echo "📊 Checking Totango data file..."
    
    data_file="/Users/<USER>/Desktop/totango/ICICI_processed.json"
    if [ ! -f "$data_file" ]; then
        echo "❌ Totango data file not found: $data_file"
        echo "   Please ensure your data is in the correct location"
        return 1
    fi
    
    # Check file size
    file_size=$(wc -c < "$data_file")
    if [ $file_size -lt 100 ]; then
        echo "❌ Data file appears to be empty or too small"
        return 1
    fi
    
    # Count entries (approximate)
    entry_count=$(grep -o '"id":' "$data_file" | wc -l)
    echo "✅ Data file found with approximately $entry_count events"
    return 0
}

# Function to check credentials
check_credentials() {
    echo "🔐 Checking credentials..."
    
    if [ -f ".env" ]; then
        if grep -q "your_username_here" .env || grep -q "your_password_here" .env; then
            echo "⚠️  Please update your credentials in .env file"
            return 1
        fi
        echo "✅ Credentials file found"
        return 0
    else
        echo "⚠️  No .env file found. You'll be prompted for credentials."
        return 0
    fi
}

# Main menu
show_menu() {
    echo ""
    echo "🚀 What would you like to do?"
    echo "1. 🔧 Setup environment (install dependencies)"
    echo "2. 🧪 Validate data transformation" 
    echo "3. 🐺 Run full migration"
    echo "4. 📊 View previous results"
    echo "5. 🆘 Help & troubleshooting"
    echo "6. 🚪 Exit"
    echo ""
    echo -n "Choose an option (1-6): "
}

# Setup function
run_setup() {
    echo ""
    echo "🔧 Running setup..."
    
    if check_file "setup_v5_enhanced.sh"; then
        chmod +x setup_v5_enhanced.sh
        ./setup_v5_enhanced.sh
    else
        echo "❌ Setup script not found"
        return 1
    fi
}

# Validation function
run_validation() {
    echo ""
    echo "🧪 Running data transformation validation..."
    
    if check_file "validate_transformation_v5.py"; then
        python3 validate_transformation_v5.py
    else
        echo "❌ Validation script not found"
        return 1
    fi
}

# Migration function
run_migration() {
    echo ""
    echo "🐺 Starting Wild Weasel v5.0 API Enhanced Migration..."
    echo ""
    
    # Pre-flight checks
    echo "🚀 Pre-flight checks..."
    
    if ! check_dependencies; then
        echo "💡 Try running option 1 (Setup environment) first"
        return 1
    fi
    
    if ! check_data; then
        echo "💡 Please ensure your Totango data is properly prepared"
        return 1
    fi
    
    check_credentials
    
    echo ""
    echo "⚠️  IMPORTANT: This will migrate ALL events in your Totango data file."
    echo "   This may take a significant amount of time depending on the data size."
    echo ""
    echo -n "Are you sure you want to proceed? (y/N): "
    read confirm
    
    if [[ $confirm == [yY] || $confirm == [yY][eE][sS] ]]; then
        echo ""
        echo "🚀 Launching migration..."
        
        if check_file "wild_weasel_agent_v5_api_enhanced.py"; then
            python3 wild_weasel_agent_v5_api_enhanced.py
        else
            echo "❌ Migration script not found"
            return 1
        fi
    else
        echo "Migration cancelled."
    fi
}

# View results function
view_results() {
    echo ""
    echo "📊 Previous migration results:"
    echo ""
    
    # Find result files
    result_files=$(ls -t migration_results_v5_*.json 2>/dev/null | head -5)
    
    if [ -z "$result_files" ]; then
        echo "No previous results found."
        return
    fi
    
    echo "Recent result files:"
    for file in $result_files; do
        file_date=$(echo $file | grep -o '[0-9]\{8\}_[0-9]\{6\}')
        if [ ! -z "$file_date" ]; then
            formatted_date=$(echo $file_date | sed 's/_/ /')
            echo "  📄 $file (from $formatted_date)"
        else
            echo "  📄 $file"
        fi
    done
    
    echo ""
    echo "📈 Latest statistics:"
    latest_stats=$(ls -t migration_stats_v5_*.json 2>/dev/null | head -1)
    if [ ! -z "$latest_stats" ]; then
        echo "From: $latest_stats"
        python3 -c "
import json
try:
    with open('$latest_stats', 'r') as f:
        stats = json.load(f)
    print(f\"  Total: {stats.get('total_items', 'N/A')} events\")
    print(f\"  Successful: {stats.get('successful', 'N/A')}\")
    print(f\"  Failed: {stats.get('failed', 'N/A')}\")
    print(f\"  Success Rate: {stats.get('success_rate', 'N/A'):.1f}%\" if isinstance(stats.get('success_rate'), (int, float)) else f\"  Success Rate: {stats.get('success_rate', 'N/A')}\")
    print(f\"  Duration: {stats.get('duration', 'N/A')}\")
except Exception as e:
    print(f\"  Error reading stats: {e}\")
"
    else
        echo "No statistics files found."
    fi
}

# Help function
show_help() {
    echo ""
    echo "🆘 Wild Weasel v5.0 Help & Troubleshooting"
    echo "==========================================="
    echo ""
    echo "📋 Prerequisites:"
    echo "  • Python 3.8+ installed"
    echo "  • Gainsight account credentials"
    echo "  • Totango data file at: /Users/<USER>/Desktop/totango/ICICI_processed.json"
    echo ""
    echo "🔧 Common Issues:"
    echo "  • 'Module not found' errors → Run option 1 (Setup environment)"
    echo "  • 'Authentication failed' → Check credentials in .env file"
    echo "  • 'Data file not found' → Ensure Totango data is in correct location"
    echo "  • Browser errors → Run 'playwright install chromium'"
    echo ""
    echo "📊 What gets migrated:"
    echo "  • All Totango events are transformed to Gainsight timeline activities"
    echo "  • Event types: automated_attribute_change, webhook, campaign_touch, etc."
    echo "  • Company mapping: ICICI Bank (0015p00005R7ysqAAB → 1P02IPMAEL4M3CQGY4K5FHU22D2HHT11KCKU)"
    echo ""
    echo "🚀 Process flow:"
    echo "  1. Setup environment → 2. Validate transformation → 3. Run migration"
    echo ""
    echo "📞 Need more help?"
    echo "  • Check logs in wild_weasel_v5_api.log"
    echo "  • Review migration results in the results files"
    echo "  • Ensure Gainsight demo environment is accessible"
}

# Main script
main() {
    # Check if we're in the right directory
    if [ ! -f "wild_weasel_agent_v5_api_enhanced.py" ]; then
        echo "❌ Error: Please run this script from the wild_weasel_gainsight_migration directory"
        echo "   Current directory: $(pwd)"
        exit 1
    fi
    
    while true; do
        show_menu
        read choice
        
        case $choice in
            1)
                run_setup
                ;;
            2)
                run_validation
                ;;
            3)
                run_migration
                ;;
            4)
                view_results
                ;;
            5)
                show_help
                ;;
            6)
                echo ""
                echo "🐺 Thanks for using Wild Weasel v5.0!"
                echo "Happy migrating! 🚀"
                exit 0
                ;;
            *)
                echo "❌ Invalid option. Please choose 1-6."
                ;;
        esac
        
        echo ""
        echo -n "Press Enter to continue..."
        read
    done
}

# Run main function
main

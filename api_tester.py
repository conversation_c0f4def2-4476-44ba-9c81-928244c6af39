#!/usr/bin/env python3
"""
🐺 Wild Weasel API Tester - Debug ID Injection & API Uploads
=============================================================================
Mission: Test and debug the specific API workflow for draft creation and activity upload

This script will:
1. Test API connectivity and authentication
2. Test draft creation and ID extraction 
3. Test activity upload with injected ID
4. Validate payload structure
5. Generate detailed debug reports
"""

import json
import os
import sys
import requests
import logging
from datetime import datetime
from typing import Dict, List, Any, Optional

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("/Users/<USER>/Desktop/wild_weasel_gainsight_migration/api_tester.log"),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger("WildWeasel-API-Tester")

class WildWeaselAPITester:
    """Focused API testing for debugging"""
    
    def __init__(self):
        self.config = {
            "drafts_api_url": "https://demo-emea1.gainsightcloud.com/v1/ant/v2/activity/drafts",
            "activity_api_url": "https://demo-emea1.gainsightcloud.com/v1/ant/v2/activity",
            "formatted_activities_file": "/Users/<USER>/Desktop/wild_weasel_gainsight_migration/gainsight_api_payload_email_activities.json",
            "gainsight_payload_file": "/Users/<USER>/Desktop/wild_weasel_gainsight_migration/Gainsight_payload.json"
        }
        
        # You'll need to provide these - get from browser after login
        self.test_cookies = {
            # Add your session cookies here after login
            # Example: "session_id": "your_session_value"
        }
        
        self.test_results = {
            "payload_validation": {},
            "api_connectivity": {},
            "draft_creation": {},
            "id_extraction": {},
            "activity_upload": {},
            "recommendations": []
        }
    
    def load_sample_payload(self) -> Dict[str, Any]:
        """Load and return a sample payload for testing"""
        try:
            # First try the formatted activities file
            if os.path.exists(self.config['formatted_activities_file']):
                with open(self.config['formatted_activities_file'], 'r') as f:
                    activities = json.load(f)
                    if activities and len(activities) > 0:
                        return activities[0]
            
            # Fallback to gainsight payload file
            if os.path.exists(self.config['gainsight_payload_file']):
                with open(self.config['gainsight_payload_file'], 'r') as f:
                    return json.load(f)
            
            logger.error("❌ No payload files found")
            return {}
            
        except Exception as e:
            logger.error(f"❌ Error loading payload: {e}")
            return {}
    
    def validate_payload_structure(self, payload: Dict[str, Any]) -> Dict[str, Any]:
        """Validate the payload structure for Gainsight API"""
        validation_result = {
            "is_valid": False,
            "errors": [],
            "warnings": [],
            "corrections_needed": {},
            "structure_analysis": {}
        }
        
        try:
            # Check top-level structure
            required_fields = ['note', 'meta', 'author', 'contexts']
            for field in required_fields:
                if field not in payload:
                    validation_result["errors"].append(f"Missing required field: {field}")
                else:
                    validation_result["structure_analysis"][field] = "present"
            
            # Check note structure
            if 'note' in payload:
                note = payload['note']
                if 'type' not in note:
                    validation_result["errors"].append("Missing note.type")
                    validation_result["corrections_needed"]["note.type"] = "EMAIL"
                
                if 'subject' not in note:
                    validation_result["errors"].append("Missing note.subject")
                    validation_result["corrections_needed"]["note.subject"] = "Test Email Activity"
                
                validation_result["structure_analysis"]["note"] = {
                    "type": note.get('type'),
                    "subject": note.get('subject', 'MISSING')[:50] + "...",
                    "has_content": 'content' in note,
                    "has_activity_date": 'activityDate' in note
                }
            
            # Check contexts (company info)
            if 'contexts' in payload:
                contexts = payload['contexts']
                if not isinstance(contexts, list) or len(contexts) == 0:
                    validation_result["errors"].append("contexts must be non-empty list")
                else:
                    context = contexts[0]
                    if 'id' not in context:
                        validation_result["errors"].append("Missing company ID in contexts")
                    
                    validation_result["structure_analysis"]["contexts"] = {
                        "count": len(contexts),
                        "company_id": context.get('id', 'MISSING'),
                        "company_label": context.get('lbl', 'MISSING')
                    }
            
            # Check ID field (this is the key issue)
            if 'id' not in payload:
                validation_result["errors"].append("Missing 'id' field - this will be filled by draft API")
                validation_result["corrections_needed"]["id"] = "WILL_BE_INJECTED_FROM_DRAFT_API"
            elif payload['id'] == "":
                validation_result["warnings"].append("ID field is empty - needs draft ID injection")
            else:
                validation_result["structure_analysis"]["id"] = payload['id']
            
            # Check author
            if 'author' in payload:
                author = payload['author']
                validation_result["structure_analysis"]["author"] = {
                    "name": author.get('name', 'MISSING'),
                    "email": author.get('email', 'MISSING'),
                    "id": author.get('id', 'MISSING')
                }
            
            # Overall validation
            validation_result["is_valid"] = len(validation_result["errors"]) == 0
            
            return validation_result
            
        except Exception as e:
            validation_result["errors"].append(f"Validation exception: {str(e)}")
            return validation_result
    
    def test_draft_api(self, payload: Dict[str, Any]) -> Dict[str, Any]:
        """Test the drafts API endpoint"""
        draft_result = {
            "success": False,
            "draft_id": None,
            "response_data": None,
            "error": None,
            "status_code": None
        }
        
        try:
            # Extract necessary data for draft creation
            note = payload.get('note', {})
            contexts = payload.get('contexts', [])
            
            if not contexts:
                draft_result["error"] = "No contexts found in payload"
                return draft_result
            
            company_id = contexts[0].get('id')
            if not company_id:
                draft_result["error"] = "No company ID found in contexts"
                return draft_result
            
            subject = note.get('subject', 'Test Email Activity')
            
            # Create draft payload
            draft_payload = {
                "type": "EMAIL",
                "subject": subject[:255],
                "companyId": company_id
            }
            
            logger.info(f"🧪 Testing draft API with payload: {json.dumps(draft_payload, indent=2)}")
            
            # You need to provide actual session cookies here
            if not self.test_cookies:
                draft_result["error"] = "No session cookies provided - please add cookies after login"
                return draft_result
            
            # Build cookie string
            cookie_string = "; ".join([f"{k}={v}" for k, v in self.test_cookies.items()])
            
            headers = {
                "Content-Type": "application/json",
                "Cookie": cookie_string,
                "User-Agent": "Wild-Weasel-API-Tester/1.0",
                "Accept": "application/json",
                "X-Requested-With": "XMLHttpRequest",
                "Origin": "https://demo-emea1.gainsightcloud.com",
                "Referer": "https://demo-emea1.gainsightcloud.com/v1/ui/customersuccess360"
            }
            
            # Make the API call
            response = requests.post(
                self.config['drafts_api_url'],
                json=draft_payload,
                headers=headers,
                timeout=30
            )
            
            draft_result["status_code"] = response.status_code
            
            if response.status_code in [200, 201]:
                response_data = response.json()
                draft_result["response_data"] = response_data
                draft_result["success"] = True
                
                # Try to extract draft ID
                draft_id = self.extract_draft_id(response_data)
                draft_result["draft_id"] = draft_id
                
                if draft_id:
                    logger.info(f"✅ Draft created successfully with ID: {draft_id}")
                else:
                    logger.warning("⚠️ Draft created but no ID found in response")
                    
            else:
                draft_result["error"] = f"HTTP {response.status_code}: {response.text}"
                logger.error(f"❌ Draft API failed: {draft_result['error']}")
            
        except Exception as e:
            draft_result["error"] = f"Exception: {str(e)}"
            logger.error(f"❌ Draft API test exception: {e}")
        
        return draft_result
    
    def extract_draft_id(self, response_data: Dict[str, Any]) -> Optional[str]:
        """Extract draft ID from response with multiple strategies"""
        try:
            # Strategy 1: Direct ID
            if 'id' in response_data and response_data['id']:
                return str(response_data['id'])
            
            # Strategy 2: Nested in data
            if 'data' in response_data:
                data = response_data['data']
                if isinstance(data, dict) and 'id' in data:
                    return str(data['id'])
                elif isinstance(data, list) and len(data) > 0 and 'id' in data[0]:
                    return str(data[0]['id'])
            
            # Strategy 3: Alternative field names
            for field in ['draftId', 'activityId', 'guid', 'uuid']:
                if field in response_data and response_data[field]:
                    return str(response_data[field])
            
            return None
            
        except Exception as e:
            logger.error(f"❌ Error extracting draft ID: {e}")
            return None
    
    def test_activity_api(self, payload: Dict[str, Any], draft_id: str) -> Dict[str, Any]:
        """Test activity creation with injected draft ID"""
        activity_result = {
            "success": False,
            "response_data": None,
            "error": None,
            "status_code": None,
            "payload_with_id": None
        }
        
        try:
            # Inject draft ID into payload
            test_payload = payload.copy()
            test_payload['id'] = draft_id
            
            activity_result["payload_with_id"] = test_payload
            
            logger.info(f"🧪 Testing activity API with injected ID: {draft_id}")
            
            if not self.test_cookies:
                activity_result["error"] = "No session cookies provided"
                return activity_result
            
            cookie_string = "; ".join([f"{k}={v}" for k, v in self.test_cookies.items()])
            
            headers = {
                "Content-Type": "application/json",
                "Cookie": cookie_string,
                "User-Agent": "Wild-Weasel-API-Tester/1.0",
                "Accept": "application/json",
                "X-Requested-With": "XMLHttpRequest",
                "Origin": "https://demo-emea1.gainsightcloud.com",
                "Referer": "https://demo-emea1.gainsightcloud.com/v1/ui/customersuccess360"
            }
            
            # Make the API call
            response = requests.post(
                self.config['activity_api_url'],
                json=test_payload,
                headers=headers,
                timeout=30
            )
            
            activity_result["status_code"] = response.status_code
            
            if response.status_code in [200, 201]:
                response_data = response.json()
                activity_result["response_data"] = response_data
                activity_result["success"] = True
                logger.info(f"✅ Activity created successfully!")
                
            else:
                activity_result["error"] = f"HTTP {response.status_code}: {response.text}"
                logger.error(f"❌ Activity API failed: {activity_result['error']}")
            
        except Exception as e:
            activity_result["error"] = f"Exception: {str(e)}"
            logger.error(f"❌ Activity API test exception: {e}")
        
        return activity_result
    
    def run_comprehensive_test(self):
        """Run comprehensive API testing"""
        print("🐺" + "="*80)
        print("  WILD WEASEL API TESTER - DEBUG SESSION")
        print("="*82)
        
        # Step 1: Load and validate payload
        logger.info("📋 Step 1: Loading and validating payload...")
        sample_payload = self.load_sample_payload()
        
        if not sample_payload:
            print("❌ CRITICAL: No payload found to test!")
            return
        
        validation_result = self.validate_payload_structure(sample_payload)
        self.test_results["payload_validation"] = validation_result
        
        print(f"\n📊 PAYLOAD VALIDATION:")
        print(f"  Valid: {'✅' if validation_result['is_valid'] else '❌'}")
        print(f"  Errors: {len(validation_result['errors'])}")
        print(f"  Warnings: {len(validation_result['warnings'])}")
        
        if validation_result['errors']:
            print("  🔧 Errors found:")
            for error in validation_result['errors']:
                print(f"    - {error}")
        
        if validation_result['corrections_needed']:
            print("  💡 Auto-corrections available:")
            for field, correction in validation_result['corrections_needed'].items():
                print(f"    - {field}: {correction}")
        
        # Step 2: Test draft API
        logger.info("📄 Step 2: Testing draft API...")
        
        if not self.test_cookies:
            print("\n❌ CRITICAL: No session cookies provided!")
            print("   Please add your session cookies to the test_cookies dictionary")
            print("   You can get these from browser dev tools after logging into Gainsight")
            self.generate_instructions()
            return
        
        draft_result = self.test_draft_api(sample_payload)
        self.test_results["draft_creation"] = draft_result
        
        print(f"\n📄 DRAFT API TEST:")
        print(f"  Success: {'✅' if draft_result['success'] else '❌'}")
        print(f"  Status Code: {draft_result['status_code']}")
        print(f"  Draft ID: {draft_result['draft_id'] or 'None'}")
        
        if draft_result['error']:
            print(f"  Error: {draft_result['error']}")
        
        # Step 3: Test activity API (if draft succeeded)
        if draft_result['success'] and draft_result['draft_id']:
            logger.info("📧 Step 3: Testing activity API with injected ID...")
            
            activity_result = self.test_activity_api(sample_payload, draft_result['draft_id'])
            self.test_results["activity_upload"] = activity_result
            
            print(f"\n📧 ACTIVITY API TEST:")
            print(f"  Success: {'✅' if activity_result['success'] else '❌'}")
            print(f"  Status Code: {activity_result['status_code']}")
            
            if activity_result['error']:
                print(f"  Error: {activity_result['error']}")
        else:
            print(f"\n📧 ACTIVITY API TEST: Skipped (draft API failed)")
        
        # Step 4: Generate recommendations
        self.generate_recommendations()
        
        # Step 5: Save detailed results
        self.save_test_results()
        
        print(f"\n🎯 TESTING COMPLETE!")
        print("="*82)
    
    def generate_recommendations(self):
        """Generate recommendations based on test results"""
        recommendations = []
        
        validation = self.test_results.get("payload_validation", {})
        draft = self.test_results.get("draft_creation", {})
        activity = self.test_results.get("activity_upload", {})
        
        # Payload recommendations
        if not validation.get("is_valid", False):
            recommendations.append("🔧 Fix payload validation errors before proceeding")
            if validation.get("corrections_needed"):
                recommendations.append("💡 Apply the suggested auto-corrections to payload")
        
        # Draft API recommendations
        if not draft.get("success", False):
            if "cookies" in str(draft.get("error", "")):
                recommendations.append("🍪 Provide valid session cookies from authenticated browser session")
            elif draft.get("status_code") == 401:
                recommendations.append("🔐 Check authentication - session may have expired")
            elif draft.get("status_code") == 403:
                recommendations.append("⚠️ Check API permissions for your user account")
            else:
                recommendations.append("🔍 Debug draft API connection and payload format")
        
        # Activity API recommendations
        if draft.get("success") and not activity.get("success", False):
            if activity.get("status_code") == 400:
                recommendations.append("📋 Check activity payload structure - may need field adjustments")
            elif activity.get("status_code") == 422:
                recommendations.append("🔧 Payload validation failed on server - check required fields")
            else:
                recommendations.append("🔍 Debug activity API payload and headers")
        
        # Success recommendations
        if draft.get("success") and activity.get("success"):
            recommendations.append("🎉 API workflow is working! You can proceed with full migration")
            recommendations.append("🚀 Consider running Wild Weasel v5 Enhanced for full automation")
        
        self.test_results["recommendations"] = recommendations
        
        print(f"\n💡 RECOMMENDATIONS:")
        for rec in recommendations:
            print(f"  {rec}")
    
    def generate_instructions(self):
        """Generate instructions for getting session cookies"""
        instructions = """
🍪 HOW TO GET SESSION COOKIES:

1. Open your browser and login to Gainsight
2. Open Developer Tools (F12)
3. Go to Network tab
4. Navigate to C360 page in Gainsight
5. Look for any request to demo-emea1.gainsightcloud.com
6. Right-click on the request → Copy → Copy as cURL
7. Extract the Cookie header from the cURL command
8. Add each cookie to the test_cookies dictionary in this format:

   self.test_cookies = {
       "cookie_name_1": "cookie_value_1",
       "cookie_name_2": "cookie_value_2",
       # ... add all cookies from the Cookie header
   }

9. Re-run this tester script

💡 Alternative: You can also copy cookies from Application/Storage tab in dev tools
"""
        print(instructions)
    
    def save_test_results(self):
        """Save comprehensive test results"""
        try:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            results_file = f"/Users/<USER>/Desktop/wild_weasel_gainsight_migration/api_test_results_{timestamp}.json"
            
            with open(results_file, 'w') as f:
                json.dump(self.test_results, f, indent=2, default=str)
            
            logger.info(f"💾 Test results saved to: {results_file}")
            print(f"📄 Detailed results saved to: {results_file}")
            
        except Exception as e:
            logger.error(f"❌ Failed to save test results: {e}")

def main():
    """Main execution"""
    try:
        tester = WildWeaselAPITester()
        tester.run_comprehensive_test()
        
    except KeyboardInterrupt:
        print("\n🐺 API testing interrupted by user")
        sys.exit(0)
    except Exception as e:
        logger.error(f"❌ API testing failed: {e}")
        import traceback
        logger.error(f"Traceback: {traceback.format_exc()}")
        sys.exit(1)

if __name__ == "__main__":
    main()

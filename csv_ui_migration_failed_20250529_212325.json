[{"row_number": 1, "subject": "ICICI: Mend Platform Access", "activity_type": "Update", "error": "UI exception: Page.screenshot: Target page, context or browser has been closed", "timestamp": "2025-05-29T21:12:32.673467"}, {"row_number": 2, "subject": "ICICI: Mend Platform Access", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:12:34.694375"}, {"row_number": 3, "subject": "ICICI: Mend Platform Access", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:12:36.710573"}, {"row_number": 4, "subject": "ICICI: Mend Platform Access", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:12:38.726370"}, {"row_number": 5, "subject": "ICICI: Mend Platform Access", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:12:40.741772"}, {"row_number": 6, "subject": "ICICI: Mend Platform Access", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:12:42.759010"}, {"row_number": 7, "subject": "ICICI: <PERSON><PERSON><PERSON> to Delighted", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:12:44.784818"}, {"row_number": 8, "subject": "ICICI: NPS Send Date", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:12:46.814024"}, {"row_number": 9, "subject": "ICICI: NPS Last Sent", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:12:48.830832"}, {"row_number": 10, "subject": "ICICI: Mend Platform Access", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:12:50.849292"}, {"row_number": 11, "subject": "ICICI: May Newsletter: AI Risk Insights, Smarter Compliance and Cloud Integration!", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:12:52.864230"}, {"row_number": 12, "subject": "ICICI: Mend Platform Access", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:12:54.887592"}, {"row_number": 13, "subject": "ICICI: Mend Platform Access", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:12:56.903727"}, {"row_number": 14, "subject": "ICICI: MP Activity Frequency", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:12:58.921754"}, {"row_number": 15, "subject": "ICICI: <PERSON> <PERSON><PERSON><PERSON> (7d)", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:13:00.935941"}, {"row_number": 16, "subject": "ICICI: Mend Platform Access", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:13:02.956795"}, {"row_number": 17, "subject": "ICICI: SCA CD Variance Stages", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:13:04.972985"}, {"row_number": 18, "subject": "ICICI: <div><br>Upcoming NPS Survey&nbsp;</div>", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:13:06.989750"}, {"row_number": 19, "subject": "ICICI: Mend Platform Access", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:13:09.006507"}, {"row_number": 20, "subject": "ICICI: MP Activity", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:13:11.023325"}, {"row_number": 21, "subject": "ICICI: MP Activity Frequency", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:13:13.037820"}, {"row_number": 22, "subject": "ICICI: MITRE", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:13:15.055468"}, {"row_number": 23, "subject": "ICICI: MP Abandonment Date", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:13:17.071279"}, {"row_number": 24, "subject": "ICICI: MP Activity", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:13:19.087626"}, {"row_number": 25, "subject": "ICICI: Mend Platform Access", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:13:21.104046"}, {"row_number": 26, "subject": "ICICI: Mend Platform Access", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:13:23.123177"}, {"row_number": 27, "subject": "ICICI: April Newsletter: New Dashboard, Mend AI, and More!", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:13:25.136276"}, {"row_number": 28, "subject": "ICICI: Mend Platform Access", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:13:27.154701"}, {"row_number": 29, "subject": "ICICI: Mend Platform Access", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:13:29.171166"}, {"row_number": 30, "subject": "ICICI: Mend Platform Access", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:13:31.187160"}, {"row_number": 31, "subject": "ICICI: MP Activity Frequency", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:13:33.206472"}, {"row_number": 32, "subject": "ICICI: MP Activity", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:13:35.224597"}, {"row_number": 33, "subject": "ICICI: <PERSON> Newsletter: Introducing Mend AI and More!", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:13:37.244688"}, {"row_number": 34, "subject": "ICICI: MP Abandonment Date", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:13:39.263841"}, {"row_number": 35, "subject": "ICICI: MP Activity", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:13:41.279963"}, {"row_number": 36, "subject": "ICICI: MP Activity Frequency", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:13:43.299944"}, {"row_number": 37, "subject": "ICICI: Mend Platform Access", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:13:45.323856"}, {"row_number": 38, "subject": "ICICI: MP Activity Frequency", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:13:47.338065"}, {"row_number": 39, "subject": "ICICI: <PERSON> <PERSON><PERSON><PERSON> (7d)", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:13:49.355049"}, {"row_number": 40, "subject": "ICICI: Mend Platform Access", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:13:51.371652"}, {"row_number": 41, "subject": "ICICI: Active - CN", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:13:53.385153"}, {"row_number": 42, "subject": "ICICI: Active - SAST", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:13:55.406603"}, {"row_number": 43, "subject": "ICICI: Active - SCA", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:13:57.422326"}, {"row_number": 44, "subject": "ICICI: Mend Platform Access", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:13:59.440189"}, {"row_number": 45, "subject": "ICICI: Invicti Campaign", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:14:01.454907"}, {"row_number": 46, "subject": "ICICI: MP Activity", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:14:03.471617"}, {"row_number": 47, "subject": "ICICI: Mend Platform Access", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:14:05.489454"}, {"row_number": 48, "subject": "ICICI: MP Abandonment Date", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:14:07.504876"}, {"row_number": 49, "subject": "ICICI: MP Activity", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:14:09.520587"}, {"row_number": 50, "subject": "ICICI: MP Activity", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:14:11.540499"}, {"row_number": 51, "subject": "ICICI: February Newsletter: AI-Powered Code Remediation and More!", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:14:13.560245"}, {"row_number": 52, "subject": "ICICI: MP Abandonment Date", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:14:15.582057"}, {"row_number": 53, "subject": "ICICI: MP Activity", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:14:17.599177"}, {"row_number": 54, "subject": "ICICI: Mend Platform Access", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:14:19.615350"}, {"row_number": 55, "subject": "ICICI: MP Activity", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:14:21.632748"}, {"row_number": 56, "subject": "ICICI: Mend Platform Access", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:14:23.648710"}, {"row_number": 57, "subject": "ICICI: MP Abandonment Date", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:14:25.667750"}, {"row_number": 58, "subject": "ICICI: MP Activity", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:14:27.680122"}, {"row_number": 59, "subject": "ICICI: MP Activity", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:14:29.698400"}, {"row_number": 60, "subject": "ICICI: MP Activity Frequency", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:14:31.718584"}, {"row_number": 61, "subject": "ICICI: SAST Planned Downtime_App_copy", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:14:33.736648"}, {"row_number": 62, "subject": "ICICI: Mend Platform Access", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:14:35.758717"}, {"row_number": 63, "subject": "ICICI: MP Abandonment Date", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:14:37.763740"}, {"row_number": 64, "subject": "ICICI: MP Activity", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:14:39.773953"}, {"row_number": 65, "subject": "ICICI: MP Activity", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:14:41.788654"}, {"row_number": 66, "subject": "ICICI: MP Abandonment Date", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:14:43.802804"}, {"row_number": 67, "subject": "ICICI: MP Activity", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:14:45.813017"}, {"row_number": 68, "subject": "ICICI: AI Design Partners", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:14:47.830055"}, {"row_number": 69, "subject": "ICICI: Mend Platform Access", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:14:49.848021"}, {"row_number": 70, "subject": "ICICI: MP Activity", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:14:51.864830"}, {"row_number": 71, "subject": "ICICI: <PERSON> <PERSON><PERSON><PERSON> (14d)", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:14:53.872176"}, {"row_number": 72, "subject": "ICICI: Mend Platform Access", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:14:55.895746"}, {"row_number": 73, "subject": "ICICI: MP Abandonment Date", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:14:57.917271"}, {"row_number": 74, "subject": "ICICI: MP Activity", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:14:59.938355"}, {"row_number": 75, "subject": "ICICI: MP Activity", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:15:01.959757"}, {"row_number": 76, "subject": "ICICI: Mend Platform Access", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:15:03.978098"}, {"row_number": 77, "subject": "ICICI: MP Abandonment Date", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:15:05.991640"}, {"row_number": 78, "subject": "ICICI: MP Activity", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:15:08.005742"}, {"row_number": 79, "subject": "ICICI: MP Activity Frequency", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:15:10.026262"}, {"row_number": 80, "subject": "ICICI: MP Activity", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:15:12.043110"}, {"row_number": 81, "subject": "ICICI: January Newsletter:  Keep Your Code Secure, Exciting Updates from Mend.io!", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:15:14.053229"}, {"row_number": 82, "subject": "ICICI: Mend Platform Access", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:15:16.073994"}, {"row_number": 83, "subject": "ICICI: MP Abandonment Date", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:15:18.087563"}, {"row_number": 84, "subject": "ICICI: MP Activity", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:15:20.107859"}, {"row_number": 85, "subject": "ICICI: MP Activity", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:15:22.121244"}, {"row_number": 86, "subject": "ICICI: Mend Platform Access", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:15:24.140291"}, {"row_number": 87, "subject": "ICICI: MP Activity Frequency", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:15:26.147541"}, {"row_number": 88, "subject": "ICICI: MP Abandonment Date", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:15:28.163436"}, {"row_number": 89, "subject": "ICICI: MP Activity", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:15:30.177048"}, {"row_number": 90, "subject": "ICICI: <PERSON><PERSON><PERSON> to Delighted", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:15:32.184070"}, {"row_number": 91, "subject": "ICICI: <PERSON><PERSON><PERSON> to Delighted", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:15:34.205120"}, {"row_number": 92, "subject": "ICICI: NPS Send Date", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:15:36.212483"}, {"row_number": 93, "subject": "ICICI: NPS Last Sent", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:15:38.220801"}, {"row_number": 94, "subject": "ICICI: NPS Send Date", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:15:40.229111"}, {"row_number": 95, "subject": "ICICI: NPS Last Sent", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:15:42.235014"}, {"row_number": 96, "subject": "ICICI: Mend Platform Access", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:15:44.241527"}, {"row_number": 97, "subject": "ICICI: Mend Platform Access", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:15:46.250939"}, {"row_number": 98, "subject": "ICICI: Risk Status", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:15:48.256437"}, {"row_number": 99, "subject": "ICICI: Risk Next Step(s)", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:15:50.263385"}, {"row_number": 100, "subject": "ICICI: Primary Risk Reason", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:15:52.267587"}, {"row_number": 101, "subject": "ICICI: Mend Platform Access", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:15:54.276405"}, {"row_number": 102, "subject": "ICICI: <div><br>Upcoming NPS Survey&nbsp;</div>", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:15:56.284064"}, {"row_number": 103, "subject": "ICICI: Mend Platform Access", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:15:58.289565"}, {"row_number": 104, "subject": "ICICI: Mend Platform Access", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:16:00.299034"}, {"row_number": 105, "subject": "ICICI: Risk KPI", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:16:02.306049"}, {"row_number": 106, "subject": "ICICI: <PERSON> <PERSON> <PERSON><PERSON><PERSON>", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:16:04.312410"}, {"row_number": 107, "subject": "ICICI: Mend Platform Access", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:16:06.320512"}, {"row_number": 108, "subject": "ICICI: December Newsletter: Sharper Risk Insights & Updates", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:16:08.330372"}, {"row_number": 109, "subject": "ICICI: Mend Platform Access", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:16:10.339576"}, {"row_number": 110, "subject": "ICICI: Solana: MSC Critical Security Event", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:16:12.346717"}, {"row_number": 111, "subject": "ICICI: Mend Platform Access", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:16:14.353869"}, {"row_number": 112, "subject": "ICICI: Mend Platform Access", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:16:16.363356"}, {"row_number": 113, "subject": "ICICI: Mend Platform Access", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:16:18.371627"}, {"row_number": 114, "subject": "ICICI: SAST Planned Downtime_copy", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:16:20.376324"}, {"row_number": 115, "subject": "ICICI: Mend Platform Access", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:16:22.382504"}, {"row_number": 116, "subject": "ICICI: Mend Platform Access", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:16:24.391502"}, {"row_number": 117, "subject": "ICICI: Mend Platform Access", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:16:26.400657"}, {"row_number": 118, "subject": "ICICI: SAST Planned Downtime", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:16:28.410829"}, {"row_number": 119, "subject": "ICICI: Mend Platform Access", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:16:30.419785"}, {"row_number": 120, "subject": "ICICI: <PERSON><PERSON><PERSON> to Delighted", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:16:32.428134"}, {"row_number": 121, "subject": "ICICI: NPS Send Date", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:16:34.436013"}, {"row_number": 122, "subject": "ICICI: NPS Last Sent", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:16:36.443695"}, {"row_number": 123, "subject": "ICICI: Mend Platform Access", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:16:38.453192"}, {"row_number": 124, "subject": "ICICI: November newsletter 2024", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:16:40.467067"}, {"row_number": 125, "subject": "ICICI: Mend Platform Access", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:16:42.487585"}, {"row_number": 126, "subject": "ICICI: <PERSON> <PERSON><PERSON><PERSON> (7d)", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:16:44.495467"}, {"row_number": 127, "subject": "ICICI: Mend Platform Access", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:16:46.520459"}, {"row_number": 128, "subject": "ICICI: <div><br>Upcoming NPS Survey&nbsp;</div>", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:16:48.540520"}, {"row_number": 129, "subject": "ICICI: October newsletter 2024", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:16:50.559463"}, {"row_number": 130, "subject": "ICICI: Risk Status", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:16:52.574000"}, {"row_number": 131, "subject": "ICICI: Primary Risk Reason", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:16:54.590752"}, {"row_number": 132, "subject": "ICICI: Risk Next Step(s)", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:16:56.600399"}, {"row_number": 133, "subject": "ICICI: Risk Status", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:16:58.604737"}, {"row_number": 134, "subject": "ICICI: Primary Risk Reason", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:17:00.609670"}, {"row_number": 135, "subject": "ICICI: Risk Next Step(s)", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:17:02.627058"}, {"row_number": 136, "subject": "ICICI: Support: IP Address Change", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:17:04.635761"}, {"row_number": 137, "subject": "ICICI: Business Model Launch", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:17:06.651685"}, {"row_number": 138, "subject": "ICICI: New Business Model_Webinar Follow up", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:17:08.671419"}, {"row_number": 139, "subject": "ICICI: MP Activity Frequency", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:17:10.688701"}, {"row_number": 140, "subject": "ICICI: <PERSON><PERSON>", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:17:12.701352"}, {"row_number": 141, "subject": "ICICI: New Business Model", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:17:14.708752"}, {"row_number": 142, "subject": "ICICI: Slack to #risk", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:17:16.729479"}, {"row_number": 143, "subject": "ICICI: Risk Next Step(s)", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:17:18.747668"}, {"row_number": 144, "subject": "ICICI: Primary Risk Reason", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:17:20.756659"}, {"row_number": 145, "subject": "ICICI: Risk Status", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:17:22.764652"}, {"row_number": 146, "subject": "ICICI: <PERSON><PERSON>", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:17:24.784103"}, {"row_number": 147, "subject": "ICICI: MP Activity Frequency", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:17:26.802770"}, {"row_number": 148, "subject": "ICICI: MP Activity Frequency", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:17:28.821556"}, {"row_number": 149, "subject": "ICICI: NPS Send Date", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:17:30.839808"}, {"row_number": 150, "subject": "ICICI: MP Activity Frequency", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:17:32.849530"}, {"row_number": 151, "subject": "ICICI: MP Activity", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:17:34.871033"}, {"row_number": 152, "subject": "ICICI: MP Activity Status", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:17:36.891577"}, {"row_number": 153, "subject": "ICICI: <PERSON><PERSON>", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:17:38.913144"}, {"row_number": 154, "subject": "ICICI: MP Activity Status", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:17:40.925152"}, {"row_number": 155, "subject": "ICICI: Product Roadmap H2 2024", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:17:42.930574"}, {"row_number": 156, "subject": "ICICI: <PERSON><PERSON><PERSON> to Delighted", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:17:44.939752"}, {"row_number": 157, "subject": "ICICI: NPS Last Sent", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:17:46.949271"}, {"row_number": 158, "subject": "ICICI: NPS Send Date", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:17:48.956472"}, {"row_number": 159, "subject": "ICICI: NPS Last Sent", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:17:50.964554"}, {"row_number": 160, "subject": "ICICI: NPS Send Date", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:17:52.973946"}, {"row_number": 161, "subject": "ICICI: <PERSON><PERSON><PERSON> to Delighted", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:17:54.980604"}, {"row_number": 162, "subject": "ICICI: Vulnerability Insights with MITRE Data", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:17:56.991106"}, {"row_number": 163, "subject": "ICICI: <PERSON><PERSON>", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:17:59.016935"}, {"row_number": 164, "subject": "ICICI: Mend Platform Access", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:18:01.021063"}, {"row_number": 165, "subject": "ICICI: CSM Managed", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:18:03.027321"}, {"row_number": 166, "subject": "ICICI: <div><br>Upcoming NPS Survey&nbsp;</div>", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:18:05.041282"}, {"row_number": 167, "subject": "ICICI: <PERSON><PERSON>", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:18:07.047949"}, {"row_number": 168, "subject": "ICICI: <PERSON><PERSON>", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:18:09.067710"}, {"row_number": 169, "subject": "ICICI: <PERSON><PERSON>", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:18:11.073163"}, {"row_number": 170, "subject": "ICICI: <PERSON><PERSON>", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:18:13.077512"}, {"row_number": 171, "subject": "ICICI: <PERSON> Follow Up Email", "activity_type": "Meeting", "error": "UI automation failed", "timestamp": "2025-05-29T21:18:15.087893"}, {"row_number": 172, "subject": "ICICI: <PERSON> Follow Up Email", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:18:17.096956"}, {"row_number": 173, "subject": "ICICI: <PERSON> Follow Up Email", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:18:19.100811"}, {"row_number": 174, "subject": "ICICI: Slack to #risk", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:18:21.110729"}, {"row_number": 175, "subject": "ICICI: <PERSON><PERSON>", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:18:23.116289"}, {"row_number": 176, "subject": "ICICI: <PERSON><PERSON><PERSON> - CSM Satisfaction Survey/Kelle Intro", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:18:25.122905"}, {"row_number": 177, "subject": "ICICI: <div><span class=\"mention-wrapper\" data-reactroot=\"\"><span class=\"field\">ICICI Bank</span>'s renewal date has passed</span></div>", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:18:27.140832"}, {"row_number": 178, "subject": "ICICI: Correction - CVE - 2024 - 3094_copy", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:18:29.145752"}, {"row_number": 179, "subject": "ICICI: <PERSON><PERSON>", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:18:31.155025"}, {"row_number": 180, "subject": "ICICI: All Other Customers - CVE - 2024 - 3094", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:18:33.163569"}, {"row_number": 181, "subject": "ICICI: <PERSON><PERSON>", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:18:35.173037"}, {"row_number": 182, "subject": "ICICI: License Utilization Status", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:18:37.181075"}, {"row_number": 183, "subject": "ICICI: License Utilization Status", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:18:39.200423"}, {"row_number": 184, "subject": "ICICI: <PERSON><PERSON>", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:18:41.214795"}, {"row_number": 185, "subject": "ICICI: <PERSON><PERSON>", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:18:43.223716"}, {"row_number": 186, "subject": "ICICI: <PERSON><PERSON>", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:18:45.244263"}, {"row_number": 187, "subject": "ICICI: <PERSON><PERSON>", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:18:47.259720"}, {"row_number": 188, "subject": "ICICI: Executive Engaged", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:18:49.274416"}, {"row_number": 189, "subject": "ICICI: AI Survey - Gold/ Platinum Customers - Follow Up", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:18:51.280497"}, {"row_number": 190, "subject": "ICICI: <PERSON><PERSON>", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:18:53.289266"}, {"row_number": 191, "subject": "ICICI: <PERSON><PERSON>", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:18:55.298246"}, {"row_number": 192, "subject": "ICICI: AI Survey - Int. Gold & Platinum Customers Oops_copy", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:18:57.307247"}, {"row_number": 193, "subject": "ICICI: AI Survey - Gold & Platinum Customers", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:18:59.319496"}, {"row_number": 194, "subject": "ICICI: <PERSON><PERSON>", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:19:01.328459"}, {"row_number": 195, "subject": "ICICI: <PERSON><PERSON>", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:19:03.346149"}, {"row_number": 196, "subject": "ICICI: <PERSON><PERSON>", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:19:05.355400"}, {"row_number": 197, "subject": "ICICI: <PERSON><PERSON>", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:19:07.360647"}, {"row_number": 198, "subject": "ICICI: Slack to #risk", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:19:09.368114"}, {"row_number": 199, "subject": "ICICI: Risk Update", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:19:11.372269"}, {"row_number": 200, "subject": "ICICI: Risk Status", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:19:13.380820"}, {"row_number": 201, "subject": "ICICI: <PERSON><PERSON>", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:19:15.388794"}, {"row_number": 202, "subject": "ICICI: <PERSON><PERSON>", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:19:17.394586"}, {"row_number": 203, "subject": "ICICI: NPS Send Date", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:19:19.400384"}, {"row_number": 204, "subject": "ICICI: NPS Informed", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:19:21.408784"}, {"row_number": 205, "subject": "ICICI: NPS Informed", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:19:23.417683"}, {"row_number": 206, "subject": "ICICI: Touch Status 2", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:19:25.426779"}, {"row_number": 207, "subject": "ICICI: <PERSON><PERSON>", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:19:27.431095"}, {"row_number": 208, "subject": "ICICI: <PERSON><PERSON>", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:19:29.437847"}, {"row_number": 209, "subject": "ICICI: <PERSON><PERSON>", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:19:31.446561"}, {"row_number": 210, "subject": "ICICI: <PERSON><PERSON>", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:19:33.451197"}, {"row_number": 211, "subject": "ICICI: <PERSON><PERSON>", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:19:35.460026"}, {"row_number": 212, "subject": "ICICI: <PERSON><PERSON>", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:19:37.465140"}, {"row_number": 213, "subject": "ICICI: Slack to #risk", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:19:39.470908"}, {"row_number": 214, "subject": "ICICI: Risk Update", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:19:41.476204"}, {"row_number": 215, "subject": "ICICI: Risk Status", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:19:43.485210"}, {"row_number": 216, "subject": "ICICI: <PERSON><PERSON>", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:19:45.496919"}, {"row_number": 217, "subject": "ICICI: <PERSON><PERSON>", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:19:47.522783"}, {"row_number": 218, "subject": "ICICI: <PERSON><PERSON>", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:19:49.548721"}, {"row_number": 219, "subject": "ICICI: <PERSON><PERSON>", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:19:51.563384"}, {"row_number": 220, "subject": "ICICI: <PERSON><PERSON>", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:19:53.572581"}, {"row_number": 221, "subject": "ICICI: <PERSON><PERSON>", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:19:55.592480"}, {"row_number": 222, "subject": "ICICI: <PERSON><PERSON>", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:19:57.615334"}, {"row_number": 223, "subject": "ICICI: <PERSON><PERSON>", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:19:59.635859"}, {"row_number": 224, "subject": "ICICI: <PERSON><PERSON>", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:20:01.653600"}, {"row_number": 225, "subject": "ICICI: <PERSON><PERSON>", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:20:03.670473"}, {"row_number": 226, "subject": "ICICI: <PERSON><PERSON>", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:20:05.689811"}, {"row_number": 227, "subject": "ICICI: ICICI Bank <> Mend - Catch Up Meeting External", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:20:07.706255"}, {"row_number": 228, "subject": "ICICI: <PERSON><PERSON>", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:20:09.721888"}, {"row_number": 229, "subject": "ICICI: SCA CD Variance Stages", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:20:11.745584"}, {"row_number": 230, "subject": "ICICI: License Utilization Status", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:20:13.754936"}, {"row_number": 231, "subject": "ICICI: Mend Vulnerability found by WithSecure", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:20:15.769311"}, {"row_number": 232, "subject": "ICICI: ICICI Bank <> Mend - Catch Up Meeting", "activity_type": "Meeting", "error": "UI automation failed", "timestamp": "2025-05-29T21:20:17.786581"}, {"row_number": 233, "subject": "ICICI: ICICI Bank<>Mend.io organization migration from LBA to VBA", "activity_type": "Meeting", "error": "UI automation failed", "timestamp": "2025-05-29T21:20:19.814583"}, {"row_number": 234, "subject": "ICICI: <PERSON><PERSON>", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:20:21.828283"}, {"row_number": 235, "subject": "ICICI: No need to touch base", "activity_type": "Meeting", "error": "UI automation failed", "timestamp": "2025-05-29T21:20:23.849672"}, {"row_number": 236, "subject": "ICICI: Unified Agent Hotfix now available-due to Java upgrade issue", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:20:25.866571"}, {"row_number": 237, "subject": "ICICI: Unified Agent - Java upgrade issue", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:20:27.877432"}, {"row_number": 238, "subject": "ICICI: LBA email change sent to the partner", "activity_type": "Meeting", "error": "UI automation failed", "timestamp": "2025-05-29T21:20:29.900216"}, {"row_number": 239, "subject": "ICICI: ICICI Bank<>MEND", "activity_type": "Meeting", "error": "UI automation failed", "timestamp": "2025-05-29T21:20:31.913929"}, {"row_number": 240, "subject": "ICICI: <PERSON><PERSON>", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:20:33.931849"}, {"row_number": 241, "subject": "ICICI: <PERSON><PERSON>", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:20:35.946104"}, {"row_number": 242, "subject": "ICICI: Request for Developer Training for ICICI", "activity_type": "Meeting", "error": "UI automation failed", "timestamp": "2025-05-29T21:20:37.959355"}, {"row_number": 243, "subject": "ICICI: Re: Queries on Licensing Policy related details - ICICI Bank", "activity_type": "Email", "error": "UI automation failed", "timestamp": "2025-05-29T21:20:39.974114"}, {"row_number": 244, "subject": "ICICI: Re: Mend integration with LDAP and SAML - ICICI", "activity_type": "Email", "error": "UI automation failed", "timestamp": "2025-05-29T21:20:41.994384"}, {"row_number": 245, "subject": "ICICI: <PERSON><PERSON>", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:20:44.015934"}, {"row_number": 246, "subject": "ICICI: NPS Send Date", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:20:46.034543"}, {"row_number": 247, "subject": "ICICI: NPS Informed", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:20:48.047486"}, {"row_number": 248, "subject": "ICICI: <PERSON><PERSON>", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:20:50.059301"}, {"row_number": 249, "subject": "ICICI: <PERSON><PERSON>", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:20:52.076447"}, {"row_number": 250, "subject": "ICICI: No need to touch base- working with the partner", "activity_type": "Meeting", "error": "UI automation failed", "timestamp": "2025-05-29T21:20:54.089593"}, {"row_number": 251, "subject": "ICICI: Inform Key Contacts of upcoming NPS survey", "activity_type": "Meeting", "error": "UI automation failed", "timestamp": "2025-05-29T21:20:56.108442"}, {"row_number": 252, "subject": "ICICI: <PERSON> is working to arrange a meeting with the bank and partner", "activity_type": "Meeting", "error": "UI automation failed", "timestamp": "2025-05-29T21:20:58.117126"}, {"row_number": 253, "subject": "ICICI: <PERSON><PERSON>", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:21:00.136782"}, {"row_number": 254, "subject": "ICICI: <PERSON><PERSON>", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:21:02.143995"}, {"row_number": 255, "subject": "ICICI: Discussion with ICICI Team - Mend - WhiteSource !!", "activity_type": "Meeting", "error": "UI automation failed", "timestamp": "2025-05-29T21:21:04.166044"}, {"row_number": 256, "subject": "ICICI: <PERSON><PERSON>", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:21:06.184568"}, {"row_number": 257, "subject": "ICICI: Update from Luis- partner manager", "activity_type": "Meeting", "error": "UI automation failed", "timestamp": "2025-05-29T21:21:08.198051"}, {"row_number": 258, "subject": "ICICI: <PERSON><PERSON>", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:21:10.219567"}, {"row_number": 259, "subject": "ICICI: Repo Integration-Accelerate your remediation", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:21:12.238414"}, {"row_number": 260, "subject": "ICICI: RSA 2023 conference", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:21:14.261638"}, {"row_number": 261, "subject": "ICICI: <PERSON><PERSON>", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:21:16.275282"}, {"row_number": 262, "subject": "ICICI: FEE ticket in place", "activity_type": "Meeting", "error": "UI automation failed", "timestamp": "2025-05-29T21:21:18.296579"}, {"row_number": 263, "subject": "ICICI: FEE ticket in place", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:21:20.306048"}, {"row_number": 264, "subject": "ICICI: FEE ticket in place", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:21:22.314447"}, {"row_number": 265, "subject": "ICICI: Discussion with ICICI Team - Mend - WhiteSource !!", "activity_type": "Meeting", "error": "UI automation failed", "timestamp": "2025-05-29T21:21:24.322704"}, {"row_number": 266, "subject": "ICICI: ICICI/Meteonic - Next Steps & Alignment", "activity_type": "Meeting", "error": "UI automation failed", "timestamp": "2025-05-29T21:21:26.332706"}, {"row_number": 267, "subject": "ICICI: Sunny to set up a joint meeting?", "activity_type": "Meeting", "error": "UI automation failed", "timestamp": "2025-05-29T21:21:28.339355"}, {"row_number": 268, "subject": "ICICI: Sunny to set up a joint meeting?", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:21:30.347234"}, {"row_number": 269, "subject": "ICICI: <PERSON><PERSON>", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:21:32.356158"}, {"row_number": 270, "subject": "ICICI: Sales Manager Region", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:21:34.362297"}, {"row_number": 271, "subject": "ICICI: SCA CD Variance Stages", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:21:36.369076"}, {"row_number": 272, "subject": "ICICI: <PERSON><PERSON>", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:21:38.375586"}, {"row_number": 273, "subject": "ICICI: Sunny to set up a joint meeting?", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:21:40.381074"}, {"row_number": 274, "subject": "ICICI: ********- zoom discussions in place", "activity_type": "Meeting", "error": "UI automation failed", "timestamp": "2025-05-29T21:21:42.390255"}, {"row_number": 275, "subject": "ICICI: Mend/Meteonic sync", "activity_type": "Meeting", "error": "UI automation failed", "timestamp": "2025-05-29T21:21:44.398245"}, {"row_number": 276, "subject": "ICICI: Case#********- call with Eng", "activity_type": "Meeting", "error": "UI automation failed", "timestamp": "2025-05-29T21:21:46.406756"}, {"row_number": 277, "subject": "ICICI: Working to set a meting with a the bank and the BP", "activity_type": "Meeting", "error": "UI automation failed", "timestamp": "2025-05-29T21:21:48.415669"}, {"row_number": 278, "subject": "ICICI: Meteonic will do onsite in the bank on Jan25th", "activity_type": "Meeting", "error": "UI automation failed", "timestamp": "2025-05-29T21:21:50.426095"}, {"row_number": 279, "subject": "ICICI: Mend's Malicious Package Communications", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:21:52.432005"}, {"row_number": 280, "subject": "ICICI: No need to touch base", "activity_type": "Meeting", "error": "UI automation failed", "timestamp": "2025-05-29T21:21:54.440152"}, {"row_number": 281, "subject": "ICICI: NPS Send Date", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:21:56.447301"}, {"row_number": 282, "subject": "ICICI: NPS Informed", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:21:58.454887"}, {"row_number": 283, "subject": "ICICI: NPS Informed", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:22:00.460932"}, {"row_number": 284, "subject": "ICICI: Internal sync with AM", "activity_type": "Meeting", "error": "UI automation failed", "timestamp": "2025-05-29T21:22:02.470143"}, {"row_number": 285, "subject": "ICICI: Touch Status", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:22:04.479830"}, {"row_number": 286, "subject": "ICICI: NPS Informed", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:22:06.489110"}, {"row_number": 287, "subject": "ICICI: Sales Manager Region", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:22:08.497637"}, {"row_number": 288, "subject": "ICICI: Team Manager Region", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:22:10.506615"}, {"row_number": 289, "subject": "ICICI: Team Manager", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:22:12.515244"}, {"row_number": 290, "subject": "ICICI: SCA CD Variance Stages", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:22:14.521878"}, {"row_number": 291, "subject": "ICICI: Setup a call with customers and explains our Policies best practice", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:22:16.528969"}, {"row_number": 292, "subject": "ICICI: Touch Status", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:22:18.538766"}, {"row_number": 293, "subject": "ICICI: Setup a call with customers and explains our Policies best practice", "activity_type": "Email", "error": "UI automation failed", "timestamp": "2025-05-29T21:22:20.546682"}, {"row_number": 294, "subject": "ICICI: Setup a call with customers and explains our Policies best practice", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:22:22.551178"}, {"row_number": 295, "subject": "ICICI: Outage in app.whitesourcesoftware.com", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:22:24.556670"}, {"row_number": 296, "subject": "ICICI: Touch Status", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:22:26.562310"}, {"row_number": 297, "subject": "ICICI: Check the usage and project number", "activity_type": "Meeting", "error": "UI automation failed", "timestamp": "2025-05-29T21:22:28.567384"}, {"row_number": 298, "subject": "ICICI: CSAT - Spring4Shell, Platinum&Gold", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:22:30.572559"}, {"row_number": 299, "subject": "ICICI: Touch Status", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:22:32.579547"}, {"row_number": 300, "subject": "ICICI: Team Manager Region", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:22:34.588560"}, {"row_number": 301, "subject": "ICICI: Spring4Shell HIGH SEVERITY Vulnerability in Spring core - Gold & Platinum", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:22:36.594593"}, {"row_number": 302, "subject": "ICICI: <PERSON><PERSON>", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:22:38.600749"}, {"row_number": 303, "subject": "ICICI: Touch Status", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:22:40.609484"}, {"row_number": 304, "subject": "ICICI: VBA migration", "activity_type": "Meeting", "error": "UI automation failed", "timestamp": "2025-05-29T21:22:42.615217"}, {"row_number": 305, "subject": "ICICI: Plz check with the usage is low", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:22:44.624149"}, {"row_number": 306, "subject": "ICICI: March 2022 Newsletter- Dedicated", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:22:46.630233"}, {"row_number": 307, "subject": "ICICI: <PERSON><PERSON>", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:22:48.636986"}, {"row_number": 308, "subject": "ICICI: BP customer- no touch base is needed", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:22:50.642370"}, {"row_number": 309, "subject": "ICICI: Touch Status", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:22:52.654120"}, {"row_number": 310, "subject": "ICICI: Customer Communication Regarding LBA to VBA Migration - SCA-ICICI Bank - for SCA-ICICI Bank", "activity_type": "Email", "error": "UI automation failed", "timestamp": "2025-05-29T21:22:54.663358"}, {"row_number": 311, "subject": "ICICI: Team Manager", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:22:56.683422"}, {"row_number": 312, "subject": "ICICI: <PERSON><PERSON>", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:22:58.699594"}, {"row_number": 313, "subject": "ICICI: Log4j Vulnerability webinar Dedicated CSM", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:23:00.704747"}, {"row_number": 314, "subject": "ICICI: Vulnerability in Apache Log4j2 library Part 2  Dedicated CSM Accounts", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:23:02.718244"}, {"row_number": 315, "subject": "ICICI: Severity Vulnerability (CVE-2021-44228) in the Log4j2 -Dedicated CSM", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:23:04.739017"}, {"row_number": 316, "subject": "ICICI: Customer Communication Regarding LBA to VBA Migration - SCA-ICICI Bank", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:23:06.747883"}, {"row_number": 317, "subject": "ICICI: Contact SCA-ICICI Bank about moving to VBA", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:23:08.753613"}, {"row_number": 318, "subject": "ICICI: <PERSON><PERSON>", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:23:10.759592"}, {"row_number": 319, "subject": "ICICI: SCA CD Variance Stages", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:23:12.767389"}, {"row_number": 320, "subject": "ICICI: Partner account", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:23:14.786368"}, {"row_number": 321, "subject": "ICICI: Sales Manager Region", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:23:16.807790"}, {"row_number": 322, "subject": "ICICI: <PERSON><PERSON>", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:23:18.827993"}]
#!/usr/bin/env python3
"""
🐺 Wild Weasel - ENHANCED Totango to Gainsight Data Transformer
================================================================================
Mission: Transform Totango data to EXACT Gainsight API payload format
Target: Match gainsight_payload.json structure exactly with proper user mapping

ENHANCED FEATURES:
1. ✅ Exact Gainsight payload format matching
2. ✅ Proper Gainsight user mapping (not Totango users)
3. ✅ Empty ID field for draft API injection
4. ✅ Enhanced touchpoint reason mapping
5. ✅ Comprehensive data validation
6. ✅ Best libraries for performance and reliability
"""

import json
import os
import sys
from datetime import datetime
from typing import List, Dict, Any, Optional
import logging
from dataclasses import dataclass
import pandas as pd
import numpy as np
from pathlib import Path

# Enhanced logging with structured format
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("/Users/<USER>/Desktop/wild_weasel_gainsight_migration/totango_transformer.log"),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger("WildWeasel-EnhancedTransformer")

@dataclass
class GainsightUser:
    """Gainsight user structure"""
    gsId: str
    name: str
    email: str
    eid: Optional[str] = None
    esys: Optional[str] = None
    pp: str = ""

@dataclass 
class GainsightCompany:
    """Gainsight company structure"""
    id: str
    obj: str = "Company"
    eobj: str = "Account"
    eid: Optional[str] = None
    esys: str = "SALESFORCE"
    lbl: str = "ICICI"
    dsp: bool = True
    base: bool = True

class EnhancedTotangoTransformer:
    """Enhanced transformer that creates EXACT Gainsight API payloads"""
    
    def __init__(self):
        # File paths
        self.base_path = Path("/Users/<USER>/Desktop/wild_weasel_gainsight_migration")
        self.totango_data_dir = Path("/Users/<USER>/Desktop/Totango")
        
        # Input files
        self.icici_file = self.totango_data_dir / "ICICI.json"
        self.id_mapping_file = self.totango_data_dir / "ID.json"
        self.touchpoint_reasons_file = self.totango_data_dir / "Touchpoint_reason.JSON"
        self.gainsight_payload_template = self.base_path / "Gainsight_payload.json"
        
        # Output files
        self.output_file = self.base_path / "gainsight_api_payload_email_activities_ENHANCED.json"
        
        # Gainsight user mapping (from your Gainsight_payload.json)
        self.gainsight_users = {
            "migration_user": GainsightUser(
                gsId="1P01E316G9DAPFOLE6V9Z586JRYFUW88XLGG",  # Migration user ID
                name="Migration User",
                email="<EMAIL>"
            ),
            "ram_prasad": GainsightUser(
                gsId="1P01E316G9DAPFOLE6SOOUG71XRMN5F3PLER",  # Ram Prasad's actual ID
                name="Ram Prasad", 
                email="<EMAIL>",
                pp="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAHgAAAB4CAYAAAA5ZDbSAAAAAXNSR0IArs4c6QAAB+9JREFUeF7tnXlQVVUcx7/siIgL4r6gAqKGYqShhEthauZGiYqZWyqOY5pZokYzLTZUjlvNNFo2uWRqphOjaS6ZpZKamJm7oZELphjIJtuj+b2GP/Td+95792283/zOX47vnHt+3+/n/c6795zfHTzyCoqrIY2tAx4CmC1bozABzJuvAGbOVwALYO4OMNcnv8ECmLkDzOVJBgtg5g4wlycZLICZO8BcnmSwAGbuAHN5ksECmLkDzOVJBgtg5g4wlycZLICZO8BcnmSwAGbuAHN5ksECmLkDzOVJBgtg5g4wlycZLICZO8BcnmSwAGbuAHN5ksECmLkDzOVJBgtg5g4wlycZLICZO8BcnmSwAGbuAHN5"
            )
        }
        
        # Gainsight company
        self.gainsight_company = GainsightCompany(
            id="1P02IPMAEL4M3CQGY4K5FHU22D2HHT11KCKU",
            lbl="ICICI"
        )
        
        # Touchpoint reason mapping (enhanced)
        self.touchpoint_mapping = {
            "campaign_touch": "ADOPTION",
            "account_alert": "INTELLIGENCE", 
            "note": "ADOPTION",
            "email": "ADOPTION",
            "newsletter": "MARKETING",
            "security": "INTELLIGENCE",
            "renewal": "RENEWAL",
            "support": "SUPPORT",
            "default": "ADOPTION"
        }
        
        # Load template payload structure
        self.template_payload = self.load_gainsight_template()
        
    def load_gainsight_template(self) -> Dict[str, Any]:
        """Load the Gainsight payload template for exact structure matching"""
        try:
            with open(self.gainsight_payload_template, 'r') as f:
                template = json.load(f)
            logger.info("✅ Loaded Gainsight payload template")
            return template
        except Exception as e:
            logger.error(f"❌ Failed to load Gainsight template: {e}")
            return {}
    
    def load_totango_data(self) -> List[Dict[str, Any]]:
        """Load Totango data with enhanced error handling"""
        try:
            if not self.icici_file.exists():
                logger.error(f"❌ Totango data file not found: {self.icici_file}")
                return []
                
            with open(self.icici_file, 'r') as f:
                data = json.load(f)
                
            logger.info(f"📊 Loaded {len(data)} Totango activities")
            return data
            
        except Exception as e:
            logger.error(f"❌ Failed to load Totango data: {e}")
            return []
    
    def load_supporting_data(self) -> tuple[Dict[str, Any], Dict[str, Any]]:
        """Load ID mappings and touchpoint reasons"""
        id_mappings = {}
        touchpoint_reasons = {}
        
        # Load ID mappings
        try:
            if self.id_mapping_file.exists():
                with open(self.id_mapping_file, 'r') as f:
                    id_data = json.load(f)
                    id_mappings = {item['id']: item for item in id_data}
                logger.info(f"✅ Loaded {len(id_mappings)} ID mappings")
        except Exception as e:
            logger.warning(f"⚠️ Could not load ID mappings: {e}")
        
        # Load touchpoint reasons
        try:
            if self.touchpoint_reasons_file.exists():
                with open(self.touchpoint_reasons_file, 'r') as f:
                    touchpoint_data = json.load(f)
                    touchpoint_reasons = {item['id']: item for item in touchpoint_data}
                logger.info(f"✅ Loaded {len(touchpoint_reasons)} touchpoint reasons")
        except Exception as e:
            logger.warning(f"⚠️ Could not load touchpoint reasons: {e}")
            
        return id_mappings, touchpoint_reasons
    
    def is_email_activity(self, activity: Dict[str, Any], id_mappings: Dict[str, Any]) -> bool:
        """Enhanced email activity detection"""
        activity_type = activity.get('type', '')
        properties = activity.get('properties', {})
        
        # Method 1: Direct email type mapping
        activity_type_id = properties.get('activity_type_id', '')
        if activity_type_id in id_mappings:
            display_name = id_mappings[activity_type_id].get('display_name', '').lower()
            if 'email' in display_name:
                return True
        
        # Method 2: Email-related activity types
        email_types = ['campaign_touch', 'account_alert', 'email', 'note']
        if activity_type in email_types:
            # Verify content is email-related
            content_fields = [
                properties.get('subject', ''),
                properties.get('name', ''),  
                properties.get('description', ''),
                properties.get('content', '')
            ]
            
            email_indicators = ['email', 'newsletter', 'campaign', 'communication', 'message']
            content_text = ' '.join(content_fields).lower()
            
            return any(indicator in content_text for indicator in email_indicators)
        
        return False
    
    def map_touchpoint_reason(self, activity: Dict[str, Any]) -> str:
        """Enhanced touchpoint reason mapping"""
        activity_type = activity.get('type', '')
        properties = activity.get('properties', {})
        
        # Check content for specific keywords
        content_text = ' '.join([
            properties.get('subject', ''),
            properties.get('name', ''),
            properties.get('description', ''),
            activity_type
        ]).lower()
        
        # Priority mapping based on content
        if any(word in content_text for word in ['renewal', 'renew', 'contract']):
            return "RENEWAL"
        elif any(word in content_text for word in ['security', 'vulnerability', 'cve', 'alert']):
            return "INTELLIGENCE"  
        elif any(word in content_text for word in ['newsletter', 'marketing', 'campaign']):
            return "MARKETING"
        elif any(word in content_text for word in ['support', 'help', 'issue', 'problem']):
            return "SUPPORT"
        elif any(word in content_text for word in ['adoption', 'usage', 'feature']):
            return "ADOPTION"
        else:
            # Fallback based on activity type
            return self.touchpoint_mapping.get(activity_type, self.touchpoint_mapping["default"])
    
    def determine_user(self, activity: Dict[str, Any]) -> GainsightUser:
        """Determine which Gainsight user to use (always use Gainsight users, not Totango)"""
        properties = activity.get('properties', {})
        from_user = properties.get('from_user', '').lower()
        
        # Always use Migration User for automated migrations
        # This ensures consistency and avoids Totango user conflicts
        return self.gainsight_users["migration_user"]
    
    def create_gainsight_payload(self, totango_activity: Dict[str, Any]) -> Dict[str, Any]:
        """Create exact Gainsight API payload matching the template structure"""
        properties = totango_activity.get('properties', {})
        
        # Extract data from Totango activity
        subject = (
            properties.get('subject') or 
            properties.get('name') or 
            f"Email Activity from Totango - {totango_activity.get('type', 'Unknown')}"
        )
        
        # Clean subject
        if subject.startswith('Totango Campaign:'):
            subject = subject.replace('Totango Campaign:', '').strip()
        
        # Extract content
        content = (
            properties.get('description') or
            properties.get('content') or
            properties.get('notes') or
            f"Email activity migrated from Totango - Type: {totango_activity.get('type')}"
        )
        
        # Convert timestamp
        timestamp_ms = totango_activity.get('timestamp', int(datetime.now().timestamp() * 1000))
        
        # Determine user and touchpoint reason
        user = self.determine_user(totango_activity)
        touchpoint_reason = self.map_touchpoint_reason(totango_activity)
        
        # Create payload matching EXACT Gainsight structure
        gainsight_payload = {
            "lastModifiedByUser": {
                "gsId": user.gsId,
                "name": user.name,
                "eid": user.eid,
                "esys": user.esys,
                "pp": user.pp
            },
            "note": {
                "customFields": {
                    "internalAttendees": [
                        {
                            "id": user.gsId,
                            "obj": "User",
                            "name": user.name,
                            "email": user.email,
                            "eid": user.eid,
                            "eobj": "User",
                            "epp": None,
                            "esys": "SALESFORCE",
                            "sys": "GAINSIGHT",
                            "pp": user.pp
                        }
                    ],
                    "externalAttendees": [],
                    "ant__Status1552512571338": None,
                    "Ant__Touchpoint_Reason__c": touchpoint_reason,
                    "Ant__Flow_Type__c": "Email Communication"
                },
                "type": "EMAIL",
                "subject": subject[:255],  # Gainsight field limit
                "activityDate": timestamp_ms,
                "content": f"&lt;p&gt;{content}&lt;/p&gt;",  # HTML encoded as in template
                "plainText": content,
                "trackers": None
            },
            "mentions": [],
            "relatedRecords": {},
            "meta": {
                "activityTypeId": "c81eeccf-2aa1-45bc-be71-5377f148e1e9",  # Email activity type ID
                "ctaId": None,
                "source": "C360",
                "hasTask": False,
                "emailSent": False,
                "systemType": "GAINSIGHT",
                "notesTemplateId": None
            },
            "author": {
                "id": user.gsId,
                "obj": "User", 
                "name": user.name,
                "email": user.email,
                "eid": user.eid,
                "eobj": "User",
                "epp": None,
                "esys": "SALESFORCE",
                "sys": "GAINSIGHT",
                "pp": user.pp
            },
            "syncedToSFDC": False,
            "id": "",  # ← EMPTY ID FOR DRAFT API INJECTION
            "tasks": [],
            "attachments": [],
            "contexts": [
                {
                    "id": self.gainsight_company.id,
                    "obj": self.gainsight_company.obj,
                    "eobj": self.gainsight_company.eobj,
                    "eid": self.gainsight_company.eid,
                    "esys": self.gainsight_company.esys,
                    "lbl": self.gainsight_company.lbl,
                    "dsp": self.gainsight_company.dsp,
                    "base": self.gainsight_company.base
                }
            ],
            "_migration_metadata": {
                "totango_original_id": totango_activity.get('id', ''),
                "totango_activity_type": totango_activity.get('type', ''),
                "migration_timestamp": datetime.now().isoformat(),
                "migration_source": "Wild Weasel Enhanced Transformer",
                "original_timestamp": timestamp_ms,
                "touchpoint_mapping": touchpoint_reason,
                "original_subject": properties.get('subject', ''),
                "transformation_version": "v2.0"
            }
        }
        
        return gainsight_payload
    
    def validate_payload(self, payload: Dict[str, Any]) -> tuple[bool, List[str]]:
        """Validate payload structure against Gainsight requirements"""
        errors = []
        
        # Check required top-level fields
        required_fields = ['note', 'meta', 'author', 'contexts', 'id']
        for field in required_fields:
            if field not in payload:
                errors.append(f"Missing required field: {field}")
        
        # Check note structure
        if 'note' in payload:
            note = payload['note']
            if 'type' not in note or note['type'] != 'EMAIL':
                errors.append("note.type must be 'EMAIL'")
            if 'subject' not in note or not note['subject']:
                errors.append("note.subject is required")
        
        # Check contexts
        if 'contexts' in payload:
            if not payload['contexts'] or not isinstance(payload['contexts'], list):
                errors.append("contexts must be non-empty list")
            elif not payload['contexts'][0].get('id'):
                errors.append("contexts[0].id is required")
        
        # Check author
        if 'author' in payload:
            author = payload['author']
            if not author.get('id') or not author.get('email'):
                errors.append("author.id and author.email are required")
        
        return len(errors) == 0, errors
    
    def transform_activities(self) -> List[Dict[str, Any]]:
        """Main transformation method"""
        logger.info("🐺 Starting Enhanced Totango to Gainsight Transformation...")
        
        # Load data
        totango_activities = self.load_totango_data()
        if not totango_activities:
            return []
        
        id_mappings, touchpoint_reasons = self.load_supporting_data()
        
        # Filter and transform email activities
        gainsight_payloads = []
        stats = {
            "total_activities": len(totango_activities),
            "email_activities": 0,
            "valid_payloads": 0,
            "invalid_payloads": 0,
            "activity_types": {}
        }
        
        for activity in totango_activities:
            activity_type = activity.get('type', 'unknown')
            stats["activity_types"][activity_type] = stats["activity_types"].get(activity_type, 0) + 1
            
            # Check if it's an email activity
            if self.is_email_activity(activity, id_mappings):
                stats["email_activities"] += 1
                
                try:
                    # Transform to Gainsight format
                    gainsight_payload = self.create_gainsight_payload(activity)
                    
                    # Validate payload
                    is_valid, errors = self.validate_payload(gainsight_payload)
                    
                    if is_valid:
                        gainsight_payloads.append(gainsight_payload)
                        stats["valid_payloads"] += 1
                        logger.debug(f"✅ Transformed: {gainsight_payload['note']['subject'][:50]}...")
                    else:
                        stats["invalid_payloads"] += 1
                        logger.error(f"❌ Invalid payload: {errors}")
                        
                except Exception as e:
                    stats["invalid_payloads"] += 1
                    logger.error(f"❌ Transformation error: {e}")
        
        # Log statistics
        logger.info(f"📊 TRANSFORMATION STATISTICS:")
        logger.info(f"   Total Totango activities: {stats['total_activities']}")
        logger.info(f"   Email activities found: {stats['email_activities']}")
        logger.info(f"   Valid Gainsight payloads: {stats['valid_payloads']}")
        logger.info(f"   Invalid payloads: {stats['invalid_payloads']}")
        
        logger.info(f"🎯 Activity Type Breakdown:")
        for atype, count in sorted(stats["activity_types"].items()):
            logger.info(f"   {atype}: {count}")
        
        return gainsight_payloads
    
    def save_payloads(self, payloads: List[Dict[str, Any]]):
        """Save enhanced payloads with metadata"""
        try:
            # Save main payloads file
            with open(self.output_file, 'w') as f:
                json.dump(payloads, f, indent=2, default=str)
            
            logger.info(f"💾 Saved {len(payloads)} enhanced Gainsight payloads to: {self.output_file}")
            
            # Save transformation report
            report_file = self.base_path / f"transformation_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
            
            report = {
                "transformation_summary": {
                    "total_payloads": len(payloads),
                    "transformation_time": datetime.now().isoformat(),
                    "source_format": "Totango Timeline",
                    "target_format": "Gainsight Activity API",
                    "template_matched": str(self.gainsight_payload_template),
                    "user_mapping": "Gainsight Users Only",
                    "company_mapping": "ICICI Bank",
                    "empty_id_fields": sum(1 for p in payloads if p.get('id') == '')
                },
                "payload_samples": payloads[:2] if payloads else [],  # Save first 2 as samples
                "validation_passed": all(self.validate_payload(p)[0] for p in payloads)
            }
            
            with open(report_file, 'w') as f:
                json.dump(report, f, indent=2, default=str)
                
            logger.info(f"📋 Transformation report saved to: {report_file}")
            
        except Exception as e:
            logger.error(f"❌ Failed to save payloads: {e}")
    
    def run_transformation(self):
        """Run the complete transformation process"""
        print("🐺" + "="*80)
        print("  WILD WEASEL - ENHANCED TOTANGO TO GAINSIGHT TRANSFORMER")
        print("="*82)
        print("🎯 TRANSFORMATION FEATURES:")
        print("  ✅ EXACT Gainsight payload format matching") 
        print("  ✅ Proper Gainsight user mapping (Migration User)")
        print("  ✅ Empty ID fields for draft API injection")
        print("  ✅ Enhanced touchpoint reason mapping")
        print("  ✅ Comprehensive validation and error handling")
        print("="*82)
        
        # Run transformation
        enhanced_payloads = self.transform_activities()
        
        if enhanced_payloads:
            # Save results
            self.save_payloads(enhanced_payloads)
            
            # Display results
            print(f"\n🎉 TRANSFORMATION COMPLETED SUCCESSFULLY!")
            print(f"   📊 Generated {len(enhanced_payloads)} Gainsight API payloads")
            print(f"   💾 Saved to: {self.output_file}")
            print(f"   🎯 Ready for Wild Weasel migration!")
            
            # Show sample
            if enhanced_payloads:
                sample = enhanced_payloads[0]
                print(f"\n📝 SAMPLE PAYLOAD:")
                print(f"   Subject: {sample['note']['subject']}")
                print(f"   Author: {sample['author']['name']} ({sample['author']['email']})")
                print(f"   Company: {sample['contexts'][0]['lbl']}")
                print(f"   Touchpoint: {sample['note']['customFields']['Ant__Touchpoint_Reason__c']}")
                print(f"   ID Field: '{sample['id']}' ← Will be filled by draft API")
            
            print(f"\n🚀 NEXT STEPS:")
            print(f"   1. Enhanced payloads are ready for migration")
            print(f"   2. Run Wild Weasel v5 Enhanced: python wild_weasel_agent_v5_enhanced.py")
            print(f"   3. Draft API will inject IDs into empty id fields")
            print(f"   4. Activities will be uploaded to ICICI Bank timeline")
            
        else:
            print(f"\n❌ TRANSFORMATION FAILED:")
            print(f"   No email activities found in Totango data")
            print(f"   Check input files and data sources")
        
        print("="*82)

def main():
    """Main execution function"""
    try:
        transformer = EnhancedTotangoTransformer()
        transformer.run_transformation()
        
    except KeyboardInterrupt:
        print("\n🐺 Transformation interrupted by user")
        sys.exit(0)
    except Exception as e:
        logger.error(f"❌ Transformation failed: {e}")
        import traceback
        logger.error(f"Traceback: {traceback.format_exc()}")
        sys.exit(1)

if __name__ == "__main__":
    main()

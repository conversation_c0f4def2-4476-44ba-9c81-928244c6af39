#!/usr/bin/env python3
"""
🐺 Wild Weasel v5.1 - API Enhanced with Meeting Type Mapping
=============================================================================
Mission: Enhanced with Playwright API testing capabilities for bulk migration
        + Intelligent Totango meeting type → Gainsight activity type mapping

ENHANCEMENTS:
1. ✅ Playwright API Context for authenticated requests
2. ✅ Bulk processing of Totango data
3. ✅ Intelligent Totango meeting type → Gainsight activity type mapping
4. ✅ Parallel processing with rate limiting
5. ✅ Enhanced error handling and retry mechanisms
6. ✅ Real-time migration monitoring and reporting
"""

import json
import os
import sys
import time
import asyncio
import logging
from datetime import datetime, timezone
from typing import Dict, List, Any, Optional, Tuple
from concurrent.futures import ThreadPoolExecutor, as_completed
from dataclasses import dataclass
from playwright.async_api import async_playwright, <PERSON><PERSON>, <PERSON><PERSON>er<PERSON>ontex<PERSON>, APIRequestContext
import aiofiles
import aiohttp

# Setup enhanced logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("/Users/<USER>/Desktop/wild_weasel_gainsight_migration/wild_weasel_v5_api.log"),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger("WildWeasel-v5.1-API")

@dataclass
class MigrationResult:
    """Enhanced result tracking"""
    success: bool
    totango_id: str
    gainsight_id: Optional[str] = None
    method: str = "API"
    error: Optional[str] = None
    timestamp: str = ""
    processing_time: float = 0.0
    retry_count: int = 0
    activity_type: str = ""
    meeting_type: str = ""

@dataclass
class MigrationStats:
    """Real-time migration statistics"""
    total_items: int = 0
    processed: int = 0
    successful: int = 0
    failed: int = 0
    api_successful: int = 0
    api_failed: int = 0
    ui_successful: int = 0
    ui_failed: int = 0
    start_time: Optional[datetime] = None
    end_time: Optional[datetime] = None
    average_processing_time: float = 0.0
    activity_type_stats: Dict[str, int] = None

class TotangoDataTransformer:
    """Transforms Totango events into Gainsight API payloads with meeting type mapping"""
    
    def __init__(self):
        # Base event type mapping for non-meeting events
        self.activity_type_mapping = {
            "automated_attribute_change": "Update",
            "webhook": "Integration Event", 
            "campaign_touch": "Email",  # Marketing campaigns usually email
            "account_alert": "Update",
            "meeting": "Meeting",
            "email": "Email",
            "call": "Call",
            "task": "Update"
        }
        
        # Totango meeting type → Gainsight activity type mapping
        self.meeting_type_mapping = {
            # Default Gainsight Types
            "Email": "Email",
            "email": "Email",
            "Telephone Call": "Call", 
            "telephone call": "Call",
            "phone call": "Call",
            "call": "Call",
            "Web Meeting": "Meeting",
            "web meeting": "Meeting",
            "meeting": "Meeting",
            "Internal Note": "Update",
            "internal note": "Update",
            "note": "Update",
            
            # Custom Gainsight Types
            "In-Person Meeting": "In-Person Meeting",
            "in-person meeting": "In-Person Meeting",
            "Gong Call": "Gong Call",
            "gong call": "Gong Call",
            "Feedback": "Feedback",
            "feedback": "Feedback",
            "Inbound": "Inbound",
            "inbound": "Inbound",
            "Slack": "Slack",
            "slack": "Slack",
            
            # Additional common variations
            "video call": "Meeting",
            "Video Call": "Meeting",
            "zoom meeting": "Meeting",
            "Zoom Meeting": "Meeting",
            "teams meeting": "Meeting",
            "Teams Meeting": "Meeting",
            "conference call": "Meeting",
            "Conference Call": "Meeting",
            "demo": "Meeting",
            "Demo": "Meeting",
            "presentation": "Meeting",
            "Presentation": "Meeting"
        }
        
        # Gainsight activity type ID mapping (for meta.activityTypeId)
        self.gainsight_activity_type_ids = {
            "Email": "1P012N32P8NVH8P0JNWLW77LU",  # Email activity type ID
            "Call": "1P012N32P8NVH8P0JNWLW77LU",  # Default to Email for now
            "Meeting": "1P012N32P8NVH8P0JNWLW77LU",  # Default to Email for now
            "Update": "1P012N32P8NVH8P0JNWLW77LU",  # Default to Email for now
            "In-Person Meeting": "1P012N32P8NVH8P0JNWLW77LU",  # Default to Email for now
            "Gong Call": "1P012N32P8NVH8P0JNWLW77LU",  # Default to Email for now
            "Feedback": "1P012N32P8NVH8P0JNWLW77LU",  # Default to Email for now
            "Inbound": "1P012N32P8NVH8P0JNWLW77LU",  # Default to Email for now
            "Slack": "1P012N32P8NVH8P0JNWLW77LU"  # Default to Email for now
        }
        
        self.company_mapping = {
            "0015p00005R7ysqAAB": "1P02IPMAEL4M3CQGY4K5FHU22D2HHT11KCKU"  # ICICI Bank mapping
        }
    
    def transform_to_gainsight(self, totango_event: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """Transform a single Totango event to Gainsight API payload"""
        try:
            # Extract basic information
            event_id = totango_event.get('id', '')
            timestamp = totango_event.get('timestamp', 0)
            event_type = totango_event.get('type', 'unknown')
            properties = totango_event.get('properties', {})
            account = totango_event.get('account', {})
            account_id = account.get('id', '')
            
            # Map to Gainsight company ID
            gainsight_company_id = self.company_mapping.get(account_id)
            if not gainsight_company_id:
                logger.warning(f"No company mapping found for Totango account: {account_id}")
                return None
            
            # Convert timestamp to ISO format
            if isinstance(timestamp, (int, float)) and timestamp > 0:
                activity_date = datetime.fromtimestamp(timestamp / 1000, tz=timezone.utc).isoformat()
            else:
                activity_date = datetime.now(tz=timezone.utc).isoformat()
            
            # Determine activity type with meeting type mapping
            activity_type, meeting_type = self._determine_activity_type(event_type, properties)
            
            # Create subject based on event type and properties
            subject = self._create_subject(event_type, properties, activity_type)
            
            # Create content from properties
            content = self._create_content(event_type, properties, totango_event, meeting_type)
            
            # Build Gainsight API payload
            payload = {
                "lastModifiedByUser": {
                    "gsId": "1P01E316G9DAPFOLE6SOOUG71XRMN5F3PLER",  # Default user
                    "name": "Wild Weasel Migration Agent",
                    "eid": None,
                    "esys": None,
                    "pp": ""
                },
                "note": {
                    "customFields": {
                        "internalAttendees": [],
                        "externalAttendees": [],
                        "Ant__Source_System__c": "Totango",
                        "Ant__Source_ID__c": event_id,
                        "Ant__Migration_Timestamp__c": datetime.now(tz=timezone.utc).isoformat(),
                        "Ant__Original_Meeting_Type__c": meeting_type if meeting_type else "N/A"
                    },
                    "type": activity_type,
                    "subject": subject,
                    "activityDate": activity_date,
                    "content": content,
                    "plainText": self._strip_html(content),
                    "trackers": None
                },
                "mentions": [],
                "relatedRecords": None,
                "meta": {
                    "activityTypeId": self.gainsight_activity_type_ids.get(activity_type, "1P012N32P8NVH8P0JNWLW77LU"),
                    "ctaId": None,
                    "source": "TOTANGO_MIGRATION",
                    "hasTask": False,
                    "emailSent": activity_type == "Email",  # Set emailSent to True for Email types
                    "systemType": "GAINSIGHT",
                    "notesTemplateId": None
                },
                "author": {
                    "id": "1P01E316G9DAPFOLE6SOOUG71XRMN5F3PLER",
                    "obj": "User",
                    "name": "Wild Weasel Migration Agent",
                    "email": "<EMAIL>",
                    "eid": None,
                    "eobj": "User",
                    "epp": None,
                    "esys": "TOTANGO",
                    "sys": "GAINSIGHT",
                    "pp": ""
                },
                "syncedToSFDC": False,
                "tasks": [],
                "attachments": [],
                "contexts": [
                    {
                        "id": gainsight_company_id,
                        "base": True,
                        "obj": "Company",
                        "lbl": "ICICI Bank",
                        "eid": account_id,
                        "eobj": "Account",
                        "eurl": None,
                        "esys": "TOTANGO",
                        "dsp": True
                    }
                ]
            }
            
            return payload
            
        except Exception as e:
            logger.error(f"Error transforming Totango event {event_id}: {e}")
            return None
    
    def _determine_activity_type(self, event_type: str, properties: Dict[str, Any]) -> Tuple[str, str]:
        """Determine the correct Gainsight activity type based on event type and meeting type"""
        # First check for meeting_type or meeting_type_name in properties
        meeting_type = None
        for key in ['meeting_type', 'meeting_type_name', 'type', 'activity_type']:
            if key in properties and properties[key]:
                meeting_type = str(properties[key]).strip()
                break
        
        # If we found a meeting type, use the meeting type mapping
        if meeting_type:
            mapped_type = self.meeting_type_mapping.get(meeting_type)
            if mapped_type:
                logger.debug(f"Mapped meeting type '{meeting_type}' to '{mapped_type}'")
                return mapped_type, meeting_type
            else:
                logger.debug(f"Unknown meeting type '{meeting_type}', checking activity type mapping")
        
        # Fall back to event type mapping
        activity_type = self.activity_type_mapping.get(event_type, "Update")
        logger.debug(f"Using activity type '{activity_type}' for event type '{event_type}'")
        return activity_type, meeting_type or ""
    
    def _create_subject(self, event_type: str, properties: Dict[str, Any], activity_type: str) -> str:
        """Create meaningful subject line"""
        # Check if there's a specific subject in properties
        if 'subject' in properties and properties['subject']:
            return str(properties['subject'])[:255]  # Gainsight has a 255 char limit
        
        if event_type == "campaign_touch":
            return f"Campaign: {properties.get('name', 'Unknown Campaign')}"
        elif event_type == "automated_attribute_change":
            display_name = properties.get('display_name', 'Unknown Attribute')
            action = properties.get('action', 'updated')
            return f"Attribute {action.title()}: {display_name}"
        elif event_type == "webhook":
            return f"Webhook: {properties.get('name', 'Integration Event')}"
        elif event_type == "account_alert":
            return properties.get('title', 'Account Alert')
        else:
            # Check for meeting type to create better subject
            meeting_type = properties.get('meeting_type') or properties.get('meeting_type_name')
            if meeting_type:
                return f"{activity_type}: Totango Activity"
            return f"Totango {event_type.replace('_', ' ').title()}"
    
    def _create_content(self, event_type: str, properties: Dict[str, Any], full_event: Dict[str, Any], meeting_type: str) -> str:
        """Create detailed content from event data"""
        content_parts = []
        
        # Add event type and timestamp
        timestamp = full_event.get('timestamp', 0)
        if timestamp:
            date_str = datetime.fromtimestamp(timestamp / 1000, tz=timezone.utc).strftime('%Y-%m-%d %H:%M:%S UTC')
            content_parts.append(f"<strong>Event Time:</strong> {date_str}")
        
        content_parts.append(f"<strong>Event Type:</strong> {event_type.replace('_', ' ').title()}")
        
        # Add meeting type information if available
        if meeting_type:
            content_parts.append(f"<strong>Original Meeting Type:</strong> {meeting_type}")
        
        # Add specific content based on event type
        if event_type == "campaign_touch":
            content_parts.extend([
                f"<strong>Campaign:</strong> {properties.get('name', 'Unknown')}",
                f"<strong>Subject:</strong> {properties.get('subject', 'N/A')}",
                f"<strong>Description:</strong> {properties.get('description', 'N/A')}",
                f"<strong>Targeted Users:</strong> {properties.get('targeted_users_count', 'N/A')}"
            ])
        elif event_type == "automated_attribute_change":
            content_parts.extend([
                f"<strong>Entity:</strong> {properties.get('entity_name', 'N/A')}",
                f"<strong>Attribute:</strong> {properties.get('display_name', 'N/A')}",
                f"<strong>Action:</strong> {properties.get('action', 'N/A')}",
                f"<strong>Previous Value:</strong> {properties.get('prev_value', 'N/A')}",
                f"<strong>New Value:</strong> {properties.get('new_value', 'N/A')}"
            ])
        elif event_type == "webhook":
            content_parts.extend([
                f"<strong>Entity:</strong> {properties.get('entity_name', 'N/A')}",
                f"<strong>Status:</strong> {properties.get('status', 'N/A')}",
                f"<strong>Request Type:</strong> {properties.get('request_type', 'N/A')}",
                f"<strong>URL:</strong> {properties.get('request_url', 'N/A')}"
            ])
        elif event_type == "account_alert":
            content_parts.extend([
                f"<strong>Alert Type:</strong> {properties.get('alert_type', 'N/A')}",
                f"<strong>Description:</strong> {properties.get('description', 'N/A')}",
                f"<strong>From User:</strong> {properties.get('from_user', 'N/A')}"
            ])
        
        # Add any additional properties
        excluded_keys = {'entity_name', 'display_name', 'action', 'prev_value', 'new_value', 
                        'name', 'subject', 'description', 'targeted_users_count', 'status',
                        'request_type', 'request_url', 'alert_type', 'from_user', 'last_updated_internal',
                        'meeting_type', 'meeting_type_name', 'type', 'activity_type'}  # Exclude meeting type fields as they're handled above
        
        additional_props = {k: v for k, v in properties.items() if k not in excluded_keys and v}
        if additional_props:
            content_parts.append("<strong>Additional Properties:</strong>")
            for key, value in additional_props.items():
                content_parts.append(f"• {key.replace('_', ' ').title()}: {value}")
        
        return "<br/>".join(content_parts)
    
    def _strip_html(self, text: str) -> str:
        """Strip HTML tags for plain text version"""
        import re
        return re.sub(r'<[^>]+>', '', text).replace('&nbsp;', ' ').strip()

class WildWeaselAgentV5Enhanced:
    """Enhanced Wild Weasel with Playwright API capabilities and meeting type mapping"""
    
    def __init__(self):
        self.config = {
            "gainsight_url": "https://demo-emea1.gainsightcloud.com",
            "login_url": "https://demo-emea1.gainsightcloud.com/v1/ui/home",
            "activity_api_url": "https://demo-emea1.gainsightcloud.com/v1/ant/v2/activity",
            "drafts_api_url": "https://demo-emea1.gainsightcloud.com/v1/ant/v2/activity/drafts",
            
            # Data source
            "totango_data_file": "/Users/<USER>/Desktop/totango/ICICI_processed.json",
            
            # Batch processing settings
            "batch_size": 10,
            "max_retries": 3,
            "request_timeout": 30000,
            "rate_limit_delay": 1.0,
            
            # Output files
            "results_file": "/Users/<USER>/Desktop/wild_weasel_gainsight_migration/migration_results_v5.json",
            "failed_file": "/Users/<USER>/Desktop/wild_weasel_gainsight_migration/migration_failed_v5.json",
            "stats_file": "/Users/<USER>/Desktop/wild_weasel_gainsight_migration/migration_stats_v5.json"
        }
        
        # Authentication
        self.credentials = {
            "username": os.getenv("GAINSIGHT_USERNAME") or input("Gainsight Username: "),
            "password": os.getenv("GAINSIGHT_PASSWORD") or input("Gainsight Password: ")
        }
        
        # Migration tracking
        self.stats = MigrationStats()
        self.stats.activity_type_stats = {}
        self.results: List[MigrationResult] = []
        self.totango_data: List[Dict[str, Any]] = []
        self.transformer = TotangoDataTransformer()
        
        # Playwright objects
        self.playwright: Optional[Playwright] = None
        self.browser = None
        self.context: Optional[BrowserContext] = None
        self.api_context: Optional[APIRequestContext] = None
    
    def mission_brief(self):
        """Display the Enhanced Wild Weasel mission brief"""
        print("🐺" + "="*80)
        print("  WILD WEASEL v5.1 - API ENHANCED + MEETING TYPE MAPPING")
        print("="*82)
        print("🚀 ENHANCED FEATURES:")
        print("  ✅ PLAYWRIGHT API CONTEXT:")
        print("     → Authenticated API request context")
        print("     → Session management and cookie handling")
        print("     → Automatic retry mechanisms")
        print("  ✅ INTELLIGENT MEETING TYPE MAPPING:")
        print("     → Totango meeting_type → Gainsight activity type")
        print("     → Support for custom Gainsight activity types")
        print("     → Automatic fallback for unknown types")
        print("  ✅ BULK PROCESSING PIPELINE:")
        print("     → Intelligent data transformation")
        print("     → Parallel batch processing")
        print("     → Rate limiting and throttling")
        print("  ✅ ENHANCED MONITORING:")
        print("     → Activity type statistics")
        print("     → Real-time migration statistics")
        print("     → Detailed error tracking")
        print("="*82)
        print(f"📊 Data Source: {self.config['totango_data_file']}")
        print(f"🎯 Target: Gainsight Demo EMEA1")
        print(f"📦 Batch Size: {self.config['batch_size']} activities per batch")
        print("="*82)
        
        # Show mapping examples
        print("🔄 MEETING TYPE MAPPINGS:")
        print("  📧 Email → Email")
        print("  📞 Telephone Call → Call")
        print("  🌐 Web Meeting → Meeting")
        print("  📝 Internal Note → Update")
        print("  🤝 In-Person Meeting → In-Person Meeting")
        print("  🎤 Gong Call → Gong Call")
        print("  💬 Slack → Slack")
        print("  📈 Feedback → Feedback")
        print("  📞 Inbound → Inbound")
        print("="*82)
    
    async def load_totango_data(self) -> bool:
        """Load and validate Totango data"""
        try:
            if not os.path.exists(self.config['totango_data_file']):
                logger.error(f"❌ Totango data file not found: {self.config['totango_data_file']}")
                return False
            
            async with aiofiles.open(self.config['totango_data_file'], 'r') as f:
                content = await f.read()
                self.totango_data = json.loads(content)
            
            self.stats.total_items = len(self.totango_data)
            logger.info(f"📊 Loaded {self.stats.total_items} Totango events for migration")
            
            # Analyze meeting types in the data
            self._analyze_meeting_types()
            
            # Sample data validation
            if self.totango_data:
                sample = self.totango_data[0]
                required_fields = ['id', 'timestamp', 'type', 'account']
                if all(field in sample for field in required_fields):
                    logger.info("✅ Totango data structure validation passed")
                    return True
                else:
                    logger.error("❌ Invalid Totango data structure")
                    return False
            else:
                logger.error("❌ No Totango events found in file")
                return False
                
        except Exception as e:
            logger.error(f"❌ Failed to load Totango data: {e}")
            return False
    
    def _analyze_meeting_types(self):
        """Analyze meeting types present in the data"""
        meeting_types = {}
        event_types = {}
        
        for event in self.totango_data:
            event_type = event.get('type', 'unknown')
            event_types[event_type] = event_types.get(event_type, 0) + 1
            
            properties = event.get('properties', {})
            for key in ['meeting_type', 'meeting_type_name', 'type', 'activity_type']:
                if key in properties and properties[key]:
                    meeting_type = str(properties[key]).strip()
                    meeting_types[meeting_type] = meeting_types.get(meeting_type, 0) + 1
                    break
        
        logger.info("📋 Data Analysis:")
        logger.info(f"  Event Types: {dict(sorted(event_types.items(), key=lambda x: x[1], reverse=True))}")
        if meeting_types:
            logger.info(f"  Meeting Types: {dict(sorted(meeting_types.items(), key=lambda x: x[1], reverse=True))}")
        else:
            logger.info("  No meeting types found in data")
    
    async def initialize_playwright(self) -> bool:
        """Initialize Playwright with authenticated context"""
        try:
            self.playwright = await async_playwright().start()
            self.browser = await self.playwright.chromium.launch(headless=False)
            self.context = await self.browser.new_context(
                user_agent="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36"
            )
            
            logger.info("🎭 Playwright initialized successfully")
            return True
            
        except Exception as e:
            logger.error(f"❌ Failed to initialize Playwright: {e}")
            return False
    
    async def authenticate_gainsight(self) -> bool:
        """Authenticate with Gainsight and setup API context"""
        try:
            page = await self.context.new_page()
            
            logger.info("🔐 Authenticating with Gainsight...")
            
            # Navigate to login
            await page.goto(self.config["login_url"])
            await page.wait_for_load_state("networkidle", timeout=15000)
            
            # Fill credentials
            await page.fill("input[name='username']", self.credentials["username"])
            await page.fill("input[name='password']", self.credentials["password"])
            
            # Submit login
            await page.click("button[type='submit']")
            
            # Wait for successful login
            await page.wait_for_function(
                "() => window.location.href.includes('/home') || window.location.href.includes('/dashboard')",
                timeout=30000
            )
            
            # Create authenticated API request context
            self.api_context = await self.context.request
            
            logger.info("✅ Gainsight authentication successful")
            logger.info("🔗 API request context established")
            
            await page.close()
            return True
            
        except Exception as e:
            logger.error(f"❌ Authentication failed: {e}")
            return False
    
    async def create_activity_via_api(self, gainsight_payload: Dict[str, Any], totango_id: str) -> MigrationResult:
        """Create activity using Gainsight API with enhanced error handling"""
        start_time = time.time()
        
        # Extract activity type and meeting type for result tracking
        activity_type = gainsight_payload.get("note", {}).get("type", "Unknown")
        meeting_type = gainsight_payload.get("note", {}).get("customFields", {}).get("Ant__Original_Meeting_Type__c", "")
        
        result = MigrationResult(
            success=False,
            totango_id=totango_id,
            method="API",
            timestamp=datetime.now().isoformat(),
            activity_type=activity_type,
            meeting_type=meeting_type
        )
        
        try:
            # Step 1: Create draft
            logger.debug(f"Creating draft for Totango ID: {totango_id}")
            
            draft_payload = {
                "type": "EMAIL",  # Always use EMAIL as the base type for draft creation
                "subject": gainsight_payload["note"]["subject"][:255],
                "companyId": gainsight_payload["contexts"][0]["id"]
            }
            
            draft_response = await self.api_context.post(
                self.config['drafts_api_url'],
                data=json.dumps(draft_payload),
                headers={
                    "Content-Type": "application/json",
                    "Accept": "application/json"
                },
                timeout=self.config['request_timeout']
            )
            
            if not draft_response.ok:
                result.error = f"Draft API failed: {draft_response.status} - {await draft_response.text()}"
                return result
            
            draft_result = await draft_response.json()
            draft_id = draft_result.get('data', {}).get('id') or draft_result.get('id')
            
            if not draft_id:
                result.error = f"No draft ID returned: {draft_result}"
                return result
            
            logger.debug(f"Draft created with ID: {draft_id}")
            
            # Step 2: Create activity with draft ID
            final_payload = gainsight_payload.copy()
            final_payload['id'] = draft_id
            
            activity_response = await self.api_context.post(
                self.config['activity_api_url'],
                data=json.dumps(final_payload),
                headers={
                    "Content-Type": "application/json",
                    "Accept": "application/json"
                },
                timeout=self.config['request_timeout']
            )
            
            if activity_response.ok:
                activity_result = await activity_response.json()
                activity_id = activity_result.get('data', {}).get('id') or activity_result.get('id')
                
                result.success = True
                result.gainsight_id = str(activity_id) if activity_id else draft_id
                logger.debug(f"✅ Activity created: {result.gainsight_id} (Type: {activity_type})")
            else:
                result.error = f"Activity API failed: {activity_response.status} - {await activity_response.text()}"
            
        except Exception as e:
            result.error = f"API exception: {str(e)}"
            logger.error(f"❌ API error for {totango_id}: {e}")
        
        finally:
            result.processing_time = time.time() - start_time
        
        return result
    
    async def process_batch(self, batch: List[Dict[str, Any]]) -> List[MigrationResult]:
        """Process a batch of Totango events"""
        batch_results = []
        
        for event in batch:
            totango_id = event.get('id', 'unknown')
            
            try:
                # Transform to Gainsight payload
                gainsight_payload = self.transformer.transform_to_gainsight(event)
                if not gainsight_payload:
                    result = MigrationResult(
                        success=False,
                        totango_id=totango_id,
                        method="TRANSFORM",
                        error="Failed to transform payload",
                        timestamp=datetime.now().isoformat()
                    )
                    batch_results.append(result)
                    continue
                
                # Attempt API creation with retries
                result = None
                for attempt in range(self.config['max_retries']):
                    result = await self.create_activity_via_api(gainsight_payload, totango_id)
                    result.retry_count = attempt
                    
                    if result.success:
                        break
                    
                    if attempt < self.config['max_retries'] - 1:
                        delay = (2 ** attempt) * self.config['rate_limit_delay']
                        logger.debug(f"Retrying in {delay}s (attempt {attempt + 1})")
                        await asyncio.sleep(delay)
                
                batch_results.append(result)
                
                # Rate limiting
                await asyncio.sleep(self.config['rate_limit_delay'])
                
            except Exception as e:
                result = MigrationResult(
                    success=False,
                    totango_id=totango_id,
                    method="BATCH",
                    error=f"Batch processing error: {str(e)}",
                    timestamp=datetime.now().isoformat()
                )
                batch_results.append(result)
                logger.error(f"❌ Batch processing error for {totango_id}: {e}")
        
        return batch_results
    
    async def migrate_all_events(self) -> bool:
        """Execute bulk migration of all Totango events"""
        if not self.totango_data:
            logger.error("❌ No Totango data to migrate")
            return False
        
        self.stats.start_time = datetime.now()
        logger.info(f"🚀 Starting bulk migration of {self.stats.total_items} events")
        
        # Create batches
        batches = []
        for i in range(0, len(self.totango_data), self.config['batch_size']):
            batches.append(self.totango_data[i:i + self.config['batch_size']])
        
        logger.info(f"📦 Created {len(batches)} batches of up to {self.config['batch_size']} events each")
        
        # Process batches
        for batch_num, batch in enumerate(batches, 1):
            logger.info(f"🔄 Processing batch {batch_num}/{len(batches)} ({len(batch)} events)")
            
            try:
                batch_results = await self.process_batch(batch)
                self.results.extend(batch_results)
                
                # Update statistics
                for result in batch_results:
                    self.stats.processed += 1
                    if result.success:
                        self.stats.successful += 1
                        self.stats.api_successful += 1
                        # Track activity type stats
                        if result.activity_type:
                            self.stats.activity_type_stats[result.activity_type] = self.stats.activity_type_stats.get(result.activity_type, 0) + 1
                    else:
                        self.stats.failed += 1
                        self.stats.api_failed += 1
                
                # Log progress
                success_rate = (self.stats.successful / self.stats.processed) * 100
                logger.info(f"📊 Batch {batch_num} complete: {len([r for r in batch_results if r.success])}/{len(batch_results)} successful")
                logger.info(f"📈 Overall progress: {self.stats.processed}/{self.stats.total_items} ({success_rate:.1f}% success rate)")
                
                # Save intermediate results
                if batch_num % 5 == 0:  # Save every 5 batches
                    await self.save_intermediate_results()
                
            except Exception as e:
                logger.error(f"❌ Error processing batch {batch_num}: {e}")
                # Mark all items in batch as failed
                for event in batch:
                    result = MigrationResult(
                        success=False,
                        totango_id=event.get('id', 'unknown'),
                        method="BATCH_ERROR",
                        error=f"Batch {batch_num} failed: {str(e)}",
                        timestamp=datetime.now().isoformat()
                    )
                    self.results.append(result)
                    self.stats.processed += 1
                    self.stats.failed += 1
                    self.stats.api_failed += 1
        
        self.stats.end_time = datetime.now()
        
        # Calculate average processing time
        processing_times = [r.processing_time for r in self.results if r.processing_time > 0]
        self.stats.average_processing_time = sum(processing_times) / len(processing_times) if processing_times else 0
        
        logger.info("✅ Bulk migration completed!")
        return True
    
    async def save_intermediate_results(self):
        """Save intermediate results during migration"""
        try:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            
            # Save current results
            results_data = [
                {
                    "success": r.success,
                    "totango_id": r.totango_id,
                    "gainsight_id": r.gainsight_id,
                    "method": r.method,
                    "error": r.error,
                    "timestamp": r.timestamp,
                    "processing_time": r.processing_time,
                    "retry_count": r.retry_count,
                    "activity_type": r.activity_type,
                    "meeting_type": r.meeting_type
                }
                for r in self.results
            ]
            
            intermediate_file = f"/Users/<USER>/Desktop/wild_weasel_gainsight_migration/intermediate_results_{timestamp}.json"
            async with aiofiles.open(intermediate_file, 'w') as f:
                await f.write(json.dumps(results_data, indent=2))
            
            logger.info(f"💾 Intermediate results saved: {intermediate_file}")
            
        except Exception as e:
            logger.warning(f"⚠️ Failed to save intermediate results: {e}")
    
    async def save_final_results(self):
        """Save comprehensive final results"""
        try:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            
            # Prepare results data
            results_data = [
                {
                    "success": r.success,
                    "totango_id": r.totango_id,
                    "gainsight_id": r.gainsight_id,
                    "method": r.method,
                    "error": r.error,
                    "timestamp": r.timestamp,
                    "processing_time": r.processing_time,
                    "retry_count": r.retry_count,
                    "activity_type": r.activity_type,
                    "meeting_type": r.meeting_type
                }
                for r in self.results
            ]
            
            # Save all results
            results_file = self.config["results_file"].replace('.json', f'_{timestamp}.json')
            async with aiofiles.open(results_file, 'w') as f:
                await f.write(json.dumps(results_data, indent=2))
            
            # Save failed results separately
            failed_results = [r for r in results_data if not r["success"]]
            if failed_results:
                failed_file = self.config["failed_file"].replace('.json', f'_{timestamp}.json')
                async with aiofiles.open(failed_file, 'w') as f:
                    await f.write(json.dumps(failed_results, indent=2))
            
            # Save statistics
            stats_data = {
                "total_items": self.stats.total_items,
                "processed": self.stats.processed,
                "successful": self.stats.successful,
                "failed": self.stats.failed,
                "api_successful": self.stats.api_successful,
                "api_failed": self.stats.api_failed,
                "ui_successful": self.stats.ui_successful,
                "ui_failed": self.stats.ui_failed,
                "start_time": self.stats.start_time.isoformat() if self.stats.start_time else None,
                "end_time": self.stats.end_time.isoformat() if self.stats.end_time else None,
                "duration": str(self.stats.end_time - self.stats.start_time) if self.stats.start_time and self.stats.end_time else None,
                "average_processing_time": self.stats.average_processing_time,
                "success_rate": (self.stats.successful / self.stats.processed * 100) if self.stats.processed > 0 else 0,
                "activity_type_stats": self.stats.activity_type_stats
            }
            
            stats_file = self.config["stats_file"].replace('.json', f'_{timestamp}.json')
            async with aiofiles.open(stats_file, 'w') as f:
                await f.write(json.dumps(stats_data, indent=2))
            
            logger.info(f"💾 Final results saved:")
            logger.info(f"  📊 All results: {results_file}")
            if failed_results:
                logger.info(f"  ❌ Failed results: {failed_file}")
            logger.info(f"  📈 Statistics: {stats_file}")
            
        except Exception as e:
            logger.error(f"❌ Failed to save final results: {e}")
    
    def generate_final_report(self):
        """Generate comprehensive final report"""
        if not self.stats.start_time or not self.stats.end_time:
            duration = "Unknown"
        else:
            duration = str(self.stats.end_time - self.stats.start_time)
        
        success_rate = (self.stats.successful / self.stats.processed * 100) if self.stats.processed > 0 else 0
        
        # Generate activity type breakdown
        activity_breakdown = ""
        if self.stats.activity_type_stats:
            for activity_type, count in sorted(self.stats.activity_type_stats.items(), key=lambda x: x[1], reverse=True):
                percentage = (count / self.stats.successful * 100) if self.stats.successful > 0 else 0
                activity_breakdown += f"     • {activity_type}: {count} ({percentage:.1f}%)\n"
        
        report = f"""
🐺 WILD WEASEL v5.1 - ENHANCED MEETING TYPE MAPPING REPORT
{'='*80}
🚀 MEETING TYPE MAPPING SUCCESS:
  ✅ INTELLIGENT MAPPING:
     → Totango meeting types properly mapped to Gainsight activity types
     → Support for both default and custom Gainsight types
     → Automatic fallback for unknown meeting types
  
  ✅ ENHANCED API INTEGRATION:
     → Authenticated API request context
     → Session-based bulk operations
     → Advanced retry mechanisms
  
  ✅ BULK PROCESSING PIPELINE:
     → Intelligent Totango → Gainsight transformation
     → Parallel batch processing with rate limiting
     → Real-time progress monitoring

📊 MIGRATION STATISTICS:
  Total Totango Events: {self.stats.total_items:,}
  ✅ Successfully Migrated: {self.stats.successful:,}
  ❌ Failed Migrations: {self.stats.failed:,}
  📈 Success Rate: {success_rate:.1f}%
  ⏱️ Total Duration: {duration}
  ⚡ Average Processing Time: {self.stats.average_processing_time:.2f}s per event

🔧 METHOD BREAKDOWN:
  🔗 API Approach:
     ✅ Successful: {self.stats.api_successful:,}
     ❌ Failed: {self.stats.api_failed:,}
     📈 API Success Rate: {(self.stats.api_successful / (self.stats.api_successful + self.stats.api_failed) * 100) if (self.stats.api_successful + self.stats.api_failed) > 0 else 0:.1f}%

📋 ACTIVITY TYPE BREAKDOWN:
{activity_breakdown}

📋 DATA TRANSFORMATION:
  Source: Totango Events with Meeting Types
  Target: Gainsight Timeline Activities
  Company: ICICI Bank (0015p00005R7ysqAAB → 1P02IPMAEL4M3CQGY4K5FHU22D2HHT11KCKU)
  Activity Types: Intelligently mapped based on meeting_type fields

🎯 PROCESSING EFFICIENCY:
  Batch Size: {self.config['batch_size']} events per batch
  Total Batches: {(self.stats.total_items + self.config['batch_size'] - 1) // self.config['batch_size']}
  Rate Limiting: {self.config['rate_limit_delay']}s between requests
  Retry Strategy: Up to {self.config['max_retries']} attempts per event

🐺 Wild Weasel v5.1 Enhanced Mission Status: {'✅ COMPLETED SUCCESSFULLY' if self.stats.failed == 0 else f'⚠️ PARTIALLY COMPLETED ({self.stats.successful:,}/{self.stats.total_items:,} successful)'}
{'='*80}
"""
        
        print(report)
        
        # Save report to file
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        report_file = f"/Users/<USER>/Desktop/wild_weasel_gainsight_migration/migration_report_v5_enhanced_{timestamp}.txt"
        try:
            with open(report_file, 'w') as f:
                f.write(report)
            logger.info(f"📄 Final report saved to: {report_file}")
        except Exception as e:
            logger.warning(f"⚠️ Failed to save report: {e}")
    
    async def cleanup(self):
        """Cleanup Playwright resources"""
        try:
            if self.context:
                await self.context.close()
            if self.browser:
                await self.browser.close()
            if self.playwright:
                await self.playwright.stop()
            logger.info("🧹 Playwright resources cleaned up")
        except Exception as e:
            logger.warning(f"⚠️ Cleanup warning: {e}")
    
    async def execute_enhanced_mission(self) -> bool:
        """Execute the complete enhanced migration mission"""
        try:
            self.mission_brief()
            
            # Step 1: Load data
            if not await self.load_totango_data():
                logger.error("❌ Mission aborted: Failed to load Totango data")
                return False
            
            # Step 2: Initialize Playwright
            if not await self.initialize_playwright():
                logger.error("❌ Mission aborted: Failed to initialize Playwright")
                return False
            
            # Step 3: Authenticate
            if not await self.authenticate_gainsight():
                logger.error("❌ Mission aborted: Failed to authenticate with Gainsight")
                return False
            
            # Step 4: Execute migration
            if not await self.migrate_all_events():
                logger.error("❌ Migration encountered errors")
            
            # Step 5: Save results and generate report
            await self.save_final_results()
            self.generate_final_report()
            
            logger.info("🐺 Wild Weasel v5.1 Enhanced mission completed!")
            return True
            
        except Exception as e:
            logger.error(f"❌ Mission execution failed: {e}")
            return False
        finally:
            await self.cleanup()

async def main():
    """Main execution function"""
    try:
        agent = WildWeaselAgentV5Enhanced()
        await agent.execute_enhanced_mission()
        
    except KeyboardInterrupt:
        print("\n🐺 Wild Weasel v5.1 Enhanced mission interrupted by user")
        sys.exit(0)
    except Exception as e:
        logger.error(f"❌ Wild Weasel v5.1 Enhanced mission failed: {e}")
        sys.exit(1)

if __name__ == "__main__":
    asyncio.run(main())

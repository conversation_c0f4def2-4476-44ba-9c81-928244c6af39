#!/usr/bin/env python3
"""
🐺 Wild Weasel - FIXED Totango to Gainsight Email Migration Agent v2.0
Mission: Comprehensive migration with proper API workflow and UI automation

FIXES IMPLEMENTED:
1. Correct Gainsight API workflow with drafts call
2. Proper request payload format
3. Enhanced browser automation with better selectors
4. Multiple fallback strategies
5. Cookie-based authentication for API calls
6. Comprehensive error handling and reporting
"""

import json
import os
import sys
import time
import requests
import logging
from datetime import datetime
from typing import Dict, List, Any, Optional
from playwright.sync_api import sync_playwright, TimeoutError as PlaywrightTimeoutError
import urllib.parse

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("wild_weasel_migration_v2.log"),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger("WildWeasel-v2")

class WildWeaselAgentV2:
    """Enhanced Wild Weasel Agent with proper API workflow and UI automation"""
    
    def __init__(self):
        self.config = {
            "gainsight_url": "https://demo-emea1.gainsightcloud.com",
            "login_url": "https://demo-emea1.gainsightcloud.com/v1/ui/home",
            "target_c360_url": "https://demo-emea1.gainsightcloud.com/v1/ui/customersuccess360?cid=1P02IPMAEL4M3CQGY4K5FHU22D2HHT11KCKU#/8fd79692-8e98-4543-b2ec-bc0dba3776ca",
            "api_base_url": "https://demo-emea1.gainsightcloud.com/v1/ant",
            "activity_api_url": "https://demo-emea1.gainsightcloud.com/v1/ant/v2/activity",
            "drafts_api_url": "https://demo-emea1.gainsightcloud.com/v1/ant/v2/activity/drafts",
            "email_activities_file": "/Users/<USER>/Desktop/wild_weasel_gainsight_migration/extracted_email_activities_fixed.json",
            "success_log": "/Users/<USER>/Desktop/wild_weasel_gainsight_migration/migration_success_v2.json",
            "failed_log": "/Users/<USER>/Desktop/wild_weasel_gainsight_migration/migration_failed_v2.json"
        }
        
        self.email_activities = []
        self.migration_results = {
            "successful": [],
            "failed": [],
            "total_processed": 0,
            "start_time": None,
            "end_time": None,
            "methods_used": [],
            "api_stats": {"successful": 0, "failed": 0},
            "ui_stats": {"successful": 0, "failed": 0}
        }
        
        # Authentication data
        self.credentials = {
            "username": os.getenv("GAINSIGHT_USERNAME"),
            "password": os.getenv("GAINSIGHT_PASSWORD"),
            "api_key": os.getenv("GAINSIGHT_API_KEY")
        }
        
        self.session_cookies = None
        self.browser_context = None
    
    def mission_brief(self):
        """Display the enhanced Wild Weasel mission brief"""
        print("🐺" + "="*70)
        print("  WILD WEASEL v2.0 - ENHANCED MIGRATION AGENT")
        print("="*72)
        print("📋 MISSION BRIEF:")
        print("✅ FIXES IMPLEMENTED:")
        print("  → Fixed email identification using proper ID mapping")
        print("  → Correct Gainsight API workflow with drafts call")
        print("  → Enhanced browser automation with fallback strategies")
        print("  → Cookie-based API authentication")
        print("  → Comprehensive error handling and retry logic")
        print("="*72)
        print(f"📁 Source Data: {self.config['email_activities_file']}")
        print(f"🎯 Target URL: {self.config['target_c360_url']}")
        print(f"🔗 Activity API: {self.config['activity_api_url']}")
        print(f"📄 Drafts API: {self.config['drafts_api_url']}")
        print("="*72)
    
    def load_email_activities(self):
        """Load the FIXED extracted email activities"""
        try:
            # First try to load the fixed file
            if os.path.exists(self.config['email_activities_file']):
                with open(self.config['email_activities_file'], 'r') as f:
                    self.email_activities = json.load(f)
            else:
                # Fallback to original file if fixed doesn't exist
                original_file = self.config['email_activities_file'].replace('_fixed', '')
                if os.path.exists(original_file):
                    with open(original_file, 'r') as f:
                        self.email_activities = json.load(f)
                    logger.warning(f"⚠️ Using original file: {original_file}")
                else:
                    logger.error(f"❌ No email activities file found")
                    return False
            
            logger.info(f"📊 Loaded {len(self.email_activities)} email activities for migration")
            
            # Show sample activities
            if self.email_activities:
                logger.info("📋 Sample activities to migrate:")
                for i, activity in enumerate(self.email_activities[:3]):
                    if 'records' in activity and activity['records']:
                        record = activity['records'][0]
                        subject = record.get('Subject', 'No Subject')
                        date = record.get('ActivityDate', 'No Date')
                        logger.info(f"  {i+1}. {subject[:60]} ({date[:10]})")
                    else:
                        # Fallback for old format
                        subject = activity.get('subject', 'No Subject')
                        date = activity.get('activityDate', 'No Date')
                        logger.info(f"  {i+1}. {subject[:60]} ({date[:10]})")
            
            return True
            
        except Exception as e:
            logger.error(f"❌ Failed to load email activities: {e}")
            return False
    
    def prompt_for_credentials(self):
        """Prompt for credentials if not available"""
        if not self.credentials["username"]:
            self.credentials["username"] = input("Enter Gainsight username: ")
        
        if not self.credentials["password"]:
            import getpass
            self.credentials["password"] = getpass.getpass("Enter Gainsight password: ")
        
        # API key is optional - we can extract cookies from browser session
        if not self.credentials["api_key"]:
            logger.info("ℹ️ No API key provided - will extract session cookies for API calls")
    
    def login_to_gainsight(self, page):
        """Enhanced login with cookie extraction for API calls"""
        try:
            logger.info("🔐 Logging into Gainsight...")
            
            # Navigate to login page
            page.goto(self.config["login_url"])
            page.wait_for_load_state("networkidle", timeout=15000)
            
            # Handle subdomain entry if needed
            try:
                subdomain_selectors = [
                    "input[name='subdomain']",
                    "input.gs-domain-input",
                    "input[placeholder*='subdomain']",
                    "input[placeholder*='domain']"
                ]
                
                subdomain_filled = False
                for selector in subdomain_selectors:
                    try:
                        subdomain_input = page.locator(selector)
                        if subdomain_input.count() > 0 and subdomain_input.is_visible():
                            logger.info(f"🌐 Entering subdomain with selector: {selector}")
                            subdomain_input.fill("demo-emea1")
                            
                            # Try to find and click Go button
                            go_selectors = [
                                "button:has-text('Go')",
                                "button[type='submit']",
                                "input[type='submit']",
                                ".submit-btn"
                            ]
                            
                            for go_selector in go_selectors:
                                try:
                                    go_btn = page.locator(go_selector)
                                    if go_btn.count() > 0 and go_btn.is_visible():
                                        go_btn.click()
                                        page.wait_for_load_state("networkidle", timeout=10000)
                                        subdomain_filled = True
                                        break
                                except:
                                    continue
                            
                            if subdomain_filled:
                                break
                    except:
                        continue
                        
            except Exception as e:
                logger.debug(f"Subdomain handling: {e}")
            
            # Enhanced credential entry with multiple selectors
            logger.info("🔑 Entering credentials...")
            
            # Username selectors
            username_selectors = [
                "input[name='username']",
                "input[name='email']",
                "input[type='email']",
                "input[placeholder*='username']",
                "input[placeholder*='email']",
                "input[placeholder*='User name']",
                ".username-input",
                "#username",
                "#email"
            ]
            
            username_filled = False
            for selector in username_selectors:
                try:
                    username_field = page.locator(selector)
                    if username_field.count() > 0 and username_field.is_visible():
                        username_field.fill(self.credentials["username"])
                        logger.info(f"✅ Username filled with selector: {selector}")
                        username_filled = True
                        break
                except:
                    continue
            
            if not username_filled:
                # Try role-based approach
                try:
                    page.get_by_role('textbox', name='User name').fill(self.credentials["username"])
                    username_filled = True
                    logger.info("✅ Username filled with role-based selector")
                except:
                    logger.warning("⚠️ Could not fill username")
            
            # Password selectors
            password_selectors = [
                "input[name='password']",
                "input[type='password']",
                "input[placeholder*='password']",
                "input[placeholder*='Password']",
                ".password-input",
                "#password"
            ]
            
            password_filled = False
            for selector in password_selectors:
                try:
                    password_field = page.locator(selector)
                    if password_field.count() > 0 and password_field.is_visible():
                        password_field.fill(self.credentials["password"])
                        logger.info(f"✅ Password filled with selector: {selector}")
                        password_filled = True
                        break
                except:
                    continue
            
            if not password_filled:
                # Try role-based approach
                try:
                    page.get_by_role('textbox', name='Password').fill(self.credentials["password"])
                    password_filled = True
                    logger.info("✅ Password filled with role-based selector")
                except:
                    logger.warning("⚠️ Could not fill password")
            
            # Enhanced login button clicking
            login_selectors = [
                "button:has-text('Log In')",
                "button:has-text('Login')",
                "button:has-text('Sign In')",
                "button[type='submit']",
                "input[type='submit']",
                ".login-btn",
                ".submit-btn",
                "#login-button"
            ]
            
            login_clicked = False
            for selector in login_selectors:
                try:
                    login_btn = page.locator(selector)
                    if login_btn.count() > 0 and login_btn.is_visible():
                        login_btn.click()
                        logger.info(f"🔘 Login clicked with selector: {selector}")
                        login_clicked = True
                        break
                except:
                    continue
            
            if not login_clicked:
                # Try role-based approach
                try:
                    page.get_by_role('button', name='Log In').click()
                    login_clicked = True
                    logger.info("🔘 Login clicked with role-based selector")
                except:
                    logger.error("❌ Could not click login button")
                    return False
            
            # Wait for successful login with multiple possible outcomes
            try:
                # Wait for URL change or dashboard elements
                page.wait_for_function(
                    "() => window.location.href.includes('/home') || window.location.href.includes('/dashboard') || document.querySelector('.dashboard') || document.querySelector('.nav-menu')",
                    timeout=30000
                )
                
                # Extract cookies for API calls
                self.extract_session_cookies(page)
                
                logger.info("✅ Successfully logged into Gainsight!")
                return True
                
            except Exception as e:
                logger.error(f"❌ Login timeout or failed: {e}")
                # Take screenshot for debugging
                page.screenshot(path="/Users/<USER>/Desktop/wild_weasel_gainsight_migration/login_error_v2.png")
                return False
                
        except Exception as e:
            logger.error(f"❌ Login process failed: {e}")
            return False
    
    def extract_session_cookies(self, page):
        """Extract session cookies for API authentication"""
        try:
            cookies = page.context.cookies()
            cookie_dict = {}
            cookie_string = ""
            
            for cookie in cookies:
                cookie_dict[cookie['name']] = cookie['value']
                cookie_string += f"{cookie['name']}={cookie['value']}; "
            
            self.session_cookies = {
                'dict': cookie_dict,
                'string': cookie_string.rstrip('; ')
            }
            
            logger.info(f"🍪 Extracted {len(cookie_dict)} session cookies for API calls")
            
            # Log some key cookies (without values for security)
            key_cookies = ['JSESSIONID', 'GS-SESSION', 'gs_session', 'session', 'auth']
            found_key_cookies = [name for name in cookie_dict.keys() if any(key.lower() in name.lower() for key in key_cookies)]
            if found_key_cookies:
                logger.info(f"🔑 Key authentication cookies found: {found_key_cookies}")
            
        except Exception as e:
            logger.warning(f"⚠️ Could not extract cookies: {e}")
            self.session_cookies = None
    
    def navigate_to_c360_timeline(self, page):
        """Enhanced navigation to C360 Timeline with better element detection"""
        try:
            logger.info("🎯 Navigating to C360 Timeline...")
            
            # Navigate to target C360 URL
            page.goto(self.config["target_c360_url"])
            page.wait_for_load_state("networkidle", timeout=30000)
            
            # Wait for page to stabilize
            time.sleep(3)
            
            # Enhanced Timeline tab detection
            timeline_selectors = [
                'a:has-text("Timeline")',
                'li:has-text("Timeline")',
                'div[role="tab"]:has-text("Timeline")',
                '.nav-item:has-text("Timeline")',
                '.tab:has-text("Timeline")',
                '.menu-item:has-text("Timeline")',
                'button:has-text("Timeline")',
                '[data-tab="timeline"]',
                '[data-name="timeline"]',
                '.timeline-tab'
            ]
            
            timeline_found = False
            for selector in timeline_selectors:
                try:
                    timeline_tab = page.locator(selector).first
                    if timeline_tab.count() > 0 and timeline_tab.is_visible():
                        logger.info(f"📌 Found Timeline tab with selector: {selector}")
                        
                        # Scroll element into view if needed
                        timeline_tab.scroll_into_view_if_needed()
                        time.sleep(1)
                        
                        # Click with retry
                        for attempt in range(3):
                            try:
                                timeline_tab.click()
                                time.sleep(2)
                                timeline_found = True
                                break
                            except Exception as e:
                                logger.debug(f"Click attempt {attempt + 1} failed: {e}")
                                time.sleep(1)
                        
                        if timeline_found:
                            break
                except Exception as e:
                    logger.debug(f"Selector {selector} failed: {e}")
                    continue
            
            # Try JavaScript approach if normal clicking failed
            if not timeline_found:
                logger.info("🔄 Trying JavaScript approach to find Timeline...")
                timeline_clicked = page.evaluate("""() => {
                    // Try multiple strategies to find Timeline
                    const strategies = [
                        () => {
                            const elements = document.querySelectorAll('*');
                            for (const el of elements) {
                                if (el.innerText && el.innerText.trim().toLowerCase() === 'timeline' && el.offsetParent !== null) {
                                    el.click();
                                    return true;
                                }
                            }
                            return false;
                        },
                        () => {
                            const links = document.querySelectorAll('a, button, div[role="tab"], li');
                            for (const link of links) {
                                if (link.textContent && link.textContent.toLowerCase().includes('timeline') && link.offsetParent !== null) {
                                    link.click();
                                    return true;
                                }
                            }
                            return false;
                        },
                        () => {
                            // Look for data attributes
                            const candidates = document.querySelectorAll('[data-tab], [data-name], [data-section]');
                            for (const el of candidates) {
                                const attrs = Array.from(el.attributes);
                                if (attrs.some(attr => attr.value.toLowerCase().includes('timeline'))) {
                                    el.click();
                                    return true;
                                }
                            }
                            return false;
                        }
                    ];
                    
                    for (const strategy of strategies) {
                        if (strategy()) return true;
                    }
                    return false;
                }""")
                
                if timeline_clicked:
                    logger.info("📌 Successfully clicked Timeline tab with JavaScript")
                    time.sleep(3)
                    timeline_found = True
            
            if timeline_found:
                # Verify we're on the timeline by looking for timeline-specific elements
                timeline_indicators = [
                    '.timeline-container',
                    '.activity-list',
                    '.timeline-activity',
                    'button:has-text("Add Activity")',
                    'button:has-text("Create Activity")',
                    '.add-activity-btn'
                ]
                
                for indicator in timeline_indicators:
                    try:
                        if page.locator(indicator).count() > 0:
                            logger.info(f"✅ Timeline confirmed with indicator: {indicator}")
                            page.screenshot(path="/Users/<USER>/Desktop/wild_weasel_gainsight_migration/timeline_page_v2.png")
                            return True
                    except:
                        continue
                
                # If no specific indicators found, but timeline was clicked, proceed anyway
                logger.info("✅ Timeline navigation completed (no specific indicators found)")
                page.screenshot(path="/Users/<USER>/Desktop/wild_weasel_gainsight_migration/timeline_page_v2.png")
                return True
            else:
                logger.error("❌ Failed to find Timeline tab with any method")
                page.screenshot(path="/Users/<USER>/Desktop/wild_weasel_gainsight_migration/timeline_not_found.png")
                return False
                
        except Exception as e:
            logger.error(f"❌ Navigation failed: {e}")
            page.screenshot(path="/Users/<USER>/Desktop/wild_weasel_gainsight_migration/navigation_error_v2.png")
            return False
    
    def create_activity_via_api_v2(self, activity):
        """FIXED: Create activity using proper Gainsight API workflow with drafts call"""
        try:
            if not self.session_cookies and not self.credentials.get("api_key"):
                logger.warning("⚠️ No authentication available for API calls")
                return False
            
            # Extract activity data
            if 'records' in activity and activity['records']:
                record = activity['records'][0]
                subject = record.get('Subject', 'Email Activity')
                notes = record.get('Notes', '')
                activity_date = record.get('ActivityDate', datetime.now().isoformat())
                author = record.get('Author', '<EMAIL>')
                external_id = record.get('ExternalId', '0015p00005R7ysqAAB')
            else:
                # Fallback for old format
                subject = activity.get('subject', 'Email Activity')
                notes = activity.get('content', '')
                activity_date = activity.get('activityDate', datetime.now().isoformat())
                author = activity.get('author', {}).get('email', '<EMAIL>')
                external_id = activity.get('company', {}).get('externalId', '0015p00005R7ysqAAB')
            
            logger.info(f"🔗 Creating activity via API: {subject[:50]}...")
            
            # Step 1: Create draft when activity type is Email (as per user's explanation)
            draft_payload = {
                "activityType": "Email",
                "companyId": external_id
            }
            
            headers = self.build_api_headers()
            
            # Call drafts API first
            logger.info("📄 Calling drafts API to generate unique ID...")
            draft_response = requests.post(
                self.config['drafts_api_url'], 
                json=draft_payload, 
                headers=headers,
                timeout=30
            )
            
            if draft_response.status_code not in [200, 201]:
                logger.error(f"❌ Drafts API failed: {draft_response.status_code} - {draft_response.text}")
                return False
            
            draft_result = draft_response.json()
            unique_id = draft_result.get('id') or draft_result.get('draftId') or draft_result.get('activityId')
            
            if not unique_id:
                logger.error(f"❌ No unique ID returned from drafts API: {draft_result}")
                return False
            
            logger.info(f"✅ Draft created with unique ID: {unique_id}")
            
            # Step 2: Create the actual activity with the unique ID in syncedToSFDC field
            activity_payload = {
                "syncedToSFDC": False,
                "id": unique_id,
                "tasks": [],
                "records": [
                    {
                        "ContextName": "Company",
                        "TypeName": "Email",
                        "ExternalId": external_id,
                        "Subject": subject[:255],
                        "Notes": notes,
                        "ActivityDate": activity_date,
                        "Author": author,
                        "companyExternalId": external_id
                    }
                ],
                "lookups": {
                    "AuthorId": {
                        "fields": {
                            "Author": "Email"
                        },
                        "lookupField": "Gsid",
                        "objectName": "GsUser",
                        "multiMatchOption": "FIRSTMATCH",
                        "onNoMatch": "ERROR"
                    },
                    "GsCompanyId": {
                        "fields": {
                            "companyExternalId": "ExternalId"
                        },
                        "lookupField": "Gsid",
                        "objectName": "Company",
                        "multiMatchOption": "FIRSTMATCH",
                        "onNoMatch": "ERROR"
                    }
                }
            }
            
            # Step 3: Create the activity
            logger.info("📧 Creating final activity with unique ID...")
            activity_response = requests.post(
                self.config['activity_api_url'], 
                json=activity_payload, 
                headers=headers,
                timeout=30
            )
            
            if activity_response.status_code in [200, 201]:
                result = activity_response.json()
                logger.info(f"✅ Activity created successfully via API")
                logger.debug(f"API Response: {result}")
                return True
            else:
                logger.error(f"❌ Activity API failed: {activity_response.status_code} - {activity_response.text}")
                return False
                
        except Exception as e:
            logger.error(f"❌ API creation failed: {e}")
            return False
    
    def build_api_headers(self) -> Dict[str, str]:
        """Build API headers with available authentication"""
        headers = {
            "Content-Type": "application/json",
            "User-Agent": "Wild-Weasel-Agent/2.0"
        }
        
        # Priority 1: API Key if available
        if self.credentials.get("api_key"):
            headers["accesskey"] = self.credentials["api_key"]
            logger.debug("🔑 Using API key for authentication")
        
        # Priority 2: Session cookies
        elif self.session_cookies and self.session_cookies.get('string'):
            headers["Cookie"] = self.session_cookies['string']
            logger.debug("🍪 Using session cookies for authentication")
            
            # Add common session headers
            if self.session_cookies.get('dict'):
                cookies = self.session_cookies['dict']
                
                # Look for CSRF tokens
                csrf_keys = ['XSRF-TOKEN', 'csrf-token', 'X-CSRF-TOKEN']
                for key in csrf_keys:
                    if key in cookies:
                        headers["X-CSRF-TOKEN"] = cookies[key]
                        break
        
        else:
            logger.warning("⚠️ No authentication method available for API calls")
        
        return headers
    
    def create_activity_via_ui_enhanced(self, page, activity):
        """Enhanced UI activity creation with better element detection"""
        try:
            # Extract activity data
            if 'records' in activity and activity['records']:
                record = activity['records'][0]
                subject = record.get('Subject', 'Email Activity')
                notes = record.get('Notes', '')
                activity_date = record.get('ActivityDate', datetime.now().isoformat())
            else:
                # Fallback for old format
                subject = activity.get('subject', 'Email Activity')
                notes = activity.get('content', '')
                activity_date = activity.get('activityDate', datetime.now().isoformat())
            
            logger.info(f"➕ Creating activity via UI: {subject[:50]}...")
            
            # Enhanced "Create Activity" button detection
            create_selectors = [
                'button:has-text("Create Activity")',
                'button:has-text("Add Activity")',
                'button:has-text("New Activity")',
                'button:has-text("+ Activity")',
                '.create-activity-btn',
                '.add-activity-btn',
                '.new-activity-btn',
                'button[title*="Create"]',
                'button[title*="Add"]',
                'button[aria-label*="Create"]',
                'button[aria-label*="Add"]',
                '.timeline-add-btn',
                '.activity-create-btn'
            ]
            
            create_button_found = False
            for selector in create_selectors:
                try:
                    create_btn = page.locator(selector).first
                    if create_btn.count() > 0 and create_btn.is_visible():
                        logger.info(f"🔘 Found create button: {selector}")
                        
                        # Scroll into view and click
                        create_btn.scroll_into_view_if_needed()
                        time.sleep(1)
                        create_btn.click()
                        time.sleep(3)  # Wait for form to appear
                        
                        create_button_found = True
                        break
                        
                except Exception as e:
                    logger.debug(f"Create selector {selector} failed: {e}")
                    continue
            
            if not create_button_found:
                # Try JavaScript approach
                logger.info("🔄 Trying JavaScript to find create button...")
                js_result = page.evaluate("""() => {
                    const buttons = document.querySelectorAll('button, a, div[role="button"]');
                    for (const btn of buttons) {
                        const text = btn.innerText || btn.textContent || '';
                        if (text.toLowerCase().includes('create') || text.toLowerCase().includes('add activity') || text.includes('+')) {
                            if (btn.offsetParent !== null) {
                                btn.click();
                                return true;
                            }
                        }
                    }
                    return false;
                }""")
                
                if js_result:
                    logger.info("✅ Create button clicked via JavaScript")
                    time.sleep(3)
                    create_button_found = True
                    
            if not create_button_found:
                logger.error("❌ Could not find create activity button")
                return False
            
            # Select Email activity type
            logger.info("📧 Selecting Email activity type...")
            email_type_selectors = [
                'option:has-text("Email")',
                'li:has-text("Email")',
                'div:has-text("Email")',
                '.activity-type-email',
                '[data-value="Email"]',
                '[value="Email"]'
            ]
            
            email_selected = False
            for selector in email_type_selectors:
                try:
                    email_option = page.locator(selector).first
                    if email_option.count() > 0 and email_option.is_visible():
                        email_option.click()
                        time.sleep(1)
                        logger.info(f"✅ Email type selected: {selector}")
                        email_selected = True
                        break
                except:
                    continue
                    
            # Fill activity form
            success = self.fill_activity_form_enhanced(page, subject, notes, activity_date)
            
            if success:
                logger.info("✅ Activity created successfully via UI")
                return True
            else:
                logger.error("❌ Failed to fill activity form")
                return False
                
        except Exception as e:
            logger.error(f"❌ UI activity creation failed: {e}")
            return False
    
    def fill_activity_form_enhanced(self, page, subject: str, notes: str, activity_date: str):
        """Enhanced form filling with better field detection"""
        try:
            # Fill subject with multiple selectors
            subject_selectors = [
                'input[name="subject"]',
                'input[placeholder*="Subject"]',
                'input[placeholder*="subject"]',
                '.subject-input',
                '#subject',
                'input[aria-label*="Subject"]',
                'input[title*="Subject"]'
            ]
            
            subject_filled = False
            for selector in subject_selectors:
                try:
                    subject_field = page.locator(selector).first
                    if subject_field.count() > 0 and subject_field.is_visible():
                        subject_field.clear()
                        subject_field.fill(subject)
                        logger.info(f"✅ Subject filled: {selector}")
                        subject_filled = True
                        break
                except:
                    continue
            
            # Fill content/notes
            content_selectors = [
                'textarea[name="content"]',
                'textarea[name="notes"]',
                'textarea[name="description"]',
                'textarea[placeholder*="Notes"]',
                'textarea[placeholder*="Content"]',
                '.content-textarea',
                '.notes-textarea',
                '#content',
                '#notes',
                'textarea[aria-label*="Notes"]',
                'textarea[aria-label*="Content"]'
            ]
            
            content_filled = False
            for selector in content_selectors:
                try:
                    content_field = page.locator(selector).first
                    if content_field.count() > 0 and content_field.is_visible():
                        content_field.clear()
                        content_field.fill(notes)
                        logger.info(f"✅ Content filled: {selector}")
                        content_filled = True
                        break
                except:
                    continue
            
            # Fill date
            date_selectors = [
                'input[name="date"]',
                'input[name="activityDate"]',
                'input[type="date"]',
                'input[type="datetime-local"]',
                '.date-picker',
                '#date',
                '#activityDate',
                'input[aria-label*="Date"]'
            ]
            
            # Convert ISO date to required format
            try:
                date_str = activity_date.split('T')[0]  # Get YYYY-MM-DD part
            except:
                date_str = datetime.now().strftime('%Y-%m-%d')
            
            date_filled = False
            for selector in date_selectors:
                try:
                    date_field = page.locator(selector).first
                    if date_field.count() > 0 and date_field.is_visible():
                        date_field.clear()
                        date_field.fill(date_str)
                        logger.info(f"✅ Date filled: {selector}")
                        date_filled = True
                        break
                except:
                    continue
            
            # Save the activity
            save_selectors = [
                'button:has-text("Save")',
                'button:has-text("Create")',
                'button:has-text("Log Activity")',
                'button:has-text("Submit")',
                '.save-btn',
                '.create-btn',
                '.submit-btn',
                'button[type="submit"]',
                'input[type="submit"]'
            ]
            
            saved = False
            for selector in save_selectors:
                try:
                    save_btn = page.locator(selector).first
                    if save_btn.count() > 0 and save_btn.is_visible():
                        save_btn.click()
                        time.sleep(3)  # Wait for save to complete
                        logger.info(f"✅ Activity saved: {selector}")
                        saved = True
                        break
                except:
                    continue
            
            if not saved:
                # Try JavaScript save
                js_saved = page.evaluate("""() => {
                    const buttons = document.querySelectorAll('button, input[type="submit"]');
                    for (const btn of buttons) {
                        const text = btn.innerText || btn.value || '';
                        if (text.toLowerCase().includes('save') || text.toLowerCase().includes('create') || text.toLowerCase().includes('submit')) {
                            btn.click();
                            return true;
                        }
                    }
                    return false;
                }""")
                
                if js_saved:
                    logger.info("✅ Activity saved via JavaScript")
                    time.sleep(3)
                    saved = True
            
            return saved and (subject_filled or content_filled)
            
        except Exception as e:
            logger.error(f"❌ Form filling failed: {e}")
            return False
    
    def migrate_activities_hybrid_approach(self, page):
        """Hybrid approach: Try API first, fallback to UI"""
        logger.info("🔄 Starting hybrid migration approach...")
        
        successful_count = 0
        failed_count = 0
        
        for i, activity in enumerate(self.email_activities):
            logger.info(f"📝 Processing activity {i+1}/{len(self.email_activities)}")
            
            # Extract subject for logging
            if 'records' in activity and activity['records']:
                subject = activity['records'][0].get('Subject', 'Unknown Subject')
            else:
                subject = activity.get('subject', 'Unknown Subject')
            
            success = False
            method_used = None
            
            # Try API first
            try:
                if self.create_activity_via_api_v2(activity):
                    success = True
                    method_used = "API"
                    self.migration_results["api_stats"]["successful"] += 1
                    logger.info(f"✅ API Success: {subject[:50]}")
                else:
                    logger.warning(f"⚠️ API failed for: {subject[:50]}, trying UI...")
                    self.migration_results["api_stats"]["failed"] += 1
            except Exception as e:
                logger.warning(f"⚠️ API exception for {subject[:50]}: {e}, trying UI...")
                self.migration_results["api_stats"]["failed"] += 1
            
            # Fallback to UI if API failed
            if not success:
                try:
                    if self.create_activity_via_ui_enhanced(page, activity):
                        success = True
                        method_used = "UI"
                        self.migration_results["ui_stats"]["successful"] += 1
                        logger.info(f"✅ UI Success: {subject[:50]}")
                    else:
                        self.migration_results["ui_stats"]["failed"] += 1
                        logger.error(f"❌ UI failed for: {subject[:50]}")
                except Exception as e:
                    self.migration_results["ui_stats"]["failed"] += 1
                    logger.error(f"❌ UI exception for {subject[:50]}: {e}")
            
            # Record results
            if success:
                successful_count += 1
                self.migration_results["successful"].append({
                    "activity": activity,
                    "method": method_used,
                    "timestamp": datetime.now().isoformat(),
                    "subject": subject
                })
            else:
                failed_count += 1
                self.migration_results["failed"].append({
                    "activity": activity,
                    "error": "Both API and UI methods failed",
                    "timestamp": datetime.now().isoformat(),
                    "subject": subject
                })
            
            # Delay between activities
            time.sleep(2)
            
            # Progress update every 5 activities
            if (i + 1) % 5 == 0:
                logger.info(f"📊 Progress: {i+1}/{len(self.email_activities)} ({successful_count} successful, {failed_count} failed)")
        
        logger.info(f"🔄 Hybrid Migration complete: {successful_count} successful, {failed_count} failed")
        return successful_count, failed_count
    
    def save_migration_results_v2(self):
        """Enhanced results saving with detailed statistics"""
        try:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            
            # Save successful migrations
            success_file = self.config["success_log"].replace('.json', f'_{timestamp}.json')
            with open(success_file, 'w') as f:
                json.dump(self.migration_results["successful"], f, indent=2, default=str)
            
            # Save failed migrations
            failed_file = self.config["failed_log"].replace('.json', f'_{timestamp}.json')
            with open(failed_file, 'w') as f:
                json.dump(self.migration_results["failed"], f, indent=2, default=str)
            
            # Save comprehensive report
            report_file = f"/Users/<USER>/Desktop/wild_weasel_gainsight_migration/migration_report_{timestamp}.json"
            with open(report_file, 'w') as f:
                json.dump(self.migration_results, f, indent=2, default=str)
            
            logger.info(f"📊 Migration results saved:")
            logger.info(f"  ✅ Success: {success_file}")
            logger.info(f"  ❌ Failed: {failed_file}")
            logger.info(f"  📋 Report: {report_file}")
            
        except Exception as e:
            logger.error(f"❌ Failed to save results: {e}")
    
    def generate_comprehensive_report(self):
        """Generate detailed migration report with statistics"""
        total_activities = len(self.email_activities)
        successful_count = len(self.migration_results["successful"])
        failed_count = len(self.migration_results["failed"])
        success_rate = (successful_count / total_activities * 100) if total_activities > 0 else 0
        
        # Calculate duration
        duration = None
        if self.migration_results["start_time"] and self.migration_results["end_time"]:
            start = datetime.fromisoformat(self.migration_results["start_time"])
            end = datetime.fromisoformat(self.migration_results["end_time"])
            duration = end - start
        
        # Method breakdown
        api_success = self.migration_results["api_stats"]["successful"]
        api_failed = self.migration_results["api_stats"]["failed"]
        ui_success = self.migration_results["ui_stats"]["successful"]
        ui_failed = self.migration_results["ui_stats"]["failed"]
        
        report = f"""
🐺 WILD WEASEL v2.0 - COMPREHENSIVE MIGRATION REPORT
{'='*60}
📊 MIGRATION STATISTICS:
  Total Email Activities: {total_activities}
  ✅ Successfully Migrated: {successful_count}
  ❌ Failed Migrations: {failed_count}
  📈 Success Rate: {success_rate:.1f}%
  ⏱️ Duration: {duration}

📋 METHOD BREAKDOWN:
  🔗 API Approach:
     ✅ Successful: {api_success}
     ❌ Failed: {api_failed}
     📈 API Success Rate: {(api_success / (api_success + api_failed) * 100) if (api_success + api_failed) > 0 else 0:.1f}%
  
  🎭 UI Approach:
     ✅ Successful: {ui_success}
     ❌ Failed: {ui_failed}
     📈 UI Success Rate: {(ui_success / (ui_success + ui_failed) * 100) if (ui_success + ui_failed) > 0 else 0:.1f}%

🎯 TARGET INFORMATION:
  Company: ICICI Bank (ID: 0015p00005R7ysqAAB)
  Platform: Gainsight Demo EMEA1
  Activity Type: Email
  
🔧 FIXES IMPLEMENTED:
  ✅ Proper email identification using ID.json mapping
  ✅ Correct API workflow with drafts call
  ✅ Enhanced browser automation with fallback strategies
  ✅ Cookie-based API authentication
  ✅ Comprehensive error handling and retry logic

📁 OUTPUT FILES:
  Success Log: {self.config['success_log']}
  Failed Log: {self.config['failed_log']}
  
🐺 Wild Weasel v2.0 Mission Status: {'✅ COMPLETED SUCCESSFULLY' if failed_count == 0 else f'⚠️ PARTIALLY COMPLETED ({successful_count}/{total_activities} successful)'}
{'='*60}
"""
        
        print(report)
        
        # Save report to file
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        report_file = f"/Users/<USER>/Desktop/wild_weasel_gainsight_migration/migration_report_{timestamp}.txt"
        with open(report_file, 'w') as f:
            f.write(report)
        
        logger.info(f"📄 Comprehensive report saved to: {report_file}")
    
    def execute_mission_v2(self):
        """Execute the enhanced Wild Weasel v2.0 migration mission"""
        self.mission_brief()
        
        # Load email activities
        if not self.load_email_activities():
            logger.error("❌ Mission aborted: Could not load email activities")
            return False
        
        # Get credentials
        self.prompt_for_credentials()
        
        self.migration_results["start_time"] = datetime.now().isoformat()
        
        # Execute hybrid approach (API + UI fallback)
        logger.info("🚀 Starting enhanced hybrid migration approach...")
        
        try:
            with sync_playwright() as p:
                browser = p.chromium.launch(
                    headless=False, 
                    slow_mo=500,
                    args=[
                        '--disable-blink-features=AutomationControlled',
                        '--disable-extensions',
                        '--no-sandbox',
                        '--disable-dev-shm-usage'
                    ]
                )
                context = browser.new_context(
                    user_agent="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36",
                    viewport={"width": 1920, "height": 1080}
                )
                page = context.new_page()
                
                # Store context for potential API cookie extraction
                self.browser_context = context
                
                if self.login_to_gainsight(page):
                    if self.navigate_to_c360_timeline(page):
                        # Execute hybrid migration
                        success_count, failed_count = self.migrate_activities_hybrid_approach(page)
                        
                        logger.info(f"🎯 Migration completed: {success_count} successful, {failed_count} failed")
                    else:
                        logger.error("❌ Failed to navigate to timeline")
                else:
                    logger.error("❌ Failed to login to Gainsight")
                
                browser.close()
                
        except Exception as e:
            logger.error(f"❌ Migration execution failed: {e}")
        
        self.migration_results["end_time"] = datetime.now().isoformat()
        self.migration_results["total_processed"] = len(self.email_activities)
        
        # Save results and generate report
        self.save_migration_results_v2()
        self.generate_comprehensive_report()
        
        logger.info("🐺 Wild Weasel v2.0 mission completed!")
        return True

def main():
    """Main execution function"""
    try:
        agent = WildWeaselAgentV2()
        agent.execute_mission_v2()
        
    except KeyboardInterrupt:
        print("\n🐺 Wild Weasel v2.0 mission interrupted by user")
        sys.exit(0)
    except Exception as e:
        logger.error(f"❌ Wild Weasel v2.0 mission failed: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()

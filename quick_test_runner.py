#!/usr/bin/env python3
"""
🐺 Wild Weasel Quick Test Runner
=============================================================================
Mission: Quick testing and validation of the Wild Weasel migration system

This script provides a simple interface to:
1. Test payload structure
2. Test API connectivity  
3. Run sample migrations
4. Launch full migration
"""

import json
import os
import sys
import subprocess
from datetime import datetime

class WildWeaselTestRunner:
    """Simple test runner for Wild Weasel components"""
    
    def __init__(self):
        self.base_path = "/Users/<USER>/Desktop/wild_weasel_gainsight_migration"
        self.scripts = {
            "payload_fixer": f"{self.base_path}/payload_fixer.py",
            "api_tester": f"{self.base_path}/api_tester.py", 
            "v5_enhanced": f"{self.base_path}/wild_weasel_agent_v5_enhanced.py",
            "v4_final": f"{self.base_path}/wild_weasel_agent_v4_final.py"
        }
    
    def show_menu(self):
        """Show main menu"""
        print("🐺" + "="*80)
        print("  WILD WEASEL QUICK TEST RUNNER")
        print("="*82)
        print("Choose an option:")
        print()
        print("1. 🔧 Fix & Validate Payloads")
        print("   → Fixes empty ID fields and validates payload structure")
        print()
        print("2. 🧪 Test API Connectivity")
        print("   → Tests draft creation and activity upload APIs")
        print()
        print("3. 🚀 Run V5 Enhanced Migration")
        print("   → Full migration with enhanced features")
        print()
        print("4. 🔄 Run V4 Final Migration")
        print("   → Fallback migration option")
        print()
        print("5. 📊 Check System Status")
        print("   → Show current files and logs")
        print()
        print("6. 📖 Show Instructions")
        print("   → How to get session cookies and run migrations")
        print()
        print("0. Exit")
        print("="*82)
    
    def check_files(self):
        """Check if required files exist"""
        print("📁 CHECKING FILES:")
        
        required_files = [
            "gainsight_api_payload_email_activities.json",
            "Gainsight_payload.json",
            "wild_weasel_agent_v5_enhanced.py",
            "payload_fixer.py"
        ]
        
        all_exist = True
        for file in required_files:
            file_path = f"{self.base_path}/{file}"
            exists = os.path.exists(file_path)
            print(f"  {'✅' if exists else '❌'} {file}")
            if not exists:
                all_exist = False
        
        return all_exist
    
    def run_payload_fixer(self):
        """Run payload fixer"""
        print("🔧 RUNNING PAYLOAD FIXER...")
        try:
            result = subprocess.run([
                sys.executable, self.scripts["payload_fixer"]
            ], capture_output=True, text=True, cwd=self.base_path)
            
            print(result.stdout)
            if result.stderr:
                print("STDERR:", result.stderr)
                
        except Exception as e:
            print(f"❌ Error running payload fixer: {e}")
    
    def run_api_tester(self):
        """Run API tester"""
        print("🧪 RUNNING API TESTER...")
        print("Note: You need to add session cookies to the api_tester.py file first!")
        try:
            result = subprocess.run([
                sys.executable, self.scripts["api_tester"]
            ], capture_output=True, text=True, cwd=self.base_path)
            
            print(result.stdout)
            if result.stderr:
                print("STDERR:", result.stderr)
                
        except Exception as e:
            print(f"❌ Error running API tester: {e}")
    
    def run_v5_migration(self):
        """Run V5 enhanced migration"""
        print("🚀 RUNNING V5 ENHANCED MIGRATION...")
        print("This will open a browser window and perform the full migration.")
        print("Make sure you have your Gainsight credentials ready!")
        
        input("Press Enter to continue or Ctrl+C to cancel...")
        
        try:
            subprocess.run([
                sys.executable, self.scripts["v5_enhanced"]
            ], cwd=self.base_path)
                
        except Exception as e:
            print(f"❌ Error running V5 migration: {e}")
    
    def run_v4_migration(self):
        """Run V4 final migration"""
        print("🔄 RUNNING V4 FINAL MIGRATION...")
        print("This is the fallback migration option.")
        
        input("Press Enter to continue or Ctrl+C to cancel...")
        
        try:
            subprocess.run([
                sys.executable, self.scripts["v4_final"]
            ], cwd=self.base_path)
                
        except Exception as e:
            print(f"❌ Error running V4 migration: {e}")
    
    def show_system_status(self):
        """Show current system status"""
        print("📊 SYSTEM STATUS:")
        print()
        
        # Check files
        print("📁 Files:")
        self.check_files()
        print()
        
        # Check recent logs
        print("📄 Recent Log Files:")
        log_files = [
            "wild_weasel_v5_enhanced.log",
            "payload_fixer.log", 
            "api_tester.log"
        ]
        
        for log_file in log_files:
            log_path = f"{self.base_path}/{log_file}"
            if os.path.exists(log_path):
                stat = os.stat(log_path)
                mod_time = datetime.fromtimestamp(stat.st_mtime)
                size_kb = stat.st_size // 1024
                print(f"  ✅ {log_file} ({size_kb}KB) - Modified: {mod_time.strftime('%Y-%m-%d %H:%M')}")
            else:
                print(f"  ❌ {log_file} - Not found")
        print()
        
        # Check payload files
        print("📦 Payload Files:")
        payload_files = [
            "gainsight_api_payload_email_activities.json",
            "gainsight_api_payload_email_activities_FIXED.json"
        ]
        
        for payload_file in payload_files:
            payload_path = f"{self.base_path}/{payload_file}"
            if os.path.exists(payload_path):
                try:
                    with open(payload_path, 'r') as f:
                        data = json.load(f)
                    empty_ids = sum(1 for item in data if item.get('id') == "")
                    print(f"  ✅ {payload_file} - {len(data)} activities, {empty_ids} empty IDs")
                except:
                    print(f"  ⚠️ {payload_file} - Exists but couldn't parse")
            else:
                print(f"  ❌ {payload_file} - Not found")
        print()
        
        # Check migration results
        print("📈 Recent Migration Results:")
        result_files = [f for f in os.listdir(self.base_path) if f.startswith("migration_report_") and f.endswith(".json")]
        result_files.sort(reverse=True)
        
        if result_files:
            for result_file in result_files[:3]:  # Show last 3
                try:
                    with open(f"{self.base_path}/{result_file}", 'r') as f:
                        data = json.load(f)
                    success = len(data.get('successful', []))
                    failed = len(data.get('failed', []))
                    total = data.get('total_processed', success + failed)
                    print(f"  📊 {result_file} - {success}/{total} successful")
                except:
                    print(f"  ⚠️ {result_file} - Couldn't parse")
        else:
            print("  📭 No migration results found yet")
    
    def show_instructions(self):
        """Show detailed instructions"""
        instructions = """
📖 WILD WEASEL INSTRUCTIONS
=============================================================================

🔧 STEP 1: Fix Payloads
  → Run option 1 to fix empty ID fields in your payloads
  → This creates gainsight_api_payload_email_activities_FIXED.json
  → The fixed file has placeholders for IDs that will be injected during migration

🍪 STEP 2: Get Session Cookies (for testing)
  → Login to Gainsight in Chrome/Edge
  → Open Dev Tools (F12) → Network tab
  → Navigate to any page in Gainsight
  → Right-click any request → Copy → Copy as cURL
  → Extract cookie values from the cURL command
  → Add them to api_tester.py or payload_fixer.py

🧪 STEP 3: Test APIs (optional)
  → Run option 2 after adding session cookies
  → This tests draft creation and activity upload
  → Helps debug any API issues before full migration

🚀 STEP 4: Run Migration
  → Option 3: V5 Enhanced (recommended) - Has better error handling
  → Option 4: V4 Final (fallback) - Original version
  → Browser will open, login with your Gainsight credentials
  → Migration will proceed automatically

📊 STEP 5: Check Results
  → Option 5 shows system status and recent results
  → Check migration_report_*.json files for detailed results
  → Check *.log files for debugging information

🐺 THE KEY ISSUE & SOLUTION:
  Problem: Payloads have "id": "" (empty ID fields)
  Solution: Draft API creates unique IDs, we inject them into payloads
  Process: Draft API → Get ID → Inject into payload → Activity API

=============================================================================
"""
        print(instructions)
    
    def run(self):
        """Main runner"""
        while True:
            try:
                self.show_menu()
                choice = input("\nEnter your choice (0-6): ").strip()
                
                if choice == "0":
                    print("👋 Goodbye!")
                    break
                elif choice == "1":
                    self.run_payload_fixer()
                elif choice == "2":
                    self.run_api_tester()
                elif choice == "3":
                    self.run_v5_migration()
                elif choice == "4":
                    self.run_v4_migration()
                elif choice == "5":
                    self.show_system_status()
                elif choice == "6":
                    self.show_instructions()
                else:
                    print("❌ Invalid choice. Please try again.")
                
                if choice != "0":
                    input("\nPress Enter to continue...")
                    
            except KeyboardInterrupt:
                print("\n👋 Goodbye!")
                break
            except Exception as e:
                print(f"❌ Error: {e}")
                input("Press Enter to continue...")

def main():
    """Main execution"""
    runner = WildWeaselTestRunner()
    runner.run()

if __name__ == "__main__":
    main()

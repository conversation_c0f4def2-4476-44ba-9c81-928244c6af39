{"total_activities": 37, "date_range": {"earliest": "2021-11-18T14:37:18", "latest": "2024-04-25T14:57:49.881000"}, "touchpoint_breakdown": {"COMMUNICATION": 37}, "author_breakdown": {"<EMAIL>": 8}, "gainsight_type_breakdown": {"Meeting": 25, "Update": 8, "Email": 4}, "totango_type_breakdown": {"Web meeting": 25, "Internal Note": 8, "Email": 4}, "custom_vs_default": {"default": 0, "custom": 0}, "company_id": "0015p00005R7ysqAAB", "ready_for_migration": true, "api_ready_format": true, "type_mapping_summary": {"default_types": {"email": {"totango_name": "Email", "gainsight_type": "Email", "expected_count": 27361}, "web_meeting": {"totango_name": "Web meeting", "gainsight_type": "Meeting", "expected_count": 12597}, "internal_note": {"totango_name": "Internal Note", "gainsight_type": "Update", "expected_count": 2491}, "telephone_call": {"totango_name": "Telephone call", "gainsight_type": "Call", "expected_count": 22}}, "custom_types": {"inbound": {"totango_name": "Inbound", "gainsight_type": "Inbound", "expected_count": 3391}, "gong_call": {"totango_name": "<PERSON>", "gainsight_type": "<PERSON>", "expected_count": 2823}, "slack": {"totango_name": "<PERSON><PERSON>ck", "gainsight_type": "<PERSON><PERSON>ck", "expected_count": 30}, "in_person_meeting": {"totango_name": "In person meeting", "gainsight_type": "In-Person Meeting", "expected_count": 14}, "feedback": {"totango_name": "<PERSON><PERSON><PERSON>", "gainsight_type": "<PERSON><PERSON><PERSON>", "expected_count": 3}}}}
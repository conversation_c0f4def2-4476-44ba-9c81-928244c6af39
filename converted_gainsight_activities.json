[{"id": 1, "subject": "Re: Queries on Licensing Policy related details - ICICI Bank", "content": "<div>\n<div class=\"nH V8djrc byY\" style=\"align-items: flex-start; display: flex; padding: 20px 0px 8px 72px;\">\n<div class=\"nH\">\n<div data-num-unread-messages=\"1\" data-num-visible-messages=\"3\">\n<div class=\"ha\" style=\"background-image: inherit; background-position: inherit; background-size: inherit; background-repeat: inherit; background-attachment: inherit; background-origin: inherit; background-clip: inherit; background-color: transparent; border-right: inherit; color: #222222; font-family: inherit; font-size: 1.375rem; font-weight: 500; line-height: 28px; margin: 0px; padding: 0px;\">\n<h2 class=\"hP\" style=\"margin: 0px; padding: 0px 10px 0px 0px; border: 0px; font-style: inherit; font-variant-ligatures: no-contextual; font-variant-caps: inherit; font-variant-numeric: inherit; font-variant-east-asian: inherit; font-variant-alternates: inherit; font-weight: 400; font-stretch: inherit; font-size: 1.375rem; line-height: inherit; font-family: 'Google Sans', <PERSON><PERSON>, <PERSON><PERSON><PERSON>, Helvetica, Arial, sans-serif; font-optical-sizing: inherit; font-kerning: inherit; font-feature-settings: inherit; font-variation-settings: inherit; display: inline; outline: none; word-break: break-word; -webkit-font-smoothing: antialiased; color: #1f1f1f;\" tabindex=\"-1\" data-thread-perm-id=\"thread-f:1771204077641986954\" data-legacy-thread-id=\"189494b97092f38a\">Re: Queries on Licensing Policy related details - ICICI Bank</h2>", "plainText": "Re: Queries on Licensing Policy related details - ICICI Bank External Inbox Search for all messages with label Inbox Remove label Inbox from this conversation <PERSON> 11:50 AM (9 hours ago) Hi <PERSON>, We provide details about each license and a risk score in the Mend UI. Have a look at this: https://docs.mend.io/bundle/sca_user_guide/page/understandi Ok<PERSON>a Shtril Customer Success Manager <PERSON><PERSON><PERSON>ril 6:54 PM (2 hours ago) Ok<PERSON>a Shtril Customer Success Manager <PERSON><PERSON><PERSON> 8:18 PM (43 minutes ago) to <PERSON>, support, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, Thanks for the mail. The team in ICICI is setting up a process now to utilize and manage the open-source licenses. That's where they are seeking our assistance. We got the below queries from ICICI team. We will review the links you sent. But I believe a short call with ICICI team will help them to setup a process faster. Can you please let me know your availability for a short call to discuss the below queries with ICICI team, What is open source and closed source? Why risk score is not mentioned for closed & open source ? For the blank risk score of open/closed source, all the details is given in the report but why not the risk score is defined for it? What action needs to be taken on suspected commercial for e.g. (https://itextpdf.com/) seems to be paid library? From: <PERSON> < <EMAIL> > Sent: Wednesday, July 12, 2023 2:20 PM To: Raviteja < <EMAIL> > Cc: <EMAIL> ; Sunny Kanfer < <EMAIL> >; Oksana Shtril < <EMAIL> > Subject: Re: Queries on Licensing Policy related details - ICICI Bank Hi Ravi, We provide details about each license and a risk score in the Mend UI. Have a look at this: https://docs.mend.io/bundle/ sca_user_guide/page/ understanding_risk_score_ attribution_and_license_ analysis.html The actions taken depend on each customer and their legal team. They need to define their policies according to their willingness to take legal risks or not. You as a partner should be able to give them some guidance about that ideally. We have some useful info and articles in our resources page about license compliance: https://www.mend. io/resources/?topic=license- compliance For the definition I just copy paste from the internet: With closed source software (also known as proprietary software), the public is not given access to the source code, so they can't see or modify it in any way. But with open source software, the source code is publicly available to anyone who wants it, and programmers can read or change that code if they desire. Does this help? Kind regards, Luis", "activityDate": 1689185090000, "author": "<PERSON><PERSON><PERSON>", "authorEmail": "<EMAIL>", "type": "EMAIL"}, {"id": 2, "subject": "Re: Mend integration with LDAP and SAML - ICICI", "content": "<div>\n<div class=\"nH V8djrc byY\" style=\"align-items: flex-start; display: flex; padding: 20px 0px 8px 72px;\">\n<div class=\"nH\">\n<div data-num-unread-messages=\"1\" data-num-visible-messages=\"1\">\n<div class=\"ha\" style=\"background-image: inherit; background-position: inherit; background-size: inherit; background-repeat: inherit; background-attachment: inherit; background-origin: inherit; background-clip: inherit; background-color: transparent; border-right: inherit; color: #222222; font-family: inherit; font-size: 1.375rem; font-weight: 500; line-height: 28px; margin: 0px; padding: 0px;\">\n<h2 class=\"hP\" style=\"margin: 0px; padding: 0px 10px 0px 0px; border: 0px; font-style: inherit; font-variant-ligatures: no-contextual; font-variant-caps: inherit; font-variant-numeric: inherit; font-variant-east-asian: inherit; font-variant-alternates: inherit; font-weight: 400; font-stretch: inherit; font-size: 1.375rem; line-height: inherit; font-family: 'Google Sans', <PERSON><PERSON>, <PERSON><PERSON><PERSON>, Helvetica, Arial, sans-serif; font-optical-sizing: inherit; font-kerning: inherit; font-feature-settings: inherit; font-variation-settings: inherit; display: inline; outline: none; word-break: break-word; -webkit-font-smoothing: antialiased; color: #1f1f1f;\" tabindex=\"-1\" data-thread-perm-id=\"thread-f:1771025053268752206\" data-legacy-thread-id=\"1893f1e71466b74e\">Re: Mend integration with LDAP and SAML - ICICI</h2>", "plainText": "Re: Mend integration with LDAP and SAML - ICICI External Inbox Search for all messages with label Inbox Remove label Inbox from this conversation <PERSON>, Jul 10, 12:25 PM (2 days ago) to <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, support <PERSON>, Yes, we have that topic as an item for the things we want to discuss with ICICI in an enablement session. kind regards, <PERSON>, Jul 10, 2023 at 10:43 AM Raviteja < <EMAIL> > wrote: <PERSON> <PERSON>, Good Day I hope you have reviewed my last mail. It will be great if you could let us know your availability for a short web call with ICICI Bank to discuss about the same. From: <PERSON><PERSON><PERSON> < <EMAIL> > Sent: Thursday, July 6, 2023 6:08 PM To: 'Ori <PERSON>' < <EMAIL> > Cc: '<PERSON>' < <EMAIL> >; <EMAIL> Subject: RE: Mend integration with LDAP and SAML - ICICI Hi Ori, Good Day Thanks for the response for sharing the Metadata file location. It will be great if you can come for a web call as ICICI Bank team has other queries on the documentation as well. From: <PERSON><PERSON> < <EMAIL> > Sent: Thursday, July 6, 2023 12:50 PM To: Ravi<PERSON><PERSON> < <EMAIL> > Cc: <PERSON> < <EMAIL> >; <EMAIL> Subject: Re: Mend integration with LDAP and SAML - ICICI Hi Raviteja, Metadata file can be found at: https://app. whitesourcesoftware.com/saml/ metadata Best regards, Ori. Ori Tabac | Sales Engineer | Mend <EMAIL> On Thu, Jul 6, 2023 at 8:44 AM Raviteja < <EMAIL> > wrote: Hi Ori, Good Day We had a discussion with the ICICI Bank team. ICICI Bank team wants to have a call in order to get few details from our side https://docs.mend.io/bundle/ sca_user_guide/page/saml_2_0_ integration.html Under this link there is link for Metadata file https://idp.ssocircle.com/ The team is asking who will provide this certificate. This certificate link is sample one. In addition to this he has some more queries for the same. It will be great if you can come for a short web call to discuss about SAML integration. From: Ori Tabac < <EMAIL> > Sent: Tuesday, July 4, 2023 5:45 PM To: Luis Bretones < <EMAIL> > Cc: Raviteja < <EMAIL> >; <EMAIL> Subject: Re: Mend integration with LDAP and SAML - ICICI Hi Ravi, Nice to meet you. Our solution is a SaaS solution, therefore we support SAML 2.0 integration. I believe it is possible to connect your local AD with SAML 2.0 using AD FS service, thus please note you need to expose the service to the world to configure it. Best regards, Ori. Ori Tabac | Sales Engineer | Mend <EMAIL> On Tue, Jul 4, 2023 at 2:04 PM Luis Bretones < <EMAIL> > wrote: Hi Ravi, Let me add Ori here. Thanks! Cheers Luis On Tue, 4 Jul 2023 at 13:39, Raviteja < <EMAIL> > wrote: Hi Luis, We got a below support request from ICICI Bank team. ICICI Bank team is looking to integrate Mend SCA with their LDAP and SAML. This is supported by Checkmarx which they are using along with Mend SCA. We have gone through the documentation and we found the documentation related to SAML integration https://docs.mend.io/bundle/ sca_user_guide/page/saml_2_0_ integration.html But for LDAP integration I was not able to see any documentation. It will be great if you could let us know how we can go ahead on LDAP integration with Mend SCA. -- Luis Bretones Hernandez EMEA & APAC Channel Director P. +34-699-615-658 www.mend.io", "activityDate": *************, "author": "<PERSON><PERSON><PERSON>", "authorEmail": "<EMAIL>", "type": "EMAIL"}, {"id": 3, "subject": "Setup a call with customers and explains our Policies best practice", "content": "<div><span style=\"font-family: <PERSON><PERSON><PERSON>-<PERSON>, <PERSON><PERSON>-<PERSON>, <PERSON><PERSON><PERSON>, sans-serif, 'arial !important;'; font-size: 14px;\">BP Account</span></div>\n<div><span style=\"font-family: <PERSON><PERSON><PERSON>-<PERSON>, <PERSON><PERSON>-<PERSON>, <PERSON><PERSON><PERSON>, sans-serif, 'arial !important;'; font-size: 14px;\">Not relevant</span></div>", "plainText": "BP Account Not relevant", "activityDate": *************, "author": "<PERSON><PERSON><PERSON>", "authorEmail": "<EMAIL>", "type": "EMAIL"}, {"id": 4, "subject": "Customer Communication Regarding LBA to VBA Migration - SCA-ICICI Bank - for SCA-ICICI Bank", "content": "<div>sent</div>", "plainText": "sent", "activityDate": *************, "author": "<PERSON><PERSON><PERSON>", "authorEmail": "<EMAIL>", "type": "EMAIL"}]
# 🐺 Wild Weasel v5.0 Enhanced - Complete Migration System

## The Wild Weasel Charter

> Migrations are hard, painful, in some cases boring (unless you're a torture junkie)
> 
> Typically customers do not want to do them and product teams do not want to invest resources in them (shiny new features are key)
> 
> We are facing a design shift in how systems are architected in terms of the rise of agentic solutions.
> 
> IMHO - standard integrations are going to die - as is to an extent some of the services that traditional professional services organisations provide
> 
> Wild Weasel is designed to be sneaky (hence the name). Its mission is the following:

### Primary Mission:
1. **Log into a SAAS platform as an agent** (The Primary Mission)
2. **Look for structured or unstructured information based on its mission** (The Source)  
3. **Download and classify that information in a low cost storage solution** (The Destination)
4. **Report back to the mission controller on status when prompted** (The Communication)
5. **Destroy all information at the Destination once instructed to** (The Cleanup)

## 🎯 Current Mission: Totango → Gainsight Migration

**Target:** Migrate email activities from Totango to Gainsight for ICICI Bank
**Challenge:** Empty ID fields in payloads that need draft IDs from Gainsight API
**Solution:** Enhanced API workflow with proper ID injection

## 📁 Enhanced System Components

### 🚀 Main Migration Scripts

#### 1. **wild_weasel_agent_v5_enhanced.py** (RECOMMENDED)
- **Enhanced API integration** with multiple retry strategies
- **Payload validation and auto-correction**
- **Advanced draft ID capture** with 6 different extraction methods
- **Comprehensive error handling** with debug file generation
- **API connectivity testing** before migration
- **Better UI automation** with enhanced element detection

#### 2. **wild_weasel_agent_v4_final.py** (FALLBACK)
- Original API integration approach
- Hybrid UI/API workflow
- Network traffic monitoring for draft ID capture

### 🔧 Testing & Debugging Tools

#### 3. **payload_fixer.py**
- **Fixes empty ID fields** in your payloads
- **Validates payload structure** for Gainsight API compatibility
- **Tests single migrations** to verify API workflow
- **Auto-corrects common issues** in payload format

#### 4. **api_tester.py**
- **Comprehensive API testing** for draft and activity endpoints
- **Debug ID extraction** from API responses
- **Payload validation** before API calls
- **Session cookie testing** and authentication validation

#### 5. **quick_test_runner.py**
- **Simple menu interface** for all Wild Weasel components
- **System status checking** with file and log analysis
- **One-click testing** and migration launching

## 🔑 The Key Issue & Solution

### **Problem Identified:**
Your Gainsight payloads have `"id": ""` (empty ID fields) which causes API failures.

### **Root Cause:**
Gainsight requires a unique draft ID before creating activities:
1. First call `/v2/activity/drafts` API to create a draft
2. Extract the unique ID from the response
3. Inject that ID into your payload
4. Then call `/v2/activity` API to create the actual activity

### **Wild Weasel Solution:**
1. **Automatic Draft Creation:** Creates drafts via API with company/subject info
2. **Smart ID Extraction:** Uses 6 different methods to extract draft IDs from responses
3. **Dynamic ID Injection:** Injects captured IDs into your payloads
4. **Robust Error Handling:** Fallback strategies if any step fails

## 🚀 Quick Start Guide

### Method 1: Use the Quick Test Runner (EASIEST)
```bash
cd /Users/<USER>/Desktop/wild_weasel_gainsight_migration
python quick_test_runner.py
```

**Follow the menu:**
1. **Fix Payloads** - Fixes empty ID fields and validates structure
2. **Test APIs** - Debug API connectivity (requires session cookies)
3. **Run V5 Migration** - Full enhanced migration (RECOMMENDED)

### Method 2: Direct Script Execution

#### Step 1: Fix Your Payloads
```bash
python payload_fixer.py
```
- Fixes empty `"id": ""` fields with placeholders
- Validates all payload structures
- Creates `gainsight_api_payload_email_activities_FIXED.json`

#### Step 2: Run Enhanced Migration
```bash
python wild_weasel_agent_v5_enhanced.py
```
- Opens browser for Gainsight login
- Automatically navigates to C360 Timeline
- Creates drafts → extracts IDs → creates activities
- Generates comprehensive reports

## 🔧 Advanced Configuration

### Adding Session Cookies for API Testing

If you want to test the API endpoints directly:

1. **Login to Gainsight** in your browser
2. **Open Dev Tools** (F12) → Network tab
3. **Make any request** to `demo-emea1.gainsightcloud.com`
4. **Copy the Cookie header** from the request
5. **Add cookies to the test scripts:**

```python
# In api_tester.py or payload_fixer.py
self.session_cookies = {
    "sessionid": "your_session_value",
    "csrftoken": "your_csrf_token",
    "authtoken": "your_auth_token"
    # ... add all cookies from the header
}
```

### Environment Variables (Optional)
```bash
export GAINSIGHT_USERNAME="your_username"
export GAINSIGHT_PASSWORD="your_password"
export HEADLESS_MODE="false"  # Set to true for headless browser
```

## 📊 System Files Overview

### Input Files:
- `gainsight_api_payload_email_activities.json` - Your original payloads (with empty IDs)
- `Gainsight_payload.json` - Reference payload structure

### Generated Files:
- `gainsight_api_payload_email_activities_FIXED.json` - Fixed payloads with ID placeholders
- `migration_report_v5_enhanced_YYYYMMDD_HHMMSS.json` - Detailed migration results
- `migration_success_v5_YYYYMMDD_HHMMSS.json` - Successful migrations
- `migration_failed_v5_YYYYMMDD_HHMMSS.json` - Failed migrations with error details

### Debug Files:
- `wild_weasel_v5_enhanced.log` - Detailed execution logs
- `debug_payload_[draft_id].json` - Payloads with injected IDs for debugging
- `api_error_[draft_id].json` - API error responses for troubleshooting
- Various screenshot files for UI debugging

## 🎯 Expected Results

### Successful Migration Output:
```
🐺 WILD WEASEL v5.0 - ENHANCED API UPLOAD FOCUS REPORT
================================================================================
📊 FINAL INTEGRATION SUCCESS:
  ✅ PRE-FORMATTED PAYLOADS:
     → Used ready-to-deploy Gainsight API payloads
     → No data transformation required
     → Seamless API integration
  
  ✅ API WORKFLOW SUCCESS:
     → Proper drafts → activity API sequence
     → Successful API calls: 4
     → Failed API calls: 0

📈 MIGRATION RESULTS:
  Total Activities: 4
  ✅ Successfully Migrated: 4
  ❌ Failed Migrations: 0
  📈 Success Rate: 100.0%
  ⏱️ Duration: 0:02:15

🎯 TARGET INFORMATION:
  Company: ICICI Bank
  Platform: Gainsight Demo EMEA1
  Activity Type: Email (Enhanced Validation Applied)
  Payload Format: Gainsight API Ready (Validated & Corrected)

🐺 Wild Weasel v5.0 Enhanced Mission Status: ✅ COMPLETED SUCCESSFULLY
================================================================================
```

## 🔍 Troubleshooting

### Common Issues & Solutions:

#### 1. "Empty ID fields" Error
**Solution:** Run `payload_fixer.py` first to fix the empty ID fields.

#### 2. API Authentication Errors
**Solution:** 
- Check your Gainsight credentials
- Ensure you're using the correct environment URL
- Try refreshing your browser session

#### 3. Draft ID Not Captured
**Enhanced Solution in V5:**
- Uses 6 different extraction methods
- Monitors network traffic for 20 seconds
- Provides detailed logging of all API responses
- Saves debug files for manual inspection

#### 4. Browser Automation Issues
**Solution:**
- Run with `headless=False` to see what's happening
- Check the generated screenshots in debug files
- Try the V4 fallback version if V5 has issues

#### 5. Payload Validation Errors
**Solution:**
- Use the payload validator in `payload_fixer.py`
- Check the generated error reports
- Compare with the working `Gainsight_payload.json` example

## 📈 Performance Metrics

The Enhanced V5 system includes:
- **6 draft ID extraction strategies** (vs 3 in V4)
- **Payload validation and auto-correction** (new in V5)
- **API connectivity pre-testing** (new in V5)
- **Enhanced error reporting** with debug file generation
- **Retry logic** for failed API calls
- **Multiple UI element detection strategies**

## 🐺 Wild Weasel Advantages

1. **Sneaky & Automated:** Works like a human user but faster and more reliable
2. **Self-Healing:** Multiple fallback strategies for each step
3. **Comprehensive Logging:** Every step is logged for debugging
4. **Flexible:** Works with any Gainsight environment
5. **Extensible:** Can be adapted for other SaaS migrations

## 🚨 Important Notes

- **Browser Required:** The migration opens a real browser for authentication
- **Network Dependent:** Requires stable internet connection
- **Rate Limited:** Includes delays to avoid overwhelming the API
- **Credential Security:** Credentials are not stored, only used for session
- **Debug Mode:** All runs generate detailed logs and debug files

## 📞 Support & Debugging

If you encounter issues:

1. **Check the logs:** Look at `wild_weasel_v5_enhanced.log`
2. **Run the test runner:** Use `quick_test_runner.py` for system status
3. **Validate payloads:** Use `payload_fixer.py` to check structure
4. **Test APIs individually:** Use `api_tester.py` with session cookies
5. **Review debug files:** Check generated debug files for API responses

---

## 🎯 Mission Accomplished?

When Wild Weasel completes successfully, you should see:
- ✅ All activities migrated to Gainsight
- 📊 Comprehensive migration report
- 🎉 ICICI Bank timeline populated with Totango email activities
- 🔍 Full audit trail in logs and debug files

**The Wild Weasel has successfully infiltrated Gainsight and completed the migration mission! 🐺**

#!/usr/bin/env python3
"""
🐺 Wild Weasel v5.1 - Enhanced Meeting Type Mapping Validator
=============================================================================
Test script to validate enhanced Totango → Gainsight data transformation with meeting type mapping
"""

import json
import sys
import os
from datetime import datetime, timezone
from typing import Dict, List, Any, Optional

# Add the current directory to path to import our transformer
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def create_test_events_with_meeting_types() -> List[Dict[str, Any]]:
    """Create test events with various meeting types to validate mapping"""
    test_events = [
        # Email type event
        {
            "id": "test_email_001",
            "timestamp": *************,
            "type": "meeting",
            "account": {"id": "0015p00005R7ysqAAB"},
            "properties": {
                "meeting_type": "Email",
                "subject": "Follow-up Email to ICICI Bank",
                "entity_name": "Test User"
            }
        },
        # Web Meeting event  
        {
            "id": "test_meeting_001",
            "timestamp": *************,
            "type": "meeting",
            "account": {"id": "0015p00005R7ysqAAB"},
            "properties": {
                "meeting_type_name": "Web Meeting",
                "subject": "Quarterly Business Review",
                "entity_name": "Test User"
            }
        },
        # Telephone Call event
        {
            "id": "test_call_001", 
            "timestamp": *************,
            "type": "meeting",
            "account": {"id": "0015p00005R7ysqAAB"},
            "properties": {
                "meeting_type": "Telephone Call",
                "subject": "Support Call with ICICI",
                "entity_name": "Test User"
            }
        },
        # Internal Note event
        {
            "id": "test_note_001",
            "timestamp": *************, 
            "type": "meeting",
            "account": {"id": "0015p00005R7ysqAAB"},
            "properties": {
                "meeting_type": "Internal Note",
                "subject": "Customer update notes",
                "entity_name": "Test User"
            }
        },
        # Custom Gainsight type - In-Person Meeting
        {
            "id": "test_inperson_001",
            "timestamp": *************,
            "type": "meeting", 
            "account": {"id": "0015p00005R7ysqAAB"},
            "properties": {
                "meeting_type": "In-Person Meeting",
                "subject": "Executive Meeting at ICICI Office",
                "entity_name": "Test User"
            }
        },
        # Custom Gainsight type - Gong Call
        {
            "id": "test_gong_001",
            "timestamp": *************,
            "type": "meeting",
            "account": {"id": "0015p00005R7ysqAAB"},
            "properties": {
                "meeting_type": "Gong Call",
                "subject": "Recorded Sales Call",
                "entity_name": "Test User"
            }
        },
        # Custom Gainsight type - Slack
        {
            "id": "test_slack_001",
            "timestamp": *************,
            "type": "meeting",
            "account": {"id": "0015p00005R7ysqAAB"},
            "properties": {
                "meeting_type": "Slack",
                "subject": "Slack conversation with customer",
                "entity_name": "Test User"
            }
        },
        # Unknown meeting type (should fall back)
        {
            "id": "test_unknown_001",
            "timestamp": *************,
            "type": "meeting",
            "account": {"id": "0015p00005R7ysqAAB"},
            "properties": {
                "meeting_type": "Unknown Type",
                "subject": "Some unknown meeting type",
                "entity_name": "Test User"
            }
        },
        # Non-meeting event (automated_attribute_change)
        {
            "id": "test_attr_001",
            "timestamp": *************,
            "type": "automated_attribute_change",
            "account": {"id": "0015p00005R7ysqAAB"},
            "properties": {
                "entity_name": "ICICI Bank",
                "display_name": "Platform Access", 
                "action": "update",
                "prev_value": "Active",
                "new_value": "Inactive"
            }
        },
        # Campaign touch event
        {
            "id": "test_campaign_001",
            "timestamp": *************,
            "type": "campaign_touch",
            "account": {"id": "0015p00005R7ysqAAB"},
            "properties": {
                "name": "Monthly Newsletter",
                "subject": "December Newsletter: Platform Updates",
                "description": "Monthly product update newsletter",
                "targeted_users_count": 5
            }
        }
    ]
    
    return test_events

def load_sample_data(file_path: str, sample_size: int = 5) -> List[Dict[str, Any]]:
    """Load a sample of Totango data for testing"""
    try:
        with open(file_path, 'r') as f:
            data = json.load(f)
        
        # Return a sample that includes events with meeting types if available
        sample_data = []
        meeting_events = []
        other_events = []
        
        for item in data:
            properties = item.get('properties', {})
            has_meeting_type = any(key in properties for key in ['meeting_type', 'meeting_type_name', 'type', 'activity_type'])
            
            if has_meeting_type:
                meeting_events.append(item)
            else:
                other_events.append(item)
        
        # Prioritize events with meeting types
        sample_data.extend(meeting_events[:3])
        sample_data.extend(other_events[:sample_size - len(sample_data)])
        
        # If we don't have enough, just take the first few
        if len(sample_data) < sample_size:
            sample_data = data[:sample_size]
        
        return sample_data
        
    except Exception as e:
        print(f"❌ Error loading sample data: {e}")
        return []

def validate_gainsight_payload(payload: Dict[str, Any]) -> List[str]:
    """Validate that the Gainsight payload has all required fields"""
    errors = []
    
    # Check top-level structure
    required_top_level = ['lastModifiedByUser', 'note', 'meta', 'author', 'contexts']
    for field in required_top_level:
        if field not in payload:
            errors.append(f"Missing top-level field: {field}")
    
    # Check note structure
    if 'note' in payload:
        note = payload['note']
        required_note_fields = ['type', 'subject', 'activityDate', 'content', 'plainText']
        for field in required_note_fields:
            if field not in note:
                errors.append(f"Missing note field: {field}")
        
        # Validate customFields
        if 'customFields' not in note:
            errors.append("Missing note.customFields")
        else:
            custom_fields = note['customFields']
            if 'Ant__Source_System__c' not in custom_fields:
                errors.append("Missing source system tracking field")
            if 'Ant__Source_ID__c' not in custom_fields:
                errors.append("Missing source ID tracking field")
    
    # Check contexts (company mapping)
    if 'contexts' in payload:
        contexts = payload['contexts']
        if not contexts or len(contexts) == 0:
            errors.append("Missing company context")
        else:
            context = contexts[0]
            required_context_fields = ['id', 'obj', 'lbl']
            for field in required_context_fields:
                if field not in context:
                    errors.append(f"Missing context field: {field}")
    
    # Check meta
    if 'meta' in payload:
        meta = payload['meta']
        required_meta_fields = ['activityTypeId', 'source', 'systemType']
        for field in required_meta_fields:
            if field not in meta:
                errors.append(f"Missing meta field: {field}")
    
    return errors

def test_meeting_type_mapping():
    """Test the meeting type mapping specifically"""
    print("🔄" + "="*70)
    print("  TESTING MEETING TYPE MAPPING")
    print("="*72)
    
    try:
        from wild_weasel_agent_v5_1_meeting_enhanced import TotangoDataTransformer
        print("✅ Successfully imported enhanced TotangoDataTransformer")
    except Exception as e:
        print(f"❌ Failed to import enhanced transformer: {e}")
        return False
    
    transformer = TotangoDataTransformer()
    test_events = create_test_events_with_meeting_types()
    
    print(f"🧪 Testing {len(test_events)} events with various meeting types...")
    
    all_tests_passed = True
    
    for i, event in enumerate(test_events):
        event_id = event.get('id', f'unknown_{i}')
        event_type = event.get('type', 'unknown')
        properties = event.get('properties', {})
        meeting_type = properties.get('meeting_type') or properties.get('meeting_type_name', 'N/A')
        
        print(f"\n--- Test {i+1}: {event_type} | Meeting Type: {meeting_type} ---")
        
        try:
            # Test the activity type determination
            activity_type, detected_meeting_type = transformer._determine_activity_type(event_type, properties)
            
            print(f"  📝 Event Type: {event_type}")
            print(f"  🎯 Meeting Type: {meeting_type}")
            print(f"  ➡️ Mapped Activity Type: {activity_type}")
            print(f"  🔍 Detected Meeting Type: {detected_meeting_type}")
            
            # Validate expected mappings
            expected_mappings = {
                "Email": "Email",
                "Web Meeting": "Meeting", 
                "Telephone Call": "Call",
                "Internal Note": "Update",
                "In-Person Meeting": "In-Person Meeting",
                "Gong Call": "Gong Call",
                "Slack": "Slack",
                "Unknown Type": "Meeting"  # Should fall back to event type mapping
            }
            
            if meeting_type in expected_mappings:
                expected = expected_mappings[meeting_type]
                if activity_type == expected:
                    print(f"  ✅ Correct mapping: {meeting_type} → {activity_type}")
                else:
                    print(f"  ❌ Incorrect mapping: {meeting_type} → {activity_type} (expected {expected})")
                    all_tests_passed = False
            else:
                print(f"  ℹ️ Non-meeting event or unmapped type")
            
            # Transform the full event
            gainsight_payload = transformer.transform_to_gainsight(event)
            
            if gainsight_payload is None:
                print(f"  ⚠️ Transformation returned None")
                continue
            
            # Validate the payload
            validation_errors = validate_gainsight_payload(gainsight_payload)
            
            if validation_errors:
                print("  ❌ Payload validation failed:")
                for error in validation_errors:
                    print(f"    • {error}")
                all_tests_passed = False
            else:
                print("  ✅ Payload validation passed")
                
                # Show key details
                note = gainsight_payload.get('note', {})
                print(f"  📧 Final Activity Type: {note.get('type', 'N/A')}")
                print(f"  📝 Subject: {note.get('subject', 'N/A')[:50]}...")
                print(f"  🔗 Original Meeting Type: {note.get('customFields', {}).get('Ant__Original_Meeting_Type__c', 'N/A')}")
        
        except Exception as e:
            print(f"  ❌ Test failed with exception: {e}")
            all_tests_passed = False
    
    return all_tests_passed

def test_real_data_transformation():
    """Test transformation with real Totango data"""
    print("\n🔍" + "="*70)
    print("  TESTING WITH REAL TOTANGO DATA")
    print("="*72)
    
    # Import our transformer
    try:
        from wild_weasel_agent_v5_1_meeting_enhanced import TotangoDataTransformer
        print("✅ Successfully imported enhanced TotangoDataTransformer")
    except Exception as e:
        print(f"❌ Failed to import enhanced transformer: {e}")
        return False
    
    # Load sample data
    data_file = "/Users/<USER>/Desktop/totango/ICICI_processed.json"
    if not os.path.exists(data_file):
        print(f"❌ Totango data file not found: {data_file}")
        return False
    
    print(f"📁 Loading sample data from: {data_file}")
    sample_data = load_sample_data(data_file, sample_size=5)
    
    if not sample_data:
        print("❌ No sample data loaded")
        return False
    
    print(f"📊 Loaded {len(sample_data)} sample events for testing")
    
    # Initialize transformer
    transformer = TotangoDataTransformer()
    print("🔄 Enhanced transformer initialized")
    
    # Test transformation
    print("\n🧪 Testing enhanced data transformation...")
    all_tests_passed = True
    
    for i, event in enumerate(sample_data):
        event_id = event.get('id', f'unknown_{i}')
        event_type = event.get('type', 'unknown')
        properties = event.get('properties', {})
        
        # Check for meeting types in the data
        meeting_type = None
        for key in ['meeting_type', 'meeting_type_name', 'type', 'activity_type']:
            if key in properties and properties[key]:
                meeting_type = str(properties[key]).strip()
                break
        
        print(f"\n--- Real Data Test {i+1}: {event_type} ---")
        print(f"  📋 Event ID: {event_id[:30]}...")
        print(f"  🎯 Meeting Type: {meeting_type if meeting_type else 'None'}")
        
        try:
            # Transform the event
            gainsight_payload = transformer.transform_to_gainsight(event)
            
            if gainsight_payload is None:
                print(f"  ⚠️ Transformation returned None (may be expected for some events)")
                continue
            
            # Validate the payload
            validation_errors = validate_gainsight_payload(gainsight_payload)
            
            if validation_errors:
                print("  ❌ Validation failed:")
                for error in validation_errors:
                    print(f"    • {error}")
                all_tests_passed = False
            else:
                print("  ✅ Transformation and validation passed")
                
                # Show key details
                note = gainsight_payload.get('note', {})
                custom_fields = note.get('customFields', {})
                print(f"  📧 Activity Type: {note.get('type', 'N/A')}")
                print(f"  📝 Subject: {note.get('subject', 'N/A')[:50]}...")
                print(f"  📅 Activity Date: {note.get('activityDate', 'N/A')}")
                print(f"  🏢 Company: {gainsight_payload.get('contexts', [{}])[0].get('lbl', 'N/A')}")
                print(f"  🔗 Source ID: {custom_fields.get('Ant__Source_ID__c', 'N/A')}")
                print(f"  🎯 Original Meeting Type: {custom_fields.get('Ant__Original_Meeting_Type__c', 'N/A')}")
        
        except Exception as e:
            print(f"  ❌ Transformation failed with exception: {e}")
            all_tests_passed = False
    
    return all_tests_passed

def show_mapping_reference():
    """Show the complete meeting type mapping reference"""
    print("\n📋" + "="*70)
    print("  MEETING TYPE MAPPING REFERENCE")
    print("="*72)
    
    try:
        from wild_weasel_agent_v5_1_meeting_enhanced import TotangoDataTransformer
        transformer = TotangoDataTransformer()
        
        print("🔄 TOTANGO MEETING TYPE → GAINSIGHT ACTIVITY TYPE MAPPINGS:")
        print("\n📧 Default Gainsight Types:")
        default_mappings = [
            ("Email", "Email"),
            ("Telephone Call", "Call"),
            ("Web Meeting", "Meeting"), 
            ("Internal Note", "Update")
        ]
        
        for totango_type, gainsight_type in default_mappings:
            mapped = transformer.meeting_type_mapping.get(totango_type, "NOT MAPPED")
            status = "✅" if mapped == gainsight_type else "❌"
            print(f"  {status} {totango_type} → {mapped}")
        
        print("\n🎯 Custom Gainsight Types:")
        custom_mappings = [
            ("In-Person Meeting", "In-Person Meeting"),
            ("Gong Call", "Gong Call"),
            ("Feedback", "Feedback"),
            ("Inbound", "Inbound"),
            ("Slack", "Slack")
        ]
        
        for totango_type, gainsight_type in custom_mappings:
            mapped = transformer.meeting_type_mapping.get(totango_type, "NOT MAPPED")
            status = "✅" if mapped == gainsight_type else "❌"
            print(f"  {status} {totango_type} → {mapped}")
        
        print("\n🔄 Additional Variations:")
        variations = ["video call", "zoom meeting", "teams meeting", "conference call", "demo", "presentation"]
        for variation in variations:
            mapped = transformer.meeting_type_mapping.get(variation, "NOT MAPPED")
            print(f"  • {variation} → {mapped}")
        
        print(f"\n📊 Total mappings defined: {len(transformer.meeting_type_mapping)}")
        
    except Exception as e:
        print(f"❌ Could not load mapping reference: {e}")

def main():
    """Main validation function"""
    try:
        print("🐺" + "="*70)
        print("  WILD WEASEL v5.1 - ENHANCED MEETING TYPE MAPPING VALIDATOR")
        print("="*72)
        
        # Test 1: Meeting type mapping with synthetic test data
        print("\n🧪 PHASE 1: Testing Meeting Type Mapping Logic")
        mapping_success = test_meeting_type_mapping()
        
        # Test 2: Real data transformation
        print("\n🧪 PHASE 2: Testing with Real Totango Data")
        real_data_success = test_real_data_transformation()
        
        # Show mapping reference
        show_mapping_reference()
        
        # Final summary
        print("\n" + "="*72)
        if mapping_success and real_data_success:
            print("🎉 ALL ENHANCED TESTS PASSED!")
            print("✅ Meeting type mapping is working correctly")
            print("✅ Real data transformation is successful")
            print("✅ Your enhanced Wild Weasel v5.1 is ready for migration")
        else:
            print("⚠️ SOME TESTS FAILED")
            if not mapping_success:
                print("❌ Meeting type mapping tests failed")
            if not real_data_success:
                print("❌ Real data transformation tests failed")
            print("🔧 Please review the errors above before running migration")
        
        print("="*72)
        return mapping_success and real_data_success

    except KeyboardInterrupt:
        print("\n🐺 Validation interrupted by user")
        sys.exit(0)
    except Exception as e:
        print(f"\n❌ Validation failed: {e}")
        sys.exit(1)

if __name__ == "__main__":
    success = main()
    if success:
        print("\n🚀 Ready to run Wild Weasel v5.1 Enhanced migration!")
        print("Execute: python3 wild_weasel_agent_v5_1_meeting_enhanced.py")
    else:
        print("\n🔧 Please fix the issues above before running migration")

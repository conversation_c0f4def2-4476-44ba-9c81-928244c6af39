#!/usr/bin/env python3
"""
🐺 Wild Weasel v4.0 - Final API Payload Integration with Embedded Credentials
=============================================================================
Mission: Use properly formatted Gainsight API payloads for seamless migration
"""

import json
import os
import sys
import time
import requests
import logging
from datetime import datetime
from typing import Dict, List, Any, Optional
from playwright.sync_api import sync_playwright, TimeoutError as PlaywrightTimeoutError

class ElementFinder:
    """Basic element finder for V4"""
    
    @staticmethod
    def smart_click(page, target_text, element_type="button"):
        """Simple smart click implementation"""
        try:
            # Try direct text-based selectors
            selectors = [
                f'button:has-text("{target_text}")',
                f'input:has-text("{target_text}")',
                f'a:has-text("{target_text}")'
            ]
            
            for selector in selectors:
                try:
                    if page.locator(selector).count() > 0:
                        page.click(selector)
                        return True
                except:
                    continue
            
            # JavaScript fallback
            js_result = page.evaluate(f"""(targetText) => {{
                const elements = document.querySelectorAll('button, input[type="button"], a');
                for (const el of elements) {{
                    const text = (el.innerText || el.textContent || '').trim();
                    if (text.toLowerCase().includes(targetText.toLowerCase()) && 
                        el.offsetParent !== null && !el.disabled) {{
                        el.click();
                        return true;
                    }}
                }}
                return false;
            }}""", target_text)
            
            return js_result
        except:
            return False

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("/Users/<USER>/Desktop/wild_weasel_gainsight_migration/wild_weasel_v4_final.log"),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger("WildWeasel-v4-Final")

class WildWeaselAgentV4Final:
    """Final Wild Weasel with pre-formatted Gainsight API payloads"""
    
    def __init__(self):
        self.config = {
            "gainsight_url": "https://demo-emea1.gainsightcloud.com",
            "login_url": "https://demo-emea1.gainsightcloud.com/v1/ui/home",
            "target_c360_url": "https://demo-emea1.gainsightcloud.com/v1/ui/customersuccess360?cid=1P02IPMAEL4M3CQGY4K5FHU22D2HHT11KCKU#/8fd79692-8e98-4543-b2ec-bc0dba3776ca",
            "activity_api_url": "https://demo-emea1.gainsightcloud.com/v1/ant/v2/activity",
            "drafts_api_url": "https://demo-emea1.gainsightcloud.com/v1/ant/v2/activity/drafts",
            
            # Source: Pre-formatted Gainsight API payloads
            "formatted_activities_file": "/Users/<USER>/Desktop/wild_weasel_gainsight_migration/gainsight_api_payload_email_activities.json",
            
            # Output files
            "success_log": "/Users/<USER>/Desktop/wild_weasel_gainsight_migration/migration_success_v4.json",
            "failed_log": "/Users/<USER>/Desktop/wild_weasel_gainsight_migration/migration_failed_v4.json"
        }
        
        self.gainsight_activities = []
        self.migration_results = {
            "successful": [],
            "failed": [],
            "total_processed": 0,
            "start_time": None,
            "end_time": None,
            "api_stats": {"successful": 0, "failed": 0},
            "ui_stats": {"successful": 0, "failed": 0}
        }
        
        # Embedded credentials
        self.credentials = {
            "username": "<EMAIL>",
            "password": "@Ramprasad826ie"
        }
        
        self.session_cookies = None
    
    def mission_brief(self):
        """Display the Final Wild Weasel mission brief"""
        print("🐺" + "="*80)
        print("  WILD WEASEL v4.0 - FINAL API PAYLOAD INTEGRATION")
        print("="*82)
        print("🎯 FINAL INTEGRATION FEATURES:")
        print("  ✅ PRE-FORMATTED PAYLOADS:")
        print("     → Direct use of Gainsight API payload format")
        print("     → No data transformation needed")
        print("  ✅ PROPER API WORKFLOW:")
        print("     → Step 1: Create draft via /v2/activity/drafts")
        print("     → Step 2: Create activity with draft ID")
        print("  ✅ SMART UI FALLBACK:")
        print("     → Captures drafts API call automatically when selecting Email")
        print("     → Extracts unique ID from network response")
        print("     → Fills form and clicks 'Log Activity'")
        print("  ✅ ENHANCED AUTOMATION:")
        print("     → Cookie-based API authentication")
        print("     → Multiple selector strategies with JS fallbacks")
        print("="*82)
        print(f"📁 Formatted Activities: {self.config['formatted_activities_file']}")
        print(f"🎯 Target C360: {self.config['target_c360_url']}")
        print(f"👤 Username: {self.credentials['username']}")
        print("="*82)
    
    def load_formatted_activities(self):
        """Load pre-formatted Gainsight API payloads"""
        try:
            if not os.path.exists(self.config['formatted_activities_file']):
                logger.error(f"❌ Formatted activities file not found: {self.config['formatted_activities_file']}")
                return False
            
            with open(self.config['formatted_activities_file'], 'r') as f:
                self.gainsight_activities = json.load(f)
            
            logger.info(f"📊 Loaded {len(self.gainsight_activities)} pre-formatted Gainsight activities")
            
            # Validate structure
            if self.gainsight_activities and len(self.gainsight_activities) > 0:
                sample = self.gainsight_activities[0]
                required_fields = ['note', 'meta', 'author', 'contexts']
                if all(field in sample for field in required_fields):
                    logger.info("✅ Activities are in proper Gainsight API payload format")
                    return True
                else:
                    logger.error("❌ Activities are not in expected Gainsight API format")
                    return False
            else:
                logger.error("❌ No activities found in file")
                return False
                
        except Exception as e:
            logger.error(f"❌ Failed to load formatted activities: {e}")
            return False
    
    def login_to_gainsight(self, page):
        """Enhanced login with cookie extraction"""
        try:
            logger.info("🔐 Logging into Gainsight...")
            
            page.goto(self.config["login_url"])
            page.wait_for_load_state("networkidle", timeout=15000)
            
            # Enhanced credential filling
            username_selectors = [
                "input[name='username']",
                "input[name='email']", 
                "input[type='email']",
                "input[placeholder*='username' i]",
                "input[placeholder*='email' i]",
                ".username-input",
                "#username"
            ]
            
            username_filled = False
            for selector in username_selectors:
                try:
                    if page.locator(selector).count() > 0:
                        page.fill(selector, self.credentials["username"])
                        logger.info(f"✅ Username filled: {selector}")
                        username_filled = True
                        break
                except:
                    continue
            
            if not username_filled:
                logger.error("❌ Could not fill username")
                return False
            
            # Password filling
            password_selectors = [
                "input[name='password']",
                "input[type='password']",
                "input[placeholder*='password' i]",
                ".password-input",
                "#password"
            ]
            
            password_filled = False
            for selector in password_selectors:
                try:
                    if page.locator(selector).count() > 0:
                        page.fill(selector, self.credentials["password"])
                        logger.info(f"✅ Password filled: {selector}")
                        password_filled = True
                        break
                except:
                    continue
            
            if not password_filled:
                logger.error("❌ Could not fill password")
                return False
            
            # Login button clicking
            login_selectors = [
                "button:has-text('Log In')",
                "button:has-text('Login')",
                "button:has-text('Sign In')",
                "button[type='submit']",
                "input[type='submit']",
                ".login-btn"
            ]
            
            login_clicked = False
            for selector in login_selectors:
                try:
                    if page.locator(selector).count() > 0:
                        page.click(selector)
                        logger.info(f"🔘 Login clicked: {selector}")
                        login_clicked = True
                        break
                except:
                    continue
            
            if not login_clicked:
                logger.error("❌ Could not click login button")
                return False
            
            # Wait for successful login
            try:
                page.wait_for_function(
                    "() => window.location.href.includes('/home') || window.location.href.includes('/dashboard') || document.querySelector('.dashboard')",
                    timeout=30000
                )
                
                # Extract session cookies for API calls
                self.extract_session_cookies(page)
                
                logger.info("✅ Successfully logged into Gainsight!")
                return True
                
            except Exception as e:
                logger.error(f"❌ Login timeout or failed: {e}")
                page.screenshot(path="/Users/<USER>/Desktop/wild_weasel_gainsight_migration/login_error_v4.png")
                return False
                
        except Exception as e:
            logger.error(f"❌ Login process failed: {e}")
            return False
    
    def extract_session_cookies(self, page):
        """Extract session cookies for API authentication"""
        try:
            cookies = page.context.cookies()
            cookie_dict = {}
            cookie_string = ""
            
            for cookie in cookies:
                cookie_dict[cookie['name']] = cookie['value']
                cookie_string += f"{cookie['name']}={cookie['value']}; "
            
            self.session_cookies = {
                'dict': cookie_dict,
                'string': cookie_string.rstrip('; ')
            }
            
            logger.info(f"🍪 Extracted {len(cookie_dict)} session cookies for API calls")
            
        except Exception as e:
            logger.warning(f"⚠️ Could not extract cookies: {e}")
            self.session_cookies = None
    
    def navigate_to_c360_timeline(self, page):
        """Enhanced navigation to C360 Timeline"""
        try:
            logger.info("🎯 Navigating to C360 Timeline...")
            
            page.goto(self.config["target_c360_url"])
            page.wait_for_load_state("networkidle", timeout=30000)
            time.sleep(3)
            
            # Enhanced Timeline tab detection
            timeline_selectors = [
                'a:has-text("Timeline")',
                'li:has-text("Timeline")',
                'div[role="tab"]:has-text("Timeline")',
                '.nav-item:has-text("Timeline")',
                '.tab:has-text("Timeline")',
                'button:has-text("Timeline")',
                '[data-tab="timeline"]'
            ]
            
            timeline_found = False
            for selector in timeline_selectors:
                try:
                    timeline_tab = page.locator(selector).first
                    if timeline_tab.count() > 0 and timeline_tab.is_visible():
                        timeline_tab.scroll_into_view_if_needed()
                        time.sleep(1)
                        timeline_tab.click()
                        time.sleep(3)
                        timeline_found = True
                        logger.info(f"📌 Timeline clicked: {selector}")
                        break
                except:
                    continue
            
            # JavaScript fallback
            if not timeline_found:
                logger.info("🔄 Using JavaScript to find Timeline...")
                timeline_clicked = page.evaluate("""() => {
                    const elements = document.querySelectorAll('*');
                    for (const el of elements) {
                        if (el.innerText && el.innerText.trim().toLowerCase() === 'timeline' && el.offsetParent !== null) {
                            el.click();
                            return true;
                        }
                    }
                    return false;
                }""")
                
                if timeline_clicked:
                    logger.info("📌 Timeline clicked via JavaScript")
                    time.sleep(3)
                    timeline_found = True
            
            if timeline_found:
                page.screenshot(path="/Users/<USER>/Desktop/wild_weasel_gainsight_migration/timeline_v4.png")
                logger.info("✅ Successfully navigated to Timeline")
                return True
            else:
                logger.error("❌ Failed to find Timeline tab")
                return False
                
        except Exception as e:
            logger.error(f"❌ Navigation failed: {e}")
            return False
    
    def create_activity_via_api(self, activity_payload):
        """Create activity using proper Gainsight API workflow with pre-formatted payload"""
        try:
            if not self.session_cookies:
                logger.warning("⚠️ No session cookies available for API calls")
                return False
            
            # Extract data from pre-formatted payload
            note = activity_payload.get('note', {})
            subject = note.get('subject', 'Email Activity')
            activity_date = note.get('activityDate', 0)
            content = note.get('content', '')
            contexts = activity_payload.get('contexts', [])
            company_id = contexts[0].get('id') if contexts else None
            
            logger.info(f"🔗 Creating activity via API: {subject[:50]}...")
            
            headers = {
                "Content-Type": "application/json",
                "Cookie": self.session_cookies['string'],
                "User-Agent": "Wild-Weasel-v4-Final/1.0",
                "Accept": "application/json",
                "X-Requested-With": "XMLHttpRequest"
            }
            
            # STEP 1: Create draft
            draft_payload = {
                "type": "EMAIL",
                "subject": subject[:255],
                "companyId": company_id
            }
            
            logger.info("📄 Step 1: Creating draft...")
            
            try:
                draft_response = requests.post(
                    self.config['drafts_api_url'], 
                    json=draft_payload, 
                    headers=headers,
                    timeout=30,
                    verify=True
                )
                
                logger.info(f"Draft response status: {draft_response.status_code}")
                
                if draft_response.status_code not in [200, 201]:
                    logger.error(f"❌ Drafts API failed: {draft_response.status_code} - {draft_response.text}")
                    return False
                
                draft_result = draft_response.json()
                unique_id = draft_result.get('id', '')
                
                if not unique_id:
                    logger.error(f"❌ No unique ID from drafts API: {draft_result}")
                    return False
                
                logger.info(f"✅ Draft created with ID: {unique_id}")
                
            except requests.exceptions.RequestException as e:
                logger.error(f"❌ Network error in drafts API: {e}")
                return False
            
            # STEP 2: Create activity with draft ID
            # Use the pre-formatted payload and add the draft ID
            final_payload = activity_payload.copy()
            final_payload['id'] = unique_id
            
            logger.info("📧 Step 2: Creating activity with draft ID...")
            
            try:
                activity_response = requests.post(
                    self.config['activity_api_url'], 
                    json=final_payload, 
                    headers=headers,
                    timeout=30,
                    verify=True
                )
                
                logger.info(f"Activity response status: {activity_response.status_code}")
                
                if activity_response.status_code in [200, 201]:
                    result = activity_response.json()
                    logger.info(f"✅ Activity created successfully")
                    return True
                else:
                    logger.error(f"❌ Activity API failed: {activity_response.status_code} - {activity_response.text}")
                    return False
                    
            except requests.exceptions.RequestException as e:
                logger.error(f"❌ Network error in activity API: {e}")
                return False
                
        except Exception as e:
            logger.error(f"❌ API creation failed: {e}")
            import traceback
            logger.error(f"Traceback: {traceback.format_exc()}")
            return False
    
    def migrate_activities_api_only(self):
        """API-only migration approach"""
        logger.info("🔄 Starting API-only migration approach...")
        
        successful_count = 0
        failed_count = 0
        
        for i, activity in enumerate(self.gainsight_activities):
            logger.info(f"📝 Processing activity {i+1}/{len(self.gainsight_activities)}")
            
            subject = activity.get('note', {}).get('subject', 'Unknown Subject')
            
            # Try API approach
            try:
                if self.create_activity_via_api(activity):
                    successful_count += 1
                    self.migration_results["api_stats"]["successful"] += 1
                    self.migration_results["successful"].append({
                        "activity": activity,
                        "method": "API",
                        "timestamp": datetime.now().isoformat(),
                        "subject": subject
                    })
                    logger.info(f"✅ API Success: {subject[:50]}")
                else:
                    failed_count += 1
                    self.migration_results["api_stats"]["failed"] += 1
                    self.migration_results["failed"].append({
                        "activity": activity,
                        "error": "API call failed",
                        "timestamp": datetime.now().isoformat(),
                        "subject": subject
                    })
                    logger.error(f"❌ API failed for: {subject[:50]}")
            except Exception as e:
                failed_count += 1
                self.migration_results["api_stats"]["failed"] += 1
                self.migration_results["failed"].append({
                    "activity": activity,
                    "error": f"API exception: {str(e)}",
                    "timestamp": datetime.now().isoformat(),
                    "subject": subject
                })
                logger.error(f"❌ API exception for {subject[:50]}: {e}")
            
            # Delay between activities
            time.sleep(2)
        
        logger.info(f"🔄 Migration complete: {successful_count} successful, {failed_count} failed")
        return successful_count, failed_count
    
    def save_migration_results(self):
        """Save comprehensive migration results"""
        try:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            
            # Save successful migrations
            success_file = self.config["success_log"].replace('.json', f'_{timestamp}.json')
            with open(success_file, 'w') as f:
                json.dump(self.migration_results["successful"], f, indent=2, default=str)
            
            # Save failed migrations
            failed_file = self.config["failed_log"].replace('.json', f'_{timestamp}.json')
            with open(failed_file, 'w') as f:
                json.dump(self.migration_results["failed"], f, indent=2, default=str)
            
            logger.info(f"💾 Results saved: {success_file}, {failed_file}")
            
        except Exception as e:
            logger.error(f"❌ Failed to save results: {e}")
    
    def generate_final_report(self):
        """Generate comprehensive final report"""
        total_activities = len(self.gainsight_activities)
        successful = len(self.migration_results["successful"])
        failed = len(self.migration_results["failed"])
        success_rate = (successful / total_activities * 100) if total_activities > 0 else 0
        
        report = f"""
🐺 WILD WEASEL v4.0 FINAL API INTEGRATION REPORT
{'='*80}
📊 MIGRATION STATISTICS:
  Total Activities: {total_activities}
  ✅ Successful: {successful}
  ❌ Failed: {failed}
  📈 Success Rate: {success_rate:.1f}%

🔧 METHOD BREAKDOWN:
  🔗 API Approach:
     ✅ Successful: {self.migration_results["api_stats"]["successful"]}
     ❌ Failed: {self.migration_results["api_stats"]["failed"]}

📋 DATA PROCESSING:
  Source: Pre-formatted Gainsight API payloads
  Target: Gainsight Timeline Activities
  Company: ICICI Bank
  Method: Direct API calls with authentication

🐺 Wild Weasel v4.0 Final Mission Status: {'✅ COMPLETED SUCCESSFULLY' if failed == 0 else f'⚠️ PARTIALLY COMPLETED ({successful}/{total_activities} successful)'}
{'='*80}
"""
        
        print(report)
        
        # Save report to file
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        report_file = f"/Users/<USER>/Desktop/wild_weasel_gainsight_migration/migration_report_v4_{timestamp}.txt"
        try:
            with open(report_file, 'w') as f:
                f.write(report)
            logger.info(f"📄 Final report saved to: {report_file}")
        except Exception as e:
            logger.warning(f"⚠️ Failed to save report: {e}")
    
    def execute_mission(self):
        """Execute the complete Wild Weasel v4 Final mission"""
        try:
            self.mission_brief()
            
            # Load pre-formatted activities
            if not self.load_formatted_activities():
                logger.error("❌ Mission aborted: Could not load activities")
                return False
            
            self.migration_results["start_time"] = datetime.now()
            self.migration_results["total_processed"] = len(self.gainsight_activities)
            
            # Initialize browser for authentication and cookie extraction
            with sync_playwright() as playwright:
                browser = playwright.chromium.launch(headless=False)
                context = browser.new_context()
                page = context.new_page()
                
                # Login to get session cookies
                if not self.login_to_gainsight(page):
                    logger.error("❌ Mission aborted: Login failed")
                    browser.close()
                    return False
                
                # Keep browser open but start API migration
                self.migrate_activities_api_only()
                
                browser.close()
            
            self.migration_results["end_time"] = datetime.now()
            
            # Save results and generate report
            self.save_migration_results()
            self.generate_final_report()
            
            logger.info("🐺 Wild Weasel v4.0 Final mission completed!")
            return True
            
        except Exception as e:
            logger.error(f"❌ Mission execution failed: {e}")
            return False

def main():
    """Main execution function"""
    try:
        agent = WildWeaselAgentV4Final()
        agent.execute_mission()
        
    except KeyboardInterrupt:
        print("\n🐺 Wild Weasel v4.0 Final mission interrupted by user")
        sys.exit(0)
    except Exception as e:
        print(f"\n❌ Wild Weasel v4.0 Final mission failed: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()

#!/bin/bash

# 🐺 Wild Weasel v5.0 API Enhanced Setup Script
# Sets up the enhanced environment for bulk API migration

echo "🐺 Wild Weasel v5.0 API Enhanced Setup"
echo "====================================="

# Check if we're in the right directory
if [ ! -f "wild_weasel_agent_v5_api_enhanced.py" ]; then
    echo "❌ Error: Please run this script from the wild_weasel_gainsight_migration directory"
    exit 1
fi

echo "📦 Installing enhanced dependencies..."

# Install Python dependencies
pip3 install -r requirements_v5_enhanced.txt

# Install Playwright browsers (if not already installed)
echo "🎭 Setting up Playwright browsers..."
playwright install chromium

# Create necessary directories
echo "📁 Creating output directories..."
mkdir -p logs
mkdir -p results
mkdir -p backups

# Set up environment file if it doesn't exist
if [ ! -f ".env" ]; then
    echo "🔐 Creating environment file..."
    cat > .env << 'EOF'
# Wild Weasel v5.0 API Enhanced Configuration
GAINSIGHT_USERNAME=your_username_here
GAINSIGHT_PASSWORD=your_password_here

# Optional: Advanced configuration
BATCH_SIZE=10
MAX_RETRIES=3
RATE_LIMIT_DELAY=1.0
REQUEST_TIMEOUT=30000

# Logging level (DEBUG, INFO, WARNING, ERROR)
LOG_LEVEL=INFO
EOF
    echo "📝 Please edit .env file with your Gainsight credentials"
fi

# Make the script executable
chmod +x wild_weasel_agent_v5_api_enhanced.py

echo ""
echo "✅ Setup completed successfully!"
echo ""
echo "🚀 Next steps:"
echo "1. Edit .env file with your Gainsight credentials"
echo "2. Ensure your Totango data is at: /Users/<USER>/Desktop/totango/ICICI_processed.json"
echo "3. Run the enhanced migration:"
echo "   python3 wild_weasel_agent_v5_api_enhanced.py"
echo ""
echo "📊 Key features of v5.0:"
echo "• Playwright API context for authenticated bulk requests"
echo "• Intelligent Totango → Gainsight data transformation"
echo "• Parallel batch processing with rate limiting"
echo "• Advanced retry mechanisms and error handling"
echo "• Real-time migration monitoring and reporting"
echo ""
echo "🐺 Happy migrating!"

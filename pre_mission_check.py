#!/usr/bin/env python3
"""
🐺 Wild Weasel Pre-Mission Check
Verify all systems are ready for migration
"""

import json
import os
import sys
from datetime import datetime

def check_dependencies():
    """Check if all required Python packages are installed"""
    print("🔍 Checking dependencies...")
    
    try:
        import playwright
        print("  ✅ Playwright installed")
    except ImportError:
        print("  ❌ Playwright not installed. Run: pip install playwright")
        return False
    
    try:
        import requests
        print("  ✅ Requests library installed")
    except ImportError:
        print("  ❌ Requests not installed. Run: pip install requests")
        return False
    
    try:
        from dotenv import load_dotenv
        print("  ✅ Python-dotenv installed")
    except ImportError:
        print("  ⚠️ Python-dotenv not installed (optional). Run: pip install python-dotenv")
    
    return True

def check_data_files():
    """Check if Totango data files are available"""
    print("\n📁 Checking data files...")
    
    base_path = "/Users/<USER>/Desktop/wild_weasel_gainsight_migration"
    
    # Check extracted activities
    activities_file = os.path.join(base_path, "extracted_email_activities.json")
    if os.path.exists(activities_file):
        try:
            with open(activities_file, 'r') as f:
                activities = json.load(f)
            print(f"  ✅ Email activities file found: {len(activities)} activities")
        except Exception as e:
            print(f"  ❌ Error reading activities file: {e}")
            return False
    else:
        print("  ❌ Email activities file not found. Run: python3 analyze_totango_data.py")
        return False
    
    # Check original Totango files
    totango_path = "/Users/<USER>/Desktop/Totango"
    totango_files = ["ICICI.json", "ID.json", "Touchpoint_reason.JSON"]
    
    for file in totango_files:
        file_path = os.path.join(totango_path, file)
        if os.path.exists(file_path):
            print(f"  ✅ {file} found")
        else:
            print(f"  ⚠️ {file} not found in {totango_path}")
    
    return True

def check_configuration():
    """Check configuration setup"""
    print("\n⚙️ Checking configuration...")
    
    base_path = "/Users/<USER>/Desktop/wild_weasel_gainsight_migration"
    
    # Check .env file
    env_file = os.path.join(base_path, ".env")
    env_template = os.path.join(base_path, ".env.template")
    
    if os.path.exists(env_file):
        print("  ✅ .env configuration file found")
        
        # Check if credentials are set
        with open(env_file, 'r') as f:
            content = f.read()
            if "<EMAIL>" in content:
                print("  ⚠️ Default username found in .env - please update with real credentials")
            else:
                print("  ✅ Custom credentials configured")
    else:
        print("  ⚠️ .env file not found. Copy from .env.template and configure")
        if os.path.exists(env_template):
            print("  ℹ️ Template available: .env.template")
    
    return True

def check_network_connectivity():
    """Check if Gainsight is accessible"""
    print("\n🌐 Checking network connectivity...")
    
    try:
        import requests
        
        # Test Gainsight accessibility
        url = "https://demo-emea1.gainsightcloud.com"
        response = requests.get(url, timeout=10)
        
        if response.status_code == 200:
            print("  ✅ Gainsight demo instance is accessible")
        else:
            print(f"  ⚠️ Gainsight returned status code: {response.status_code}")
        
    except Exception as e:
        print(f"  ❌ Network connectivity issue: {e}")
        return False
    
    return True

def validate_sample_activity():
    """Validate a sample activity structure"""
    print("\n🔬 Validating sample activity structure...")
    
    try:
        activities_file = "/Users/<USER>/Desktop/wild_weasel_gainsight_migration/extracted_email_activities.json"
        with open(activities_file, 'r') as f:
            activities = json.load(f)
        
        if not activities:
            print("  ❌ No activities found in file")
            return False
        
        sample = activities[0]
        required_fields = ['type', 'subject', 'activityDate', 'content', 'author', 'company']
        
        print(f"  📋 Sample activity: {sample['subject'][:50]}...")
        
        for field in required_fields:
            if field in sample:
                print(f"    ✅ {field}: {str(sample[field])[:30]}...")
            else:
                print(f"    ❌ Missing required field: {field}")
                return False
        
        print(f"  ✅ Sample activity structure is valid")
        return True
        
    except Exception as e:
        print(f"  ❌ Error validating sample activity: {e}")
        return False

def show_mission_summary():
    """Show mission summary"""
    print("\n🎯 MISSION SUMMARY")
    print("="*50)
    
    try:
        activities_file = "/Users/<USER>/Desktop/wild_weasel_gainsight_migration/extracted_email_activities.json"
        with open(activities_file, 'r') as f:
            activities = json.load(f)
        
        print(f"📊 Activities to migrate: {len(activities)}")
        
        # Date range
        dates = [datetime.fromisoformat(a['activityDate'].replace('Z', '+00:00')) for a in activities]
        dates.sort()
        
        print(f"📅 Date range: {dates[0].strftime('%Y-%m-%d')} to {dates[-1].strftime('%Y-%m-%d')}")
        
        # Activity types breakdown
        activity_types = {}
        for activity in activities:
            source_type = activity.get('sourceData', {}).get('totango_type', 'unknown')
            activity_types[source_type] = activity_types.get(source_type, 0) + 1
        
        print("📋 Activity types:")
        for atype, count in activity_types.items():
            print(f"  • {atype}: {count}")
        
        # Target information
        print("\n🎯 Target:")
        print("  • Platform: Gainsight Demo EMEA1")
        print("  • Company: ICICI Bank")
        print("  • Activity Type: Email")
        
    except Exception as e:
        print(f"❌ Error loading mission summary: {e}")

def main():
    """Run pre-mission checks"""
    print("🐺 Wild Weasel Pre-Mission Check")
    print("="*40)
    
    checks = [
        check_dependencies,
        check_data_files,
        check_configuration,
        check_network_connectivity,
        validate_sample_activity
    ]
    
    passed = 0
    for check in checks:
        if check():
            passed += 1
    
    print(f"\n📋 CHECK RESULTS: {passed}/{len(checks)} passed")
    
    if passed == len(checks):
        print("✅ All systems ready! Wild Weasel is prepared for mission execution.")
        show_mission_summary()
        print(f"\n🚀 To begin migration, run: python3 wild_weasel_agent.py")
    else:
        print("❌ Some checks failed. Please resolve issues before mission execution.")
        return False
    
    return True

if __name__ == "__main__":
    main()

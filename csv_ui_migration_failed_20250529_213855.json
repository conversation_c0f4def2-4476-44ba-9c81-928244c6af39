[{"row_number": 1, "subject": "ICICI: Mend Platform Access", "activity_type": "Update", "error": "UI exception: Page.screenshot: Target page, context or browser has been closed", "timestamp": "2025-05-29T21:28:01.660249"}, {"row_number": 2, "subject": "ICICI: Mend Platform Access", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:28:03.683911"}, {"row_number": 3, "subject": "ICICI: Mend Platform Access", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:28:05.705120"}, {"row_number": 4, "subject": "ICICI: Mend Platform Access", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:28:07.711168"}, {"row_number": 5, "subject": "ICICI: Mend Platform Access", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:28:09.720268"}, {"row_number": 6, "subject": "ICICI: Mend Platform Access", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:28:11.726457"}, {"row_number": 7, "subject": "ICICI: <PERSON><PERSON><PERSON> to Delighted", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:28:13.738573"}, {"row_number": 8, "subject": "ICICI: NPS Send Date", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:28:15.746081"}, {"row_number": 9, "subject": "ICICI: NPS Last Sent", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:28:17.755292"}, {"row_number": 10, "subject": "ICICI: Mend Platform Access", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:28:19.765036"}, {"row_number": 11, "subject": "ICICI: May Newsletter: AI Risk Insights, Smarter Compliance and Cloud Integration!", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:28:21.772423"}, {"row_number": 12, "subject": "ICICI: Mend Platform Access", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:28:23.781349"}, {"row_number": 13, "subject": "ICICI: Mend Platform Access", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:28:25.804991"}, {"row_number": 14, "subject": "ICICI: MP Activity Frequency", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:28:27.814693"}, {"row_number": 15, "subject": "ICICI: <PERSON> <PERSON><PERSON><PERSON> (7d)", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:28:29.837098"}, {"row_number": 16, "subject": "ICICI: Mend Platform Access", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:28:31.858861"}, {"row_number": 17, "subject": "ICICI: SCA CD Variance Stages", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:28:33.863182"}, {"row_number": 18, "subject": "ICICI: <div><br>Upcoming NPS Survey&nbsp;</div>", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:28:35.880464"}, {"row_number": 19, "subject": "ICICI: Mend Platform Access", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:28:37.896176"}, {"row_number": 20, "subject": "ICICI: MP Activity", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:28:39.917949"}, {"row_number": 21, "subject": "ICICI: MP Activity Frequency", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:28:41.938044"}, {"row_number": 22, "subject": "ICICI: MITRE", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:28:43.951064"}, {"row_number": 23, "subject": "ICICI: MP Abandonment Date", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:28:45.966705"}, {"row_number": 24, "subject": "ICICI: MP Activity", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:28:47.989208"}, {"row_number": 25, "subject": "ICICI: Mend Platform Access", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:28:50.011411"}, {"row_number": 26, "subject": "ICICI: Mend Platform Access", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:28:52.026107"}, {"row_number": 27, "subject": "ICICI: April Newsletter: New Dashboard, Mend AI, and More!", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:28:54.057913"}, {"row_number": 28, "subject": "ICICI: Mend Platform Access", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:28:56.066524"}, {"row_number": 29, "subject": "ICICI: Mend Platform Access", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:28:58.083110"}, {"row_number": 30, "subject": "ICICI: Mend Platform Access", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:29:00.101935"}, {"row_number": 31, "subject": "ICICI: MP Activity Frequency", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:29:02.126108"}, {"row_number": 32, "subject": "ICICI: MP Activity", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:29:04.144058"}, {"row_number": 33, "subject": "ICICI: <PERSON> Newsletter: Introducing Mend AI and More!", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:29:06.167868"}, {"row_number": 34, "subject": "ICICI: MP Abandonment Date", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:29:08.191537"}, {"row_number": 35, "subject": "ICICI: MP Activity", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:29:10.207709"}, {"row_number": 36, "subject": "ICICI: MP Activity Frequency", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:29:12.221626"}, {"row_number": 37, "subject": "ICICI: Mend Platform Access", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:29:14.238972"}, {"row_number": 38, "subject": "ICICI: MP Activity Frequency", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:29:16.260737"}, {"row_number": 39, "subject": "ICICI: <PERSON> <PERSON><PERSON><PERSON> (7d)", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:29:18.283959"}, {"row_number": 40, "subject": "ICICI: Mend Platform Access", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:29:20.304694"}, {"row_number": 41, "subject": "ICICI: Active - CN", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:29:22.325439"}, {"row_number": 42, "subject": "ICICI: Active - SAST", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:29:24.343831"}, {"row_number": 43, "subject": "ICICI: Active - SCA", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:29:26.354905"}, {"row_number": 44, "subject": "ICICI: Mend Platform Access", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:29:28.363991"}, {"row_number": 45, "subject": "ICICI: Invicti Campaign", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:29:30.386228"}, {"row_number": 46, "subject": "ICICI: MP Activity", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:29:32.407574"}, {"row_number": 47, "subject": "ICICI: Mend Platform Access", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:29:34.429186"}, {"row_number": 48, "subject": "ICICI: MP Abandonment Date", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:29:36.448701"}, {"row_number": 49, "subject": "ICICI: MP Activity", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:29:38.469522"}, {"row_number": 50, "subject": "ICICI: MP Activity", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:29:40.489171"}, {"row_number": 51, "subject": "ICICI: February Newsletter: AI-Powered Code Remediation and More!", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:29:42.511802"}, {"row_number": 52, "subject": "ICICI: MP Abandonment Date", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:29:44.528900"}, {"row_number": 53, "subject": "ICICI: MP Activity", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:29:46.551882"}, {"row_number": 54, "subject": "ICICI: Mend Platform Access", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:29:48.570051"}, {"row_number": 55, "subject": "ICICI: MP Activity", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:29:50.585502"}, {"row_number": 56, "subject": "ICICI: Mend Platform Access", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:29:52.602995"}, {"row_number": 57, "subject": "ICICI: MP Abandonment Date", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:29:54.616022"}, {"row_number": 58, "subject": "ICICI: MP Activity", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:29:56.636237"}, {"row_number": 59, "subject": "ICICI: MP Activity", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:29:58.655664"}, {"row_number": 60, "subject": "ICICI: MP Activity Frequency", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:30:00.666770"}, {"row_number": 61, "subject": "ICICI: SAST Planned Downtime_App_copy", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:30:02.689690"}, {"row_number": 62, "subject": "ICICI: Mend Platform Access", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:30:04.707684"}, {"row_number": 63, "subject": "ICICI: MP Abandonment Date", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:30:06.725004"}, {"row_number": 64, "subject": "ICICI: MP Activity", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:30:08.746440"}, {"row_number": 65, "subject": "ICICI: MP Activity", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:30:10.769572"}, {"row_number": 66, "subject": "ICICI: MP Abandonment Date", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:30:12.788477"}, {"row_number": 67, "subject": "ICICI: MP Activity", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:30:14.809613"}, {"row_number": 68, "subject": "ICICI: AI Design Partners", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:30:16.826438"}, {"row_number": 69, "subject": "ICICI: Mend Platform Access", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:30:18.846677"}, {"row_number": 70, "subject": "ICICI: MP Activity", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:30:20.876371"}, {"row_number": 71, "subject": "ICICI: <PERSON> <PERSON><PERSON><PERSON> (14d)", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:30:22.897146"}, {"row_number": 72, "subject": "ICICI: Mend Platform Access", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:30:24.912182"}, {"row_number": 73, "subject": "ICICI: MP Abandonment Date", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:30:26.923668"}, {"row_number": 74, "subject": "ICICI: MP Activity", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:30:28.934589"}, {"row_number": 75, "subject": "ICICI: MP Activity", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:30:30.954201"}, {"row_number": 76, "subject": "ICICI: Mend Platform Access", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:30:32.973760"}, {"row_number": 77, "subject": "ICICI: MP Abandonment Date", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:30:34.991821"}, {"row_number": 78, "subject": "ICICI: MP Activity", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:30:37.002463"}, {"row_number": 79, "subject": "ICICI: MP Activity Frequency", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:30:39.029921"}, {"row_number": 80, "subject": "ICICI: MP Activity", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:30:41.047122"}, {"row_number": 81, "subject": "ICICI: January Newsletter:  Keep Your Code Secure, Exciting Updates from Mend.io!", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:30:43.067757"}, {"row_number": 82, "subject": "ICICI: Mend Platform Access", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:30:45.087691"}, {"row_number": 83, "subject": "ICICI: MP Abandonment Date", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:30:47.107311"}, {"row_number": 84, "subject": "ICICI: MP Activity", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:30:49.122579"}, {"row_number": 85, "subject": "ICICI: MP Activity", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:30:51.138196"}, {"row_number": 86, "subject": "ICICI: Mend Platform Access", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:30:53.150538"}, {"row_number": 87, "subject": "ICICI: MP Activity Frequency", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:30:55.164833"}, {"row_number": 88, "subject": "ICICI: MP Abandonment Date", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:30:57.183958"}, {"row_number": 89, "subject": "ICICI: MP Activity", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:30:59.199719"}, {"row_number": 90, "subject": "ICICI: <PERSON><PERSON><PERSON> to Delighted", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:31:01.220404"}, {"row_number": 91, "subject": "ICICI: <PERSON><PERSON><PERSON> to Delighted", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:31:03.238570"}, {"row_number": 92, "subject": "ICICI: NPS Send Date", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:31:05.262748"}, {"row_number": 93, "subject": "ICICI: NPS Last Sent", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:31:07.279886"}, {"row_number": 94, "subject": "ICICI: NPS Send Date", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:31:09.294385"}, {"row_number": 95, "subject": "ICICI: NPS Last Sent", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:31:11.313544"}, {"row_number": 96, "subject": "ICICI: Mend Platform Access", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:31:13.335733"}, {"row_number": 97, "subject": "ICICI: Mend Platform Access", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:31:15.349636"}, {"row_number": 98, "subject": "ICICI: Risk Status", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:31:17.368871"}, {"row_number": 99, "subject": "ICICI: Risk Next Step(s)", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:31:19.389212"}, {"row_number": 100, "subject": "ICICI: Primary Risk Reason", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:31:21.403034"}, {"row_number": 101, "subject": "ICICI: Mend Platform Access", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:31:23.423250"}, {"row_number": 102, "subject": "ICICI: <div><br>Upcoming NPS Survey&nbsp;</div>", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:31:25.446079"}, {"row_number": 103, "subject": "ICICI: Mend Platform Access", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:31:27.464480"}, {"row_number": 104, "subject": "ICICI: Mend Platform Access", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:31:29.486980"}, {"row_number": 105, "subject": "ICICI: Risk KPI", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:31:31.503500"}, {"row_number": 106, "subject": "ICICI: <PERSON> <PERSON> <PERSON><PERSON><PERSON>", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:31:33.516713"}, {"row_number": 107, "subject": "ICICI: Mend Platform Access", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:31:35.532147"}, {"row_number": 108, "subject": "ICICI: December Newsletter: Sharper Risk Insights & Updates", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:31:37.547294"}, {"row_number": 109, "subject": "ICICI: Mend Platform Access", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:31:39.564925"}, {"row_number": 110, "subject": "ICICI: Solana: MSC Critical Security Event", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:31:41.584226"}, {"row_number": 111, "subject": "ICICI: Mend Platform Access", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:31:43.604932"}, {"row_number": 112, "subject": "ICICI: Mend Platform Access", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:31:45.624391"}, {"row_number": 113, "subject": "ICICI: Mend Platform Access", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:31:47.643329"}, {"row_number": 114, "subject": "ICICI: SAST Planned Downtime_copy", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:31:49.662042"}, {"row_number": 115, "subject": "ICICI: Mend Platform Access", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:31:51.682087"}, {"row_number": 116, "subject": "ICICI: Mend Platform Access", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:31:53.697205"}, {"row_number": 117, "subject": "ICICI: Mend Platform Access", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:31:55.720077"}, {"row_number": 118, "subject": "ICICI: SAST Planned Downtime", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:31:57.740419"}, {"row_number": 119, "subject": "ICICI: Mend Platform Access", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:31:59.759216"}, {"row_number": 120, "subject": "ICICI: <PERSON><PERSON><PERSON> to Delighted", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:32:01.779362"}, {"row_number": 121, "subject": "ICICI: NPS Send Date", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:32:03.798928"}, {"row_number": 122, "subject": "ICICI: NPS Last Sent", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:32:05.818611"}, {"row_number": 123, "subject": "ICICI: Mend Platform Access", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:32:07.836699"}, {"row_number": 124, "subject": "ICICI: November newsletter 2024", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:32:09.858682"}, {"row_number": 125, "subject": "ICICI: Mend Platform Access", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:32:11.884643"}, {"row_number": 126, "subject": "ICICI: <PERSON> <PERSON><PERSON><PERSON> (7d)", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:32:13.903126"}, {"row_number": 127, "subject": "ICICI: Mend Platform Access", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:32:15.922109"}, {"row_number": 128, "subject": "ICICI: <div><br>Upcoming NPS Survey&nbsp;</div>", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:32:17.944560"}, {"row_number": 129, "subject": "ICICI: October newsletter 2024", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:32:19.963079"}, {"row_number": 130, "subject": "ICICI: Risk Status", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:32:21.983857"}, {"row_number": 131, "subject": "ICICI: Primary Risk Reason", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:32:24.007816"}, {"row_number": 132, "subject": "ICICI: Risk Next Step(s)", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:32:26.027295"}, {"row_number": 133, "subject": "ICICI: Risk Status", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:32:28.047211"}, {"row_number": 134, "subject": "ICICI: Primary Risk Reason", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:32:30.064957"}, {"row_number": 135, "subject": "ICICI: Risk Next Step(s)", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:32:32.084753"}, {"row_number": 136, "subject": "ICICI: Support: IP Address Change", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:32:34.105913"}, {"row_number": 137, "subject": "ICICI: Business Model Launch", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:32:36.123779"}, {"row_number": 138, "subject": "ICICI: New Business Model_Webinar Follow up", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:32:38.144130"}, {"row_number": 139, "subject": "ICICI: MP Activity Frequency", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:32:40.166070"}, {"row_number": 140, "subject": "ICICI: <PERSON><PERSON>", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:32:42.179350"}, {"row_number": 141, "subject": "ICICI: New Business Model", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:32:44.231987"}, {"row_number": 142, "subject": "ICICI: Slack to #risk", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:32:46.257706"}, {"row_number": 143, "subject": "ICICI: Risk Next Step(s)", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:32:48.275441"}, {"row_number": 144, "subject": "ICICI: Primary Risk Reason", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:32:50.284368"}, {"row_number": 145, "subject": "ICICI: Risk Status", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:32:52.291347"}, {"row_number": 146, "subject": "ICICI: <PERSON><PERSON>", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:32:54.302680"}, {"row_number": 147, "subject": "ICICI: MP Activity Frequency", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:32:56.307741"}, {"row_number": 148, "subject": "ICICI: MP Activity Frequency", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:32:58.313940"}, {"row_number": 149, "subject": "ICICI: NPS Send Date", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:33:00.322651"}, {"row_number": 150, "subject": "ICICI: MP Activity Frequency", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:33:02.351450"}, {"row_number": 151, "subject": "ICICI: MP Activity", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:33:04.359113"}, {"row_number": 152, "subject": "ICICI: MP Activity Status", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:33:06.364823"}, {"row_number": 153, "subject": "ICICI: <PERSON><PERSON>", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:33:08.375234"}, {"row_number": 154, "subject": "ICICI: MP Activity Status", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:33:10.384784"}, {"row_number": 155, "subject": "ICICI: Product Roadmap H2 2024", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:33:12.391544"}, {"row_number": 156, "subject": "ICICI: <PERSON><PERSON><PERSON> to Delighted", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:33:14.400953"}, {"row_number": 157, "subject": "ICICI: NPS Last Sent", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:33:16.410084"}, {"row_number": 158, "subject": "ICICI: NPS Send Date", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:33:18.419780"}, {"row_number": 159, "subject": "ICICI: NPS Last Sent", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:33:20.429282"}, {"row_number": 160, "subject": "ICICI: NPS Send Date", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:33:22.436156"}, {"row_number": 161, "subject": "ICICI: <PERSON><PERSON><PERSON> to Delighted", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:33:24.446297"}, {"row_number": 162, "subject": "ICICI: Vulnerability Insights with MITRE Data", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:33:26.454392"}, {"row_number": 163, "subject": "ICICI: <PERSON><PERSON>", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:33:28.464066"}, {"row_number": 164, "subject": "ICICI: Mend Platform Access", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:33:30.473840"}, {"row_number": 165, "subject": "ICICI: CSM Managed", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:33:32.483111"}, {"row_number": 166, "subject": "ICICI: <div><br>Upcoming NPS Survey&nbsp;</div>", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:33:34.493108"}, {"row_number": 167, "subject": "ICICI: <PERSON><PERSON>", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:33:36.508418"}, {"row_number": 168, "subject": "ICICI: <PERSON><PERSON>", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:33:38.529129"}, {"row_number": 169, "subject": "ICICI: <PERSON><PERSON>", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:33:40.549111"}, {"row_number": 170, "subject": "ICICI: <PERSON><PERSON>", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:33:42.568277"}, {"row_number": 171, "subject": "ICICI: <PERSON> Follow Up Email", "activity_type": "Meeting", "error": "UI automation failed", "timestamp": "2025-05-29T21:33:44.582317"}, {"row_number": 172, "subject": "ICICI: <PERSON> Follow Up Email", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:33:46.597938"}, {"row_number": 173, "subject": "ICICI: <PERSON> Follow Up Email", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:33:48.613504"}, {"row_number": 174, "subject": "ICICI: Slack to #risk", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:33:50.619335"}, {"row_number": 175, "subject": "ICICI: <PERSON><PERSON>", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:33:52.629146"}, {"row_number": 176, "subject": "ICICI: <PERSON><PERSON><PERSON> - CSM Satisfaction Survey/Kelle Intro", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:33:54.638976"}, {"row_number": 177, "subject": "ICICI: <div><span class=\"mention-wrapper\" data-reactroot=\"\"><span class=\"field\">ICICI Bank</span>'s renewal date has passed</span></div>", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:33:56.648507"}, {"row_number": 178, "subject": "ICICI: Correction - CVE - 2024 - 3094_copy", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:33:58.658158"}, {"row_number": 179, "subject": "ICICI: <PERSON><PERSON>", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:34:00.665298"}, {"row_number": 180, "subject": "ICICI: All Other Customers - CVE - 2024 - 3094", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:34:02.674926"}, {"row_number": 181, "subject": "ICICI: <PERSON><PERSON>", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:34:04.680734"}, {"row_number": 182, "subject": "ICICI: License Utilization Status", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:34:06.691300"}, {"row_number": 183, "subject": "ICICI: License Utilization Status", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:34:08.700333"}, {"row_number": 184, "subject": "ICICI: <PERSON><PERSON>", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:34:10.710568"}, {"row_number": 185, "subject": "ICICI: <PERSON><PERSON>", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:34:12.720345"}, {"row_number": 186, "subject": "ICICI: <PERSON><PERSON>", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:34:14.729643"}, {"row_number": 187, "subject": "ICICI: <PERSON><PERSON>", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:34:16.734298"}, {"row_number": 188, "subject": "ICICI: Executive Engaged", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:34:18.744153"}, {"row_number": 189, "subject": "ICICI: AI Survey - Gold/ Platinum Customers - Follow Up", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:34:20.753139"}, {"row_number": 190, "subject": "ICICI: <PERSON><PERSON>", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:34:22.762358"}, {"row_number": 191, "subject": "ICICI: <PERSON><PERSON>", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:34:24.767673"}, {"row_number": 192, "subject": "ICICI: AI Survey - Int. Gold & Platinum Customers Oops_copy", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:34:26.777975"}, {"row_number": 193, "subject": "ICICI: AI Survey - Gold & Platinum Customers", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:34:28.798240"}, {"row_number": 194, "subject": "ICICI: <PERSON><PERSON>", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:34:30.807755"}, {"row_number": 195, "subject": "ICICI: <PERSON><PERSON>", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:34:32.817431"}, {"row_number": 196, "subject": "ICICI: <PERSON><PERSON>", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:34:34.826688"}, {"row_number": 197, "subject": "ICICI: <PERSON><PERSON>", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:34:36.834977"}, {"row_number": 198, "subject": "ICICI: Slack to #risk", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:34:38.844258"}, {"row_number": 199, "subject": "ICICI: Risk Update", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:34:40.852363"}, {"row_number": 200, "subject": "ICICI: Risk Status", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:34:42.862674"}, {"row_number": 201, "subject": "ICICI: <PERSON><PERSON>", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:34:44.870947"}, {"row_number": 202, "subject": "ICICI: <PERSON><PERSON>", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:34:46.880725"}, {"row_number": 203, "subject": "ICICI: NPS Send Date", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:34:48.887734"}, {"row_number": 204, "subject": "ICICI: NPS Informed", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:34:50.896607"}, {"row_number": 205, "subject": "ICICI: NPS Informed", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:34:52.907297"}, {"row_number": 206, "subject": "ICICI: Touch Status 2", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:34:54.917096"}, {"row_number": 207, "subject": "ICICI: <PERSON><PERSON>", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:34:56.926203"}, {"row_number": 208, "subject": "ICICI: <PERSON><PERSON>", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:34:58.936516"}, {"row_number": 209, "subject": "ICICI: <PERSON><PERSON>", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:35:00.950296"}, {"row_number": 210, "subject": "ICICI: <PERSON><PERSON>", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:35:02.958904"}, {"row_number": 211, "subject": "ICICI: <PERSON><PERSON>", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:35:04.969373"}, {"row_number": 212, "subject": "ICICI: <PERSON><PERSON>", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:35:06.979238"}, {"row_number": 213, "subject": "ICICI: Slack to #risk", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:35:08.988821"}, {"row_number": 214, "subject": "ICICI: Risk Update", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:35:10.998099"}, {"row_number": 215, "subject": "ICICI: Risk Status", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:35:13.007948"}, {"row_number": 216, "subject": "ICICI: <PERSON><PERSON>", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:35:15.016279"}, {"row_number": 217, "subject": "ICICI: <PERSON><PERSON>", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:35:17.026098"}, {"row_number": 218, "subject": "ICICI: <PERSON><PERSON>", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:35:19.036062"}, {"row_number": 219, "subject": "ICICI: <PERSON><PERSON>", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:35:21.045421"}, {"row_number": 220, "subject": "ICICI: <PERSON><PERSON>", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:35:23.052377"}, {"row_number": 221, "subject": "ICICI: <PERSON><PERSON>", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:35:25.062500"}, {"row_number": 222, "subject": "ICICI: <PERSON><PERSON>", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:35:27.072097"}, {"row_number": 223, "subject": "ICICI: <PERSON><PERSON>", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:35:29.081672"}, {"row_number": 224, "subject": "ICICI: <PERSON><PERSON>", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:35:31.091100"}, {"row_number": 225, "subject": "ICICI: <PERSON><PERSON>", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:35:33.100540"}, {"row_number": 226, "subject": "ICICI: <PERSON><PERSON>", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:35:35.110376"}, {"row_number": 227, "subject": "ICICI: ICICI Bank <> Mend - Catch Up Meeting External", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:35:37.119569"}, {"row_number": 228, "subject": "ICICI: <PERSON><PERSON>", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:35:39.136120"}, {"row_number": 229, "subject": "ICICI: SCA CD Variance Stages", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:35:41.145839"}, {"row_number": 230, "subject": "ICICI: License Utilization Status", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:35:43.155881"}, {"row_number": 231, "subject": "ICICI: Mend Vulnerability found by WithSecure", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:35:45.165222"}, {"row_number": 232, "subject": "ICICI: ICICI Bank <> Mend - Catch Up Meeting", "activity_type": "Meeting", "error": "UI automation failed", "timestamp": "2025-05-29T21:35:47.175456"}, {"row_number": 233, "subject": "ICICI: ICICI Bank<>Mend.io organization migration from LBA to VBA", "activity_type": "Meeting", "error": "UI automation failed", "timestamp": "2025-05-29T21:35:49.185760"}, {"row_number": 234, "subject": "ICICI: <PERSON><PERSON>", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:35:51.195443"}, {"row_number": 235, "subject": "ICICI: No need to touch base", "activity_type": "Meeting", "error": "UI automation failed", "timestamp": "2025-05-29T21:35:53.209026"}, {"row_number": 236, "subject": "ICICI: Unified Agent Hotfix now available-due to Java upgrade issue", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:35:55.219405"}, {"row_number": 237, "subject": "ICICI: Unified Agent - Java upgrade issue", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:35:57.229312"}, {"row_number": 238, "subject": "ICICI: LBA email change sent to the partner", "activity_type": "Meeting", "error": "UI automation failed", "timestamp": "2025-05-29T21:35:59.236811"}, {"row_number": 239, "subject": "ICICI: ICICI Bank<>MEND", "activity_type": "Meeting", "error": "UI automation failed", "timestamp": "2025-05-29T21:36:01.242790"}, {"row_number": 240, "subject": "ICICI: <PERSON><PERSON>", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:36:03.252069"}, {"row_number": 241, "subject": "ICICI: <PERSON><PERSON>", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:36:05.262394"}, {"row_number": 242, "subject": "ICICI: Request for Developer Training for ICICI", "activity_type": "Meeting", "error": "UI automation failed", "timestamp": "2025-05-29T21:36:07.272215"}, {"row_number": 243, "subject": "ICICI: Re: Queries on Licensing Policy related details - ICICI Bank", "activity_type": "Email", "error": "UI automation failed", "timestamp": "2025-05-29T21:36:09.282391"}, {"row_number": 244, "subject": "ICICI: Re: Mend integration with LDAP and SAML - ICICI", "activity_type": "Email", "error": "UI automation failed", "timestamp": "2025-05-29T21:36:11.297062"}, {"row_number": 245, "subject": "ICICI: <PERSON><PERSON>", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:36:13.305867"}, {"row_number": 246, "subject": "ICICI: NPS Send Date", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:36:15.315119"}, {"row_number": 247, "subject": "ICICI: NPS Informed", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:36:17.331612"}, {"row_number": 248, "subject": "ICICI: <PERSON><PERSON>", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:36:19.338147"}, {"row_number": 249, "subject": "ICICI: <PERSON><PERSON>", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:36:21.347896"}, {"row_number": 250, "subject": "ICICI: No need to touch base- working with the partner", "activity_type": "Meeting", "error": "UI automation failed", "timestamp": "2025-05-29T21:36:23.366776"}, {"row_number": 251, "subject": "ICICI: Inform Key Contacts of upcoming NPS survey", "activity_type": "Meeting", "error": "UI automation failed", "timestamp": "2025-05-29T21:36:25.376295"}, {"row_number": 252, "subject": "ICICI: <PERSON> is working to arrange a meeting with the bank and partner", "activity_type": "Meeting", "error": "UI automation failed", "timestamp": "2025-05-29T21:36:27.387017"}, {"row_number": 253, "subject": "ICICI: <PERSON><PERSON>", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:36:29.399208"}, {"row_number": 254, "subject": "ICICI: <PERSON><PERSON>", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:36:31.409846"}, {"row_number": 255, "subject": "ICICI: Discussion with ICICI Team - Mend - WhiteSource !!", "activity_type": "Meeting", "error": "UI automation failed", "timestamp": "2025-05-29T21:36:33.430478"}, {"row_number": 256, "subject": "ICICI: <PERSON><PERSON>", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:36:35.451163"}, {"row_number": 257, "subject": "ICICI: Update from Luis- partner manager", "activity_type": "Meeting", "error": "UI automation failed", "timestamp": "2025-05-29T21:36:37.466253"}, {"row_number": 258, "subject": "ICICI: <PERSON><PERSON>", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:36:39.486946"}, {"row_number": 259, "subject": "ICICI: Repo Integration-Accelerate your remediation", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:36:41.508694"}, {"row_number": 260, "subject": "ICICI: RSA 2023 conference", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:36:43.528304"}, {"row_number": 261, "subject": "ICICI: <PERSON><PERSON>", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:36:45.556664"}, {"row_number": 262, "subject": "ICICI: FEE ticket in place", "activity_type": "Meeting", "error": "UI automation failed", "timestamp": "2025-05-29T21:36:47.573926"}, {"row_number": 263, "subject": "ICICI: FEE ticket in place", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:36:49.585446"}, {"row_number": 264, "subject": "ICICI: FEE ticket in place", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:36:51.605708"}, {"row_number": 265, "subject": "ICICI: Discussion with ICICI Team - Mend - WhiteSource !!", "activity_type": "Meeting", "error": "UI automation failed", "timestamp": "2025-05-29T21:36:53.624312"}, {"row_number": 266, "subject": "ICICI: ICICI/Meteonic - Next Steps & Alignment", "activity_type": "Meeting", "error": "UI automation failed", "timestamp": "2025-05-29T21:36:55.643196"}, {"row_number": 267, "subject": "ICICI: Sunny to set up a joint meeting?", "activity_type": "Meeting", "error": "UI automation failed", "timestamp": "2025-05-29T21:36:57.661893"}, {"row_number": 268, "subject": "ICICI: Sunny to set up a joint meeting?", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:36:59.679129"}, {"row_number": 269, "subject": "ICICI: <PERSON><PERSON>", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:37:01.697292"}, {"row_number": 270, "subject": "ICICI: Sales Manager Region", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:37:03.714956"}, {"row_number": 271, "subject": "ICICI: SCA CD Variance Stages", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:37:05.731866"}, {"row_number": 272, "subject": "ICICI: <PERSON><PERSON>", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:37:07.750762"}, {"row_number": 273, "subject": "ICICI: Sunny to set up a joint meeting?", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:37:09.771198"}, {"row_number": 274, "subject": "ICICI: ********- zoom discussions in place", "activity_type": "Meeting", "error": "UI automation failed", "timestamp": "2025-05-29T21:37:11.790730"}, {"row_number": 275, "subject": "ICICI: Mend/Meteonic sync", "activity_type": "Meeting", "error": "UI automation failed", "timestamp": "2025-05-29T21:37:13.808806"}, {"row_number": 276, "subject": "ICICI: Case#********- call with Eng", "activity_type": "Meeting", "error": "UI automation failed", "timestamp": "2025-05-29T21:37:15.830523"}, {"row_number": 277, "subject": "ICICI: Working to set a meting with a the bank and the BP", "activity_type": "Meeting", "error": "UI automation failed", "timestamp": "2025-05-29T21:37:17.849460"}, {"row_number": 278, "subject": "ICICI: Meteonic will do onsite in the bank on Jan25th", "activity_type": "Meeting", "error": "UI automation failed", "timestamp": "2025-05-29T21:37:19.869278"}, {"row_number": 279, "subject": "ICICI: Mend's Malicious Package Communications", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:37:21.887902"}, {"row_number": 280, "subject": "ICICI: No need to touch base", "activity_type": "Meeting", "error": "UI automation failed", "timestamp": "2025-05-29T21:37:23.904955"}, {"row_number": 281, "subject": "ICICI: NPS Send Date", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:37:25.920808"}, {"row_number": 282, "subject": "ICICI: NPS Informed", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:37:27.938272"}, {"row_number": 283, "subject": "ICICI: NPS Informed", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:37:29.953811"}, {"row_number": 284, "subject": "ICICI: Internal sync with AM", "activity_type": "Meeting", "error": "UI automation failed", "timestamp": "2025-05-29T21:37:31.968551"}, {"row_number": 285, "subject": "ICICI: Touch Status", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:37:33.990445"}, {"row_number": 286, "subject": "ICICI: NPS Informed", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:37:36.009493"}, {"row_number": 287, "subject": "ICICI: Sales Manager Region", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:37:38.031322"}, {"row_number": 288, "subject": "ICICI: Team Manager Region", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:37:40.052643"}, {"row_number": 289, "subject": "ICICI: Team Manager", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:37:42.069109"}, {"row_number": 290, "subject": "ICICI: SCA CD Variance Stages", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:37:44.101905"}, {"row_number": 291, "subject": "ICICI: Setup a call with customers and explains our Policies best practice", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:37:46.125784"}, {"row_number": 292, "subject": "ICICI: Touch Status", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:37:48.140089"}, {"row_number": 293, "subject": "ICICI: Setup a call with customers and explains our Policies best practice", "activity_type": "Email", "error": "UI automation failed", "timestamp": "2025-05-29T21:37:50.159111"}, {"row_number": 294, "subject": "ICICI: Setup a call with customers and explains our Policies best practice", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:37:52.176797"}, {"row_number": 295, "subject": "ICICI: Outage in app.whitesourcesoftware.com", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:37:54.197529"}, {"row_number": 296, "subject": "ICICI: Touch Status", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:37:56.214206"}, {"row_number": 297, "subject": "ICICI: Check the usage and project number", "activity_type": "Meeting", "error": "UI automation failed", "timestamp": "2025-05-29T21:37:58.233543"}, {"row_number": 298, "subject": "ICICI: CSAT - Spring4Shell, Platinum&Gold", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:38:00.249352"}, {"row_number": 299, "subject": "ICICI: Touch Status", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:38:02.265797"}, {"row_number": 300, "subject": "ICICI: Team Manager Region", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:38:04.279603"}, {"row_number": 301, "subject": "ICICI: Spring4Shell HIGH SEVERITY Vulnerability in Spring core - Gold & Platinum", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:38:06.298882"}, {"row_number": 302, "subject": "ICICI: <PERSON><PERSON>", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:38:08.320180"}, {"row_number": 303, "subject": "ICICI: Touch Status", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:38:10.343253"}, {"row_number": 304, "subject": "ICICI: VBA migration", "activity_type": "Meeting", "error": "UI automation failed", "timestamp": "2025-05-29T21:38:12.365737"}, {"row_number": 305, "subject": "ICICI: Plz check with the usage is low", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:38:14.376618"}, {"row_number": 306, "subject": "ICICI: March 2022 Newsletter- Dedicated", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:38:16.398852"}, {"row_number": 307, "subject": "ICICI: <PERSON><PERSON>", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:38:18.418243"}, {"row_number": 308, "subject": "ICICI: BP customer- no touch base is needed", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:38:20.440082"}, {"row_number": 309, "subject": "ICICI: Touch Status", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:38:22.462507"}, {"row_number": 310, "subject": "ICICI: Customer Communication Regarding LBA to VBA Migration - SCA-ICICI Bank - for SCA-ICICI Bank", "activity_type": "Email", "error": "UI automation failed", "timestamp": "2025-05-29T21:38:24.485617"}, {"row_number": 311, "subject": "ICICI: Team Manager", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:38:26.501423"}, {"row_number": 312, "subject": "ICICI: <PERSON><PERSON>", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:38:28.522171"}, {"row_number": 313, "subject": "ICICI: Log4j Vulnerability webinar Dedicated CSM", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:38:30.538898"}, {"row_number": 314, "subject": "ICICI: Vulnerability in Apache Log4j2 library Part 2  Dedicated CSM Accounts", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:38:32.554221"}, {"row_number": 315, "subject": "ICICI: Severity Vulnerability (CVE-2021-44228) in the Log4j2 -Dedicated CSM", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:38:34.575555"}, {"row_number": 316, "subject": "ICICI: Customer Communication Regarding LBA to VBA Migration - SCA-ICICI Bank", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:38:36.581091"}, {"row_number": 317, "subject": "ICICI: Contact SCA-ICICI Bank about moving to VBA", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:38:38.599788"}, {"row_number": 318, "subject": "ICICI: <PERSON><PERSON>", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:38:40.617260"}, {"row_number": 319, "subject": "ICICI: SCA CD Variance Stages", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:38:42.635826"}, {"row_number": 320, "subject": "ICICI: Partner account", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:38:44.652781"}, {"row_number": 321, "subject": "ICICI: Sales Manager Region", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:38:46.671931"}, {"row_number": 322, "subject": "ICICI: <PERSON><PERSON>", "activity_type": "Update", "error": "UI automation failed", "timestamp": "2025-05-29T21:38:48.691757"}]